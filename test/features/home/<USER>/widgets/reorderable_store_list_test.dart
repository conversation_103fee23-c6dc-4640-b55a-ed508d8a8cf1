import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/storage/storage_service.dart';
import 'package:storetrack_app/core/services/device_info_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';

// Mock DeviceInfoService for testing
class MockDeviceInfoService implements DeviceInfoService {
  @override
  Future<String> getDeviceId() async {
    return 'test_device_id';
  }

  @override
  Future<String> getDeviceModel() {
    // TODO: implement getDeviceModel
    throw UnimplementedError();
  }

  @override
  Future<String> getDeviceName() {
    // TODO: implement getDeviceName
    throw UnimplementedError();
  }

  @override
  String getDevicePlatform() {
    // TODO: implement getDevicePlatform
    throw UnimplementedError();
  }

  @override
  Future<String> getDeviceVersion() {
    // TODO: implement getDeviceVersion
    throw UnimplementedError();
  }
}

void main() {
  group('ReorderableStoreList Widget Tests', () {
    late DataManager dataManager;

    setUpAll(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      final sharedPrefs = await SharedPreferences.getInstance();

      // Create the storage service and data manager
      final storageService = StorageServiceImpl(sharedPrefs);
      final deviceInfoService = MockDeviceInfoService();
      dataManager = DataManagerImpl(storageService, deviceInfoService);

      // Register in service locator for widget testing
      if (!sl.isRegistered<DataManager>()) {
        sl.registerSingleton<DataManager>(dataManager);
      }
    });

    tearDownAll(() async {
      // Clean up service locator
      if (sl.isRegistered<DataManager>()) {
        await sl.unregister<DataManager>();
      }
    });

    testWidgets(
        'ReorderableStoreList should build without errors with empty tasks',
        (WidgetTester tester) async {
      // Build the widget with empty tasks
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ReorderableStoreList(
              tasks: [],
              isCalendarMode: false,
            ),
          ),
        ),
      );

      // Verify that the widget builds and shows empty state
      expect(find.text('No unscheduled tasks available'), findsOneWidget);
    });

    testWidgets(
        'ReorderableStoreList should build without errors with sample tasks',
        (WidgetTester tester) async {
      // Create sample tasks
      final sampleTasks = [
        TaskDetail(
          taskId: 1,
          storeName: 'Store A',
          location: 'Location A',
          storeId: 1,
        ),
        TaskDetail(
          taskId: 2,
          storeName: 'Store B',
          location: 'Location B',
          storeId: 2,
        ),
      ];

      // Build the widget with sample tasks
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ReorderableStoreList(
              tasks: sampleTasks,
              isCalendarMode: false,
            ),
          ),
        ),
      );

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Verify that the widget builds successfully
      expect(find.byType(ReorderableStoreList), findsOneWidget);
    });

    test('DataManager task order methods work correctly', () async {
      // Test saving and retrieving task order
      final taskOrder = ['task_1', 'task_2', 'task_3'];

      await dataManager.saveTaskOrder(taskOrder);
      final retrievedOrder = await dataManager.getTaskOrder();

      expect(retrievedOrder, equals(taskOrder));
    });
  });
}
