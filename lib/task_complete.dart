// first prepare data
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/features/home/<USER>/models/profile_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

var realm = RealmDatabase.instance.realm;
ProfileModel? userProfile =
    realm.query<ProfileModel>('userId == \$0', ["userId"]).firstOrNull;
var userOrgsId = userProfile?.orgIDs;
var task = TaskDetail();

class Dummy {
  // then prepare ui
  void checkTaskStatus() {
    if (task.isOpen == true) {
      // scheduled date == today
    } else {
      // scheduled date == task.scheduledTimeStamp
    }

    if (task.sentToPayroll == true) {
      // show sent to payroll confirmdialog
    }
  }

  submitTask() {}

  bool checkNonMPTPhotosValidate(TaskDetail taskDetailEntity) {
    bool nonMPTTaskPhotosValidated = true;

    for (PhotoFolder photoFolderEntity in taskDetailEntity.photoFolder ?? []) {
      int folderContentAmount = 0;

      for (Photo photoEntity in photoFolderEntity.photos ?? []) {
        if (photoEntity.userDeletedPhoto != true) {
          folderContentAmount++;
        }
      }

      if ((photoFolderEntity.attribute == true) && (folderContentAmount == 0)) {
        nonMPTTaskPhotosValidated = false;
      }
    }

    return nonMPTTaskPhotosValidated;
  }

  bool checkFormsValidated(TaskDetail taskDetailEntity) {
    int taskTotalFormsCount = 0;
    bool taskFormsValidated = false;
    int totalMandatoryFormCount = 0;
    int totalMandatoryFormCompleteCount = 0;

    if (taskDetailEntity.forms != null) {
      for (Form formEntity in taskDetailEntity.forms!) {
        if (formEntity.isVisionForm != true) {
          taskTotalFormsCount++;

          if (formEntity.isMandatory == true) {
            totalMandatoryFormCount++;
          }

          if ((formEntity.formCompleted == true) &&
              (formEntity.questionAnswers != null) &&
              (formEntity.questionAnswers!.isNotEmpty)) {
            if (formEntity.isMandatory == true) {
              totalMandatoryFormCompleteCount++;
            }
          }
        }
      }
    }

    if ((taskDetailEntity.forms == null) || (taskTotalFormsCount == 0)) {
      taskFormsValidated = true;
    } else {
      if (totalMandatoryFormCount == totalMandatoryFormCompleteCount) {
        taskFormsValidated = true;
      }
    }

    return taskFormsValidated;
  }

  bool checkAllFormsCompleted(TaskDetail taskDetailEntity) {
    int taskTotalFormsCount = 0;
    int totalCompletedFormsCount = 0;

    if (taskDetailEntity.forms != null) {
      for (Form formEntity in taskDetailEntity.forms!) {
        if (formEntity.isVisionForm != true) {
          taskTotalFormsCount++;

          if ((formEntity.formCompleted == true) &&
              (formEntity.questionAnswers != null) &&
              (formEntity.questionAnswers!.isNotEmpty)) {
            totalCompletedFormsCount++;
          }
        }
      }
    }

    if ((taskDetailEntity.forms == null) || (taskTotalFormsCount == 0)) {
      return true;
    } else {
      return taskTotalFormsCount == totalCompletedFormsCount;
    }
  }

  bool arePhotosOk(TaskDetail taskDetailEntity) {
    if (!checkNonMPTPhotosValidate(taskDetailEntity) &&
        !_hasMPT(taskDetailEntity)) {
      logger(
          "You have to upload all expected photos in order to save this page.");
      return false;
    } else {
      return true;
    }
  }

  bool _hasMPT(TaskDetail taskDetailEntity) {
    if (taskDetailEntity.forms != null) {
      for (Form formEntity in taskDetailEntity.forms!) {
        if (formEntity.isVisionForm == true) {
          return true;
        }
      }
    }
    return false;
  }

  void timeChecking(
      TaskDetail taskDetailEntity, int taskDuration, bool isSuccessful) {
    String warningMessage = "";
    bool taskFormsValidated = checkFormsValidated(taskDetailEntity);
    bool commentRequired = false;
    bool isHoopEmployee = false;

    final budgetResult =
        FormUtils.calculateBudget(taskDetailEntity.taskId!.toInt());
    final taskBudgetCalculated = budgetResult?.tempBudget ?? 0;

    if (taskDuration == 0 && taskBudgetCalculated > 0) {
      warningMessage =
          "You entered 0 working hours/mins so you will not be paid for this task.";
    } else if (taskDuration > taskBudgetCalculated) {
      if (userOrgsId != null) {
        bool userFallsUnder6or1 =
            userOrgsId!.contains("1") || userOrgsId!.contains("6");
        if (userFallsUnder6or1 && (taskDetailEntity.comment?.isEmpty ?? true)) {
          warningMessage =
              "Please mention the reason for going over budget in schedule comment section.";
          commentRequired = true;
        } else if (userOrgsId!.contains("8")) {
          warningMessage = "Reporting over budget is forbidden.";
          isHoopEmployee = true;
        }
      } else {
        warningMessage =
            "Your claimed time exceeds the budgeted time for this visit.";
      }
    }

    if (!taskFormsValidated &&
        (taskDetailEntity.comment?.isNotEmpty ?? false)) {
      if (warningMessage.isEmpty) {
        warningMessage =
            "If you press OK, the schedule will be marked as unsuccessful. Press cancel if you wish to continue working on this schedule.";
      } else {
        warningMessage =
            "1. $warningMessage\n\n2. If you press OK, the schedule will be marked as unsuccessful. Press cancel if you wish to continue working on this schedule.";
      }
    }

    if (warningMessage.isNotEmpty) {
      logger(warningMessage);

      if (!commentRequired && !isHoopEmployee) {
        performTaskSubmission(isSuccessful);
      } else {
        logger("Task submission blocked due to validation requirements");
      }
    } else {
      performTaskSubmission(isSuccessful);
    }
  }

  void performTaskSubmission(bool isSuccessful) {
    logger("Performing task submission with success status: $isSuccessful");

    // Process realm data update
    realm.write(() {
      // Find the task model to update
      final taskModel = realm
          .query<TaskDetailModel>('taskId == \$0', [task.taskId]).firstOrNull;

      if (taskModel != null) {
        // TaskStatus
        if (isSuccessful) {
          taskModel.taskStatus =
              "Successful"; // TaskDetailModel.TASK_SUCCESSFUL_STATUS_VALUE
        } else {
          taskModel.taskStatus =
              "Unsuccessful"; // TaskDetailModel.TASK_UNSUCCESSFUL_STATUS_VALUE
        }

        // Wapi3 Submission Status
        if (taskModel.taskStatus == "Successful" ||
            taskModel.taskStatus == "Unsuccessful") {
          // Check if all forms are completed (both mandatory and non-mandatory)
          bool taskFormsEdited = checkAllFormsCompleted(task);
          if (taskFormsEdited) {
            taskModel.submissionState =
                4; // TaskDetailModel.TASK_SUBMISSION_STATE_RESUBMIT_CHANGE
          } else {
            taskModel.submissionState =
                3; // TaskDetailModel.TASK_SUBMISSION_STATE_RESUBMIT_NO_CHANGE
          }
        } else {
          taskModel.submissionState =
              2; // TaskDetailModel.TASK_SUBMISSION_STATE_FIRST_SUBMIT
        }

        // Set task duration and other values
        // TODO: Get these values from UI components
        // taskModel.minutes = taskDuration;
        // taskModel.claimableKms = claimableKms;
        // taskModel.pages = printedPages;
        // taskModel.taskLatitude = taskSubmissionLatitude;
        // taskModel.taskLongitude = taskSubmissionLongitude;

        taskModel.submissionTimeStamp = DateTime.now();

        if (taskModel.isOpen == true) {
          // Set date schedule to today
          taskModel.scheduledTimeStamp = DateTime.now();
        }

        // Mark task as sync pending
        taskModel.syncPending = true;
      }
    });
  }
}
