import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class PrevForm {
  PrevForm();
  logic() {
    var form = Form();
    var questions = form.questions;
    for (var i in questions!) {
      var questionParts = i.questionParts;
      for (var j in questionParts!) {
        var questionAnswer = form.questionAnswers;
        for (var k in questionAnswer!) {
          if (k.questionpartId.toString() == j.questionpartMultiId) {
            var measurement = i.measurements;
            for (var l in measurement!) {
              if (l.measurementId == k.measurementId) {
                
              }
            }
          }
        }
      }
    }
  }
}
