import 'dart:convert';

import 'package:storetrack_app/features/home/<USER>/models/vacancy_model.dart';

class VacanciesResponse {
  final VacanciesData data;

  VacanciesResponse({
    required this.data,
  });

  factory VacanciesResponse.fromRawJson(String str) =>
      VacanciesResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VacanciesResponse.fromJson(Map<String, dynamic> json) =>
      VacanciesResponse(
        data: VacanciesData.fromJson(json["data"] ?? {}),
      );

  Map<String, dynamic> toJson() => {
        "data": data.toJson(),
      };
}

class VacanciesData {
  final List<VacancyModel> vacancies;

  VacanciesData({
    required this.vacancies,
  });

  factory VacanciesData.fromJson(Map<String, dynamic> json) => VacanciesData(
        vacancies: json["vacancies"] != null
            ? List<VacancyModel>.from(
                json["vacancies"].map((x) => VacancyModel.fromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        "vacancies": List<dynamic>.from(vacancies.map((x) => x.toJson())),
      };
}
