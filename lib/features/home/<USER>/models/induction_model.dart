import 'package:realm/realm.dart';

part 'induction_model.realm.dart';

// Induction Model (structured fields)
@RealmModel()
abstract class _InductionModel {
  @PrimaryKey()
  late String id; // "induction"

  // List of inductions
  late List<_InductionItemModel> inductions;
}

// Individual induction item model
@RealmModel()
abstract class _InductionItemModel {
  int? inductionId;
  int? inductionCategoryId;
  String? inductionName;
  String? inductionCategory;
  bool? has;
  bool? isEdit;
}
