// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class HistoryModel extends _HistoryModel
    with RealmEntity, RealmObjectBase, RealmObject {
  HistoryModel(
    String id, {
    Iterable<HistoryItemModel> history = const [],
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set<RealmList<HistoryItemModel>>(
        this, 'history', RealmList<HistoryItemModel>(history));
  }

  HistoryModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  RealmList<HistoryItemModel> get history =>
      RealmObjectBase.get<HistoryItemModel>(this, 'history')
          as RealmList<HistoryItemModel>;
  @override
  set history(covariant RealmList<HistoryItemModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<HistoryModel>> get changes =>
      RealmObjectBase.getChanges<HistoryModel>(this);

  @override
  Stream<RealmObjectChanges<HistoryModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<HistoryModel>(this, keyPaths);

  @override
  HistoryModel freeze() => RealmObjectBase.freezeObject<HistoryModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'history': history.toEJson(),
    };
  }

  static EJsonValue _toEJson(HistoryModel value) => value.toEJson();
  static HistoryModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        HistoryModel(
          fromEJson(id),
          history: fromEJson(ejson['history']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(HistoryModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, HistoryModel, 'HistoryModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('history', RealmPropertyType.object,
          linkTarget: 'HistoryItemModel',
          collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class HistoryItemModel extends _HistoryItemModel
    with RealmEntity, RealmObjectBase, RealmObject {
  HistoryItemModel({
    int? storeId,
    String? storeName,
    String? clientName,
    String? cycle,
    int? budget,
    int? minutes,
    String? scheduledDate,
    double? latitude,
    double? longitude,
    int? forms,
    int? photos,
    int? unacceptedReasonId,
    String? unacceptedReason,
  }) {
    RealmObjectBase.set(this, 'storeId', storeId);
    RealmObjectBase.set(this, 'storeName', storeName);
    RealmObjectBase.set(this, 'clientName', clientName);
    RealmObjectBase.set(this, 'cycle', cycle);
    RealmObjectBase.set(this, 'budget', budget);
    RealmObjectBase.set(this, 'minutes', minutes);
    RealmObjectBase.set(this, 'scheduledDate', scheduledDate);
    RealmObjectBase.set(this, 'latitude', latitude);
    RealmObjectBase.set(this, 'longitude', longitude);
    RealmObjectBase.set(this, 'forms', forms);
    RealmObjectBase.set(this, 'photos', photos);
    RealmObjectBase.set(this, 'unacceptedReasonId', unacceptedReasonId);
    RealmObjectBase.set(this, 'unacceptedReason', unacceptedReason);
  }

  HistoryItemModel._();

  @override
  int? get storeId => RealmObjectBase.get<int>(this, 'storeId') as int?;
  @override
  set storeId(int? value) => RealmObjectBase.set(this, 'storeId', value);

  @override
  String? get storeName =>
      RealmObjectBase.get<String>(this, 'storeName') as String?;
  @override
  set storeName(String? value) => RealmObjectBase.set(this, 'storeName', value);

  @override
  String? get clientName =>
      RealmObjectBase.get<String>(this, 'clientName') as String?;
  @override
  set clientName(String? value) =>
      RealmObjectBase.set(this, 'clientName', value);

  @override
  String? get cycle => RealmObjectBase.get<String>(this, 'cycle') as String?;
  @override
  set cycle(String? value) => RealmObjectBase.set(this, 'cycle', value);

  @override
  int? get budget => RealmObjectBase.get<int>(this, 'budget') as int?;
  @override
  set budget(int? value) => RealmObjectBase.set(this, 'budget', value);

  @override
  int? get minutes => RealmObjectBase.get<int>(this, 'minutes') as int?;
  @override
  set minutes(int? value) => RealmObjectBase.set(this, 'minutes', value);

  @override
  String? get scheduledDate =>
      RealmObjectBase.get<String>(this, 'scheduledDate') as String?;
  @override
  set scheduledDate(String? value) =>
      RealmObjectBase.set(this, 'scheduledDate', value);

  @override
  double? get latitude =>
      RealmObjectBase.get<double>(this, 'latitude') as double?;
  @override
  set latitude(double? value) => RealmObjectBase.set(this, 'latitude', value);

  @override
  double? get longitude =>
      RealmObjectBase.get<double>(this, 'longitude') as double?;
  @override
  set longitude(double? value) => RealmObjectBase.set(this, 'longitude', value);

  @override
  int? get forms => RealmObjectBase.get<int>(this, 'forms') as int?;
  @override
  set forms(int? value) => RealmObjectBase.set(this, 'forms', value);

  @override
  int? get photos => RealmObjectBase.get<int>(this, 'photos') as int?;
  @override
  set photos(int? value) => RealmObjectBase.set(this, 'photos', value);

  @override
  int? get unacceptedReasonId =>
      RealmObjectBase.get<int>(this, 'unacceptedReasonId') as int?;
  @override
  set unacceptedReasonId(int? value) =>
      RealmObjectBase.set(this, 'unacceptedReasonId', value);

  @override
  String? get unacceptedReason =>
      RealmObjectBase.get<String>(this, 'unacceptedReason') as String?;
  @override
  set unacceptedReason(String? value) =>
      RealmObjectBase.set(this, 'unacceptedReason', value);

  @override
  Stream<RealmObjectChanges<HistoryItemModel>> get changes =>
      RealmObjectBase.getChanges<HistoryItemModel>(this);

  @override
  Stream<RealmObjectChanges<HistoryItemModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<HistoryItemModel>(this, keyPaths);

  @override
  HistoryItemModel freeze() =>
      RealmObjectBase.freezeObject<HistoryItemModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'storeId': storeId.toEJson(),
      'storeName': storeName.toEJson(),
      'clientName': clientName.toEJson(),
      'cycle': cycle.toEJson(),
      'budget': budget.toEJson(),
      'minutes': minutes.toEJson(),
      'scheduledDate': scheduledDate.toEJson(),
      'latitude': latitude.toEJson(),
      'longitude': longitude.toEJson(),
      'forms': forms.toEJson(),
      'photos': photos.toEJson(),
      'unacceptedReasonId': unacceptedReasonId.toEJson(),
      'unacceptedReason': unacceptedReason.toEJson(),
    };
  }

  static EJsonValue _toEJson(HistoryItemModel value) => value.toEJson();
  static HistoryItemModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return HistoryItemModel(
      storeId: fromEJson(ejson['storeId']),
      storeName: fromEJson(ejson['storeName']),
      clientName: fromEJson(ejson['clientName']),
      cycle: fromEJson(ejson['cycle']),
      budget: fromEJson(ejson['budget']),
      minutes: fromEJson(ejson['minutes']),
      scheduledDate: fromEJson(ejson['scheduledDate']),
      latitude: fromEJson(ejson['latitude']),
      longitude: fromEJson(ejson['longitude']),
      forms: fromEJson(ejson['forms']),
      photos: fromEJson(ejson['photos']),
      unacceptedReasonId: fromEJson(ejson['unacceptedReasonId']),
      unacceptedReason: fromEJson(ejson['unacceptedReason']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(HistoryItemModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, HistoryItemModel, 'HistoryItemModel', [
      SchemaProperty('storeId', RealmPropertyType.int, optional: true),
      SchemaProperty('storeName', RealmPropertyType.string, optional: true),
      SchemaProperty('clientName', RealmPropertyType.string, optional: true),
      SchemaProperty('cycle', RealmPropertyType.string, optional: true),
      SchemaProperty('budget', RealmPropertyType.int, optional: true),
      SchemaProperty('minutes', RealmPropertyType.int, optional: true),
      SchemaProperty('scheduledDate', RealmPropertyType.string, optional: true),
      SchemaProperty('latitude', RealmPropertyType.double, optional: true),
      SchemaProperty('longitude', RealmPropertyType.double, optional: true),
      SchemaProperty('forms', RealmPropertyType.int, optional: true),
      SchemaProperty('photos', RealmPropertyType.int, optional: true),
      SchemaProperty('unacceptedReasonId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('unacceptedReason', RealmPropertyType.string,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
