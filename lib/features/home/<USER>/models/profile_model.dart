import 'package:realm/realm.dart';
part 'profile_model.realm.dart';

@RealmModel()
abstract class _ProfileModel {
  @PrimaryKey()
  int id = 0; // Single profile entry
  String? token;
  int? userId;
  int? contractorId;
  int? countryId;
  int? stateId;
  String? firstName;
  String? lastName;
  String? address;
  String? email;
  String? country;
  String? state;
  String? suburb;
  String? postcode;
  String? pAddress;
  String? pSuburb;
  String? pPostcode;
  int? pCountryId;
  String? pCountry;
  String? pRegion;
  int? pRegionId;
  String? pDeliveryComment;
  String? mobile;
  String? profileImageUrl;
  String? modifiedTimeStampProfile;
  bool? adminAccess;
  bool? createTask;
  String? orgIDs;
}
