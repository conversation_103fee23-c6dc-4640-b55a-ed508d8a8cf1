import 'package:realm/realm.dart';

part 'availability_model.realm.dart';

// Availability Model (structured fields)
@RealmModel()
abstract class _AvailabilityModel {
  @PrimaryKey()
  late String id; // "availability"

  // List of day availabilities
  late List<_DayAvailabilityModel> days;
}

// Day availability model
@RealmModel()
abstract class _DayAvailabilityModel {
  late int dayNumber;
  late int dayOrder;
  late String dayDescription;
  late List<_DaySpanModel> daySpans;
}

// Day span model
@RealmModel()
abstract class _DaySpanModel {
  late int dayEntryNumber;
  late String startHour;
  late String endHour;
}
