// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alert_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class AlertModel extends _AlertModel
    with RealmEntity, RealmObjectBase, RealmObject {
  AlertModel(
    String id, {
    Iterable<AlertItemModel> alerts = const [],
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set<RealmList<AlertItemModel>>(
        this, 'alerts', RealmList<AlertItemModel>(alerts));
  }

  AlertModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  RealmList<AlertItemModel> get alerts =>
      RealmObjectBase.get<AlertItemModel>(this, 'alerts')
          as RealmList<AlertItemModel>;
  @override
  set alerts(covariant RealmList<AlertItemModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<AlertModel>> get changes =>
      RealmObjectBase.getChanges<AlertModel>(this);

  @override
  Stream<RealmObjectChanges<AlertModel>> changesFor([List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<AlertModel>(this, keyPaths);

  @override
  AlertModel freeze() => RealmObjectBase.freezeObject<AlertModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'alerts': alerts.toEJson(),
    };
  }

  static EJsonValue _toEJson(AlertModel value) => value.toEJson();
  static AlertModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        AlertModel(
          fromEJson(id),
          alerts: fromEJson(ejson['alerts']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(AlertModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, AlertModel, 'AlertModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('alerts', RealmPropertyType.object,
          linkTarget: 'AlertItemModel',
          collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class AlertItemModel extends _AlertItemModel
    with RealmEntity, RealmObjectBase, RealmObject {
  AlertItemModel(
    int alertId,
    int clientId,
    int userId,
    String title,
    String shortDescription,
    String comment,
    String sender,
    DateTime date,
    String clientLogoUrl,
    bool isRead, {
    String? clientName,
    String? storeName,
    String? storeAddress,
    String? storeSuburb,
    String? storePostcode,
    String? taskId,
    String? taskDuration,
    String? cycleName,
  }) {
    RealmObjectBase.set(this, 'alertId', alertId);
    RealmObjectBase.set(this, 'clientId', clientId);
    RealmObjectBase.set(this, 'userId', userId);
    RealmObjectBase.set(this, 'title', title);
    RealmObjectBase.set(this, 'shortDescription', shortDescription);
    RealmObjectBase.set(this, 'comment', comment);
    RealmObjectBase.set(this, 'sender', sender);
    RealmObjectBase.set(this, 'date', date);
    RealmObjectBase.set(this, 'clientLogoUrl', clientLogoUrl);
    RealmObjectBase.set(this, 'isRead', isRead);
    RealmObjectBase.set(this, 'clientName', clientName);
    RealmObjectBase.set(this, 'storeName', storeName);
    RealmObjectBase.set(this, 'storeAddress', storeAddress);
    RealmObjectBase.set(this, 'storeSuburb', storeSuburb);
    RealmObjectBase.set(this, 'storePostcode', storePostcode);
    RealmObjectBase.set(this, 'taskId', taskId);
    RealmObjectBase.set(this, 'taskDuration', taskDuration);
    RealmObjectBase.set(this, 'cycleName', cycleName);
  }

  AlertItemModel._();

  @override
  int get alertId => RealmObjectBase.get<int>(this, 'alertId') as int;
  @override
  set alertId(int value) => RealmObjectBase.set(this, 'alertId', value);

  @override
  int get clientId => RealmObjectBase.get<int>(this, 'clientId') as int;
  @override
  set clientId(int value) => RealmObjectBase.set(this, 'clientId', value);

  @override
  int get userId => RealmObjectBase.get<int>(this, 'userId') as int;
  @override
  set userId(int value) => RealmObjectBase.set(this, 'userId', value);

  @override
  String get title => RealmObjectBase.get<String>(this, 'title') as String;
  @override
  set title(String value) => RealmObjectBase.set(this, 'title', value);

  @override
  String get shortDescription =>
      RealmObjectBase.get<String>(this, 'shortDescription') as String;
  @override
  set shortDescription(String value) =>
      RealmObjectBase.set(this, 'shortDescription', value);

  @override
  String get comment => RealmObjectBase.get<String>(this, 'comment') as String;
  @override
  set comment(String value) => RealmObjectBase.set(this, 'comment', value);

  @override
  String get sender => RealmObjectBase.get<String>(this, 'sender') as String;
  @override
  set sender(String value) => RealmObjectBase.set(this, 'sender', value);

  @override
  DateTime get date => RealmObjectBase.get<DateTime>(this, 'date') as DateTime;
  @override
  set date(DateTime value) => RealmObjectBase.set(this, 'date', value);

  @override
  String get clientLogoUrl =>
      RealmObjectBase.get<String>(this, 'clientLogoUrl') as String;
  @override
  set clientLogoUrl(String value) =>
      RealmObjectBase.set(this, 'clientLogoUrl', value);

  @override
  bool get isRead => RealmObjectBase.get<bool>(this, 'isRead') as bool;
  @override
  set isRead(bool value) => RealmObjectBase.set(this, 'isRead', value);

  @override
  String? get clientName =>
      RealmObjectBase.get<String>(this, 'clientName') as String?;
  @override
  set clientName(String? value) =>
      RealmObjectBase.set(this, 'clientName', value);

  @override
  String? get storeName =>
      RealmObjectBase.get<String>(this, 'storeName') as String?;
  @override
  set storeName(String? value) => RealmObjectBase.set(this, 'storeName', value);

  @override
  String? get storeAddress =>
      RealmObjectBase.get<String>(this, 'storeAddress') as String?;
  @override
  set storeAddress(String? value) =>
      RealmObjectBase.set(this, 'storeAddress', value);

  @override
  String? get storeSuburb =>
      RealmObjectBase.get<String>(this, 'storeSuburb') as String?;
  @override
  set storeSuburb(String? value) =>
      RealmObjectBase.set(this, 'storeSuburb', value);

  @override
  String? get storePostcode =>
      RealmObjectBase.get<String>(this, 'storePostcode') as String?;
  @override
  set storePostcode(String? value) =>
      RealmObjectBase.set(this, 'storePostcode', value);

  @override
  String? get taskId => RealmObjectBase.get<String>(this, 'taskId') as String?;
  @override
  set taskId(String? value) => RealmObjectBase.set(this, 'taskId', value);

  @override
  String? get taskDuration =>
      RealmObjectBase.get<String>(this, 'taskDuration') as String?;
  @override
  set taskDuration(String? value) =>
      RealmObjectBase.set(this, 'taskDuration', value);

  @override
  String? get cycleName =>
      RealmObjectBase.get<String>(this, 'cycleName') as String?;
  @override
  set cycleName(String? value) => RealmObjectBase.set(this, 'cycleName', value);

  @override
  Stream<RealmObjectChanges<AlertItemModel>> get changes =>
      RealmObjectBase.getChanges<AlertItemModel>(this);

  @override
  Stream<RealmObjectChanges<AlertItemModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<AlertItemModel>(this, keyPaths);

  @override
  AlertItemModel freeze() => RealmObjectBase.freezeObject<AlertItemModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'alertId': alertId.toEJson(),
      'clientId': clientId.toEJson(),
      'userId': userId.toEJson(),
      'title': title.toEJson(),
      'shortDescription': shortDescription.toEJson(),
      'comment': comment.toEJson(),
      'sender': sender.toEJson(),
      'date': date.toEJson(),
      'clientLogoUrl': clientLogoUrl.toEJson(),
      'isRead': isRead.toEJson(),
      'clientName': clientName.toEJson(),
      'storeName': storeName.toEJson(),
      'storeAddress': storeAddress.toEJson(),
      'storeSuburb': storeSuburb.toEJson(),
      'storePostcode': storePostcode.toEJson(),
      'taskId': taskId.toEJson(),
      'taskDuration': taskDuration.toEJson(),
      'cycleName': cycleName.toEJson(),
    };
  }

  static EJsonValue _toEJson(AlertItemModel value) => value.toEJson();
  static AlertItemModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'alertId': EJsonValue alertId,
        'clientId': EJsonValue clientId,
        'userId': EJsonValue userId,
        'title': EJsonValue title,
        'shortDescription': EJsonValue shortDescription,
        'comment': EJsonValue comment,
        'sender': EJsonValue sender,
        'date': EJsonValue date,
        'clientLogoUrl': EJsonValue clientLogoUrl,
        'isRead': EJsonValue isRead,
      } =>
        AlertItemModel(
          fromEJson(alertId),
          fromEJson(clientId),
          fromEJson(userId),
          fromEJson(title),
          fromEJson(shortDescription),
          fromEJson(comment),
          fromEJson(sender),
          fromEJson(date),
          fromEJson(clientLogoUrl),
          fromEJson(isRead),
          clientName: fromEJson(ejson['clientName']),
          storeName: fromEJson(ejson['storeName']),
          storeAddress: fromEJson(ejson['storeAddress']),
          storeSuburb: fromEJson(ejson['storeSuburb']),
          storePostcode: fromEJson(ejson['storePostcode']),
          taskId: fromEJson(ejson['taskId']),
          taskDuration: fromEJson(ejson['taskDuration']),
          cycleName: fromEJson(ejson['cycleName']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(AlertItemModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, AlertItemModel, 'AlertItemModel', [
      SchemaProperty('alertId', RealmPropertyType.int),
      SchemaProperty('clientId', RealmPropertyType.int),
      SchemaProperty('userId', RealmPropertyType.int),
      SchemaProperty('title', RealmPropertyType.string),
      SchemaProperty('shortDescription', RealmPropertyType.string),
      SchemaProperty('comment', RealmPropertyType.string),
      SchemaProperty('sender', RealmPropertyType.string),
      SchemaProperty('date', RealmPropertyType.timestamp),
      SchemaProperty('clientLogoUrl', RealmPropertyType.string),
      SchemaProperty('isRead', RealmPropertyType.bool),
      SchemaProperty('clientName', RealmPropertyType.string, optional: true),
      SchemaProperty('storeName', RealmPropertyType.string, optional: true),
      SchemaProperty('storeAddress', RealmPropertyType.string, optional: true),
      SchemaProperty('storeSuburb', RealmPropertyType.string, optional: true),
      SchemaProperty('storePostcode', RealmPropertyType.string, optional: true),
      SchemaProperty('taskId', RealmPropertyType.string, optional: true),
      SchemaProperty('taskDuration', RealmPropertyType.string, optional: true),
      SchemaProperty('cycleName', RealmPropertyType.string, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
