// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'induction_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class InductionModel extends _InductionModel
    with RealmEntity, RealmObjectBase, RealmObject {
  InductionModel(
    String id, {
    Iterable<InductionItemModel> inductions = const [],
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set<RealmList<InductionItemModel>>(
        this, 'inductions', RealmList<InductionItemModel>(inductions));
  }

  InductionModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  RealmList<InductionItemModel> get inductions =>
      RealmObjectBase.get<InductionItemModel>(this, 'inductions')
          as RealmList<InductionItemModel>;
  @override
  set inductions(covariant RealmList<InductionItemModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<InductionModel>> get changes =>
      RealmObjectBase.getChanges<InductionModel>(this);

  @override
  Stream<RealmObjectChanges<InductionModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<InductionModel>(this, keyPaths);

  @override
  InductionModel freeze() => RealmObjectBase.freezeObject<InductionModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'inductions': inductions.toEJson(),
    };
  }

  static EJsonValue _toEJson(InductionModel value) => value.toEJson();
  static InductionModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        InductionModel(
          fromEJson(id),
          inductions: fromEJson(ejson['inductions']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(InductionModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, InductionModel, 'InductionModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('inductions', RealmPropertyType.object,
          linkTarget: 'InductionItemModel',
          collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class InductionItemModel extends _InductionItemModel
    with RealmEntity, RealmObjectBase, RealmObject {
  InductionItemModel({
    int? inductionId,
    int? inductionCategoryId,
    String? inductionName,
    String? inductionCategory,
    bool? has,
    bool? isEdit,
  }) {
    RealmObjectBase.set(this, 'inductionId', inductionId);
    RealmObjectBase.set(this, 'inductionCategoryId', inductionCategoryId);
    RealmObjectBase.set(this, 'inductionName', inductionName);
    RealmObjectBase.set(this, 'inductionCategory', inductionCategory);
    RealmObjectBase.set(this, 'has', has);
    RealmObjectBase.set(this, 'isEdit', isEdit);
  }

  InductionItemModel._();

  @override
  int? get inductionId => RealmObjectBase.get<int>(this, 'inductionId') as int?;
  @override
  set inductionId(int? value) =>
      RealmObjectBase.set(this, 'inductionId', value);

  @override
  int? get inductionCategoryId =>
      RealmObjectBase.get<int>(this, 'inductionCategoryId') as int?;
  @override
  set inductionCategoryId(int? value) =>
      RealmObjectBase.set(this, 'inductionCategoryId', value);

  @override
  String? get inductionName =>
      RealmObjectBase.get<String>(this, 'inductionName') as String?;
  @override
  set inductionName(String? value) =>
      RealmObjectBase.set(this, 'inductionName', value);

  @override
  String? get inductionCategory =>
      RealmObjectBase.get<String>(this, 'inductionCategory') as String?;
  @override
  set inductionCategory(String? value) =>
      RealmObjectBase.set(this, 'inductionCategory', value);

  @override
  bool? get has => RealmObjectBase.get<bool>(this, 'has') as bool?;
  @override
  set has(bool? value) => RealmObjectBase.set(this, 'has', value);

  @override
  bool? get isEdit => RealmObjectBase.get<bool>(this, 'isEdit') as bool?;
  @override
  set isEdit(bool? value) => RealmObjectBase.set(this, 'isEdit', value);

  @override
  Stream<RealmObjectChanges<InductionItemModel>> get changes =>
      RealmObjectBase.getChanges<InductionItemModel>(this);

  @override
  Stream<RealmObjectChanges<InductionItemModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<InductionItemModel>(this, keyPaths);

  @override
  InductionItemModel freeze() =>
      RealmObjectBase.freezeObject<InductionItemModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'inductionId': inductionId.toEJson(),
      'inductionCategoryId': inductionCategoryId.toEJson(),
      'inductionName': inductionName.toEJson(),
      'inductionCategory': inductionCategory.toEJson(),
      'has': has.toEJson(),
      'isEdit': isEdit.toEJson(),
    };
  }

  static EJsonValue _toEJson(InductionItemModel value) => value.toEJson();
  static InductionItemModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return InductionItemModel(
      inductionId: fromEJson(ejson['inductionId']),
      inductionCategoryId: fromEJson(ejson['inductionCategoryId']),
      inductionName: fromEJson(ejson['inductionName']),
      inductionCategory: fromEJson(ejson['inductionCategory']),
      has: fromEJson(ejson['has']),
      isEdit: fromEJson(ejson['isEdit']),
    );
  }

  static final schema = () {
    RealmObjectBase.registerFactory(InductionItemModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, InductionItemModel, 'InductionItemModel', [
      SchemaProperty('inductionId', RealmPropertyType.int, optional: true),
      SchemaProperty('inductionCategoryId', RealmPropertyType.int,
          optional: true),
      SchemaProperty('inductionName', RealmPropertyType.string, optional: true),
      SchemaProperty('inductionCategory', RealmPropertyType.string,
          optional: true),
      SchemaProperty('has', RealmPropertyType.bool, optional: true),
      SchemaProperty('isEdit', RealmPropertyType.bool, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
