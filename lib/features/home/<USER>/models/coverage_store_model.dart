class CoverageStoresResponse {
  final CoverageStoresData? data;
  final String? message;

  CoverageStoresResponse({
    this.data,
    this.message,
  });

  factory CoverageStoresResponse.fromJson(Map<String, dynamic> json) {
    return CoverageStoresResponse(
      data: json['data'] != null
          ? CoverageStoresData.fromJson(json['data'])
          : null,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data?.toJson(),
      'message': message,
    };
  }
}

class CoverageStoresData {
  final List<CoverageStore> coverageStores;

  CoverageStoresData({
    required this.coverageStores,
  });

  factory CoverageStoresData.fromJson(Map<String, dynamic> json) {
    return CoverageStoresData(
      coverageStores: json['CoverageStores'] != null
          ? (json['CoverageStores'] as List)
              .map((item) => CoverageStore.fromJson(item))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJ<PERSON>() {
    return {
      'CoverageStores': coverageStores.map((store) => store.toJson()).toList(),
    };
  }
}

class CoverageStore {
  final int storeId;
  final int uniqueId;
  final int groupId;
  final String groupName;
  final String storeName;
  final String address;
  final String region;
  final String suburb;
  final String postcode;

  CoverageStore({
    required this.storeId,
    required this.uniqueId,
    required this.groupId,
    required this.groupName,
    required this.storeName,
    required this.address,
    required this.region,
    required this.suburb,
    required this.postcode,
  });

  factory CoverageStore.fromJson(Map<String, dynamic> json) {
    return CoverageStore(
      storeId: json['store_id'] ?? 0,
      uniqueId: json['unique_id'] ?? 0,
      groupId: json['groud_id'] ?? 0, // Note: API uses 'groud_id' (typo in API)
      groupName:
          json['groud_name']?.toString() ?? '', // Note: API uses 'groud_name'
      storeName: json['store_name']?.toString() ?? '',
      address: json['store_address']?.toString() ?? '',
      region: json['store_region']?.toString() ?? '',
      suburb: json['store_suburb']?.toString() ?? '',
      postcode: json['postcode']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'store_id': storeId,
      'unique_id': uniqueId,
      'groud_id': groupId, // Keeping API field name for consistency
      'groud_name': groupName,
      'store_name': storeName,
      'store_address': address,
      'store_region': region,
      'store_suburb': suburb,
      'postcode': postcode,
    };
  }

  // For display in UI
  String get displayName => storeName;
  String get fullAddress => '$address, $suburb $postcode';
}
