// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'availability_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class AvailabilityModel extends _AvailabilityModel
    with RealmEntity, RealmObjectBase, RealmObject {
  AvailabilityModel(
    String id, {
    Iterable<DayAvailabilityModel> days = const [],
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set<RealmList<DayAvailabilityModel>>(
        this, 'days', RealmList<DayAvailabilityModel>(days));
  }

  AvailabilityModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  RealmList<DayAvailabilityModel> get days =>
      RealmObjectBase.get<DayAvailabilityModel>(this, 'days')
          as RealmList<DayAvailabilityModel>;
  @override
  set days(covariant RealmList<DayAvailabilityModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<AvailabilityModel>> get changes =>
      RealmObjectBase.getChanges<AvailabilityModel>(this);

  @override
  Stream<RealmObjectChanges<AvailabilityModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<AvailabilityModel>(this, keyPaths);

  @override
  AvailabilityModel freeze() =>
      RealmObjectBase.freezeObject<AvailabilityModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'days': days.toEJson(),
    };
  }

  static EJsonValue _toEJson(AvailabilityModel value) => value.toEJson();
  static AvailabilityModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        AvailabilityModel(
          fromEJson(id),
          days: fromEJson(ejson['days']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(AvailabilityModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, AvailabilityModel, 'AvailabilityModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('days', RealmPropertyType.object,
          linkTarget: 'DayAvailabilityModel',
          collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class DayAvailabilityModel extends _DayAvailabilityModel
    with RealmEntity, RealmObjectBase, RealmObject {
  DayAvailabilityModel(
    int dayNumber,
    int dayOrder,
    String dayDescription, {
    Iterable<DaySpanModel> daySpans = const [],
  }) {
    RealmObjectBase.set(this, 'dayNumber', dayNumber);
    RealmObjectBase.set(this, 'dayOrder', dayOrder);
    RealmObjectBase.set(this, 'dayDescription', dayDescription);
    RealmObjectBase.set<RealmList<DaySpanModel>>(
        this, 'daySpans', RealmList<DaySpanModel>(daySpans));
  }

  DayAvailabilityModel._();

  @override
  int get dayNumber => RealmObjectBase.get<int>(this, 'dayNumber') as int;
  @override
  set dayNumber(int value) => RealmObjectBase.set(this, 'dayNumber', value);

  @override
  int get dayOrder => RealmObjectBase.get<int>(this, 'dayOrder') as int;
  @override
  set dayOrder(int value) => RealmObjectBase.set(this, 'dayOrder', value);

  @override
  String get dayDescription =>
      RealmObjectBase.get<String>(this, 'dayDescription') as String;
  @override
  set dayDescription(String value) =>
      RealmObjectBase.set(this, 'dayDescription', value);

  @override
  RealmList<DaySpanModel> get daySpans =>
      RealmObjectBase.get<DaySpanModel>(this, 'daySpans')
          as RealmList<DaySpanModel>;
  @override
  set daySpans(covariant RealmList<DaySpanModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<DayAvailabilityModel>> get changes =>
      RealmObjectBase.getChanges<DayAvailabilityModel>(this);

  @override
  Stream<RealmObjectChanges<DayAvailabilityModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<DayAvailabilityModel>(this, keyPaths);

  @override
  DayAvailabilityModel freeze() =>
      RealmObjectBase.freezeObject<DayAvailabilityModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'dayNumber': dayNumber.toEJson(),
      'dayOrder': dayOrder.toEJson(),
      'dayDescription': dayDescription.toEJson(),
      'daySpans': daySpans.toEJson(),
    };
  }

  static EJsonValue _toEJson(DayAvailabilityModel value) => value.toEJson();
  static DayAvailabilityModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'dayNumber': EJsonValue dayNumber,
        'dayOrder': EJsonValue dayOrder,
        'dayDescription': EJsonValue dayDescription,
      } =>
        DayAvailabilityModel(
          fromEJson(dayNumber),
          fromEJson(dayOrder),
          fromEJson(dayDescription),
          daySpans: fromEJson(ejson['daySpans']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(DayAvailabilityModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, DayAvailabilityModel, 'DayAvailabilityModel', [
      SchemaProperty('dayNumber', RealmPropertyType.int),
      SchemaProperty('dayOrder', RealmPropertyType.int),
      SchemaProperty('dayDescription', RealmPropertyType.string),
      SchemaProperty('daySpans', RealmPropertyType.object,
          linkTarget: 'DaySpanModel', collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class DaySpanModel extends _DaySpanModel
    with RealmEntity, RealmObjectBase, RealmObject {
  DaySpanModel(
    int dayEntryNumber,
    String startHour,
    String endHour,
  ) {
    RealmObjectBase.set(this, 'dayEntryNumber', dayEntryNumber);
    RealmObjectBase.set(this, 'startHour', startHour);
    RealmObjectBase.set(this, 'endHour', endHour);
  }

  DaySpanModel._();

  @override
  int get dayEntryNumber =>
      RealmObjectBase.get<int>(this, 'dayEntryNumber') as int;
  @override
  set dayEntryNumber(int value) =>
      RealmObjectBase.set(this, 'dayEntryNumber', value);

  @override
  String get startHour =>
      RealmObjectBase.get<String>(this, 'startHour') as String;
  @override
  set startHour(String value) => RealmObjectBase.set(this, 'startHour', value);

  @override
  String get endHour => RealmObjectBase.get<String>(this, 'endHour') as String;
  @override
  set endHour(String value) => RealmObjectBase.set(this, 'endHour', value);

  @override
  Stream<RealmObjectChanges<DaySpanModel>> get changes =>
      RealmObjectBase.getChanges<DaySpanModel>(this);

  @override
  Stream<RealmObjectChanges<DaySpanModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<DaySpanModel>(this, keyPaths);

  @override
  DaySpanModel freeze() => RealmObjectBase.freezeObject<DaySpanModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'dayEntryNumber': dayEntryNumber.toEJson(),
      'startHour': startHour.toEJson(),
      'endHour': endHour.toEJson(),
    };
  }

  static EJsonValue _toEJson(DaySpanModel value) => value.toEJson();
  static DaySpanModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'dayEntryNumber': EJsonValue dayEntryNumber,
        'startHour': EJsonValue startHour,
        'endHour': EJsonValue endHour,
      } =>
        DaySpanModel(
          fromEJson(dayEntryNumber),
          fromEJson(startHour),
          fromEJson(endHour),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(DaySpanModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, DaySpanModel, 'DaySpanModel', [
      SchemaProperty('dayEntryNumber', RealmPropertyType.int),
      SchemaProperty('startHour', RealmPropertyType.string),
      SchemaProperty('endHour', RealmPropertyType.string),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
