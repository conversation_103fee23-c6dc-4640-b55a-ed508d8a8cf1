import 'package:realm/realm.dart';

part 'history_model.realm.dart';

// History Model (structured fields)
@RealmModel()
abstract class _HistoryModel {
  @PrimaryKey()
  late String id; // "history"

  // List of history items
  late List<_HistoryItemModel> history;
}

// Individual history item model
@RealmModel()
abstract class _HistoryItemModel {
  int? storeId;
  String? storeName;
  String? clientName;
  String? cycle;
  int? budget;
  int? minutes;
  String? scheduledDate;
  double? latitude;
  double? longitude;
  int? forms;
  int? photos;
  int? unacceptedReasonId;
  String? unacceptedReason;
}
