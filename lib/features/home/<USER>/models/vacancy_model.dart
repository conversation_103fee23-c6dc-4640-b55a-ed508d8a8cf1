import 'dart:convert';

import 'package:storetrack_app/features/home/<USER>/entities/vacancy_entity.dart';

class VacancyModel extends VacancyEntity {
  const VacancyModel({
    required super.id,
    required super.jobTitle,
    required super.jobLocation,
    required super.jobDescription,
    super.companyName,
    super.salaryRange,
    super.employmentType,
    super.postedDate,
    super.canApply,
    super.canRefer,
  });

  factory VacancyModel.fromRawJson(String str) =>
      VacancyModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VacancyModel.fromJson(Map<String, dynamic> json) => VacancyModel(
        id: json["rec_id"] ?? 0,
        jobTitle: json["pos_title"] ?? "",
        jobLocation:
            VacancyModel._formatLocation(json["suburb"], json["state"]),
        jobDescription: json["pos_description"] ?? "",
        companyName: null, // Not provided in API response
        salaryRange: null, // Not provided in API response
        employmentType: null, // Not provided in API response
        postedDate: null, // Not provided in API response
        canApply: true, // Default to true
        canRefer: true, // Default to true
      );

  Map<String, dynamic> toJson() => {
        "rec_id": id,
        "pos_title": jobTitle,
        "suburb": jobLocation.split(',').first.trim(),
        "state": jobLocation.split(',').length > 1
            ? jobLocation.split(',')[1].trim()
            : "",
        "pos_description": jobDescription,
        "company_name": companyName,
        "salary_range": salaryRange,
        "employment_type": employmentType,
        "posted_date": postedDate?.toIso8601String(),
        "can_apply": canApply,
        "can_refer": canRefer,
      };

  static String _formatLocation(dynamic suburb, dynamic state) {
    final suburbStr = (suburb ?? "").toString().trim();
    final stateStr = (state ?? "").toString().trim();

    if (suburbStr.isEmpty && stateStr.isEmpty) return "";
    if (suburbStr.isEmpty) return stateStr;
    if (stateStr.isEmpty) return suburbStr;

    return "$suburbStr, $stateStr";
  }

  VacancyEntity toEntity() => VacancyEntity(
        id: id,
        jobTitle: jobTitle,
        jobLocation: jobLocation,
        jobDescription: jobDescription,
        companyName: companyName,
        salaryRange: salaryRange,
        employmentType: employmentType,
        postedDate: postedDate,
        canApply: canApply,
        canRefer: canRefer,
      );
}
