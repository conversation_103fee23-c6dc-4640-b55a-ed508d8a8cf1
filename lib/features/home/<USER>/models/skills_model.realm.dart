// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'skills_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class SkillsModel extends _SkillsModel
    with RealmEntity, RealmObjectBase, RealmObject {
  SkillsModel(
    String id, {
    Iterable<SkillItemModel> skills = const [],
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set<RealmList<SkillItemModel>>(
        this, 'skills', RealmList<SkillItemModel>(skills));
  }

  SkillsModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  RealmList<SkillItemModel> get skills =>
      RealmObjectBase.get<SkillItemModel>(this, 'skills')
          as RealmList<SkillItemModel>;
  @override
  set skills(covariant RealmList<SkillItemModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<SkillsModel>> get changes =>
      RealmObjectBase.getChanges<SkillsModel>(this);

  @override
  Stream<RealmObjectChanges<SkillsModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<SkillsModel>(this, keyPaths);

  @override
  SkillsModel freeze() => RealmObjectBase.freezeObject<SkillsModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'skills': skills.toEJson(),
    };
  }

  static EJsonValue _toEJson(SkillsModel value) => value.toEJson();
  static SkillsModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        SkillsModel(
          fromEJson(id),
          skills: fromEJson(ejson['skills']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(SkillsModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, SkillsModel, 'SkillsModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('skills', RealmPropertyType.object,
          linkTarget: 'SkillItemModel',
          collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class SkillItemModel extends _SkillItemModel
    with RealmEntity, RealmObjectBase, RealmObject {
  SkillItemModel(
    int skillId,
    String skillName,
    String skillDescription,
    bool has,
  ) {
    RealmObjectBase.set(this, 'skillId', skillId);
    RealmObjectBase.set(this, 'skillName', skillName);
    RealmObjectBase.set(this, 'skillDescription', skillDescription);
    RealmObjectBase.set(this, 'has', has);
  }

  SkillItemModel._();

  @override
  int get skillId => RealmObjectBase.get<int>(this, 'skillId') as int;
  @override
  set skillId(int value) => RealmObjectBase.set(this, 'skillId', value);

  @override
  String get skillName =>
      RealmObjectBase.get<String>(this, 'skillName') as String;
  @override
  set skillName(String value) => RealmObjectBase.set(this, 'skillName', value);

  @override
  String get skillDescription =>
      RealmObjectBase.get<String>(this, 'skillDescription') as String;
  @override
  set skillDescription(String value) =>
      RealmObjectBase.set(this, 'skillDescription', value);

  @override
  bool get has => RealmObjectBase.get<bool>(this, 'has') as bool;
  @override
  set has(bool value) => RealmObjectBase.set(this, 'has', value);

  @override
  Stream<RealmObjectChanges<SkillItemModel>> get changes =>
      RealmObjectBase.getChanges<SkillItemModel>(this);

  @override
  Stream<RealmObjectChanges<SkillItemModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<SkillItemModel>(this, keyPaths);

  @override
  SkillItemModel freeze() => RealmObjectBase.freezeObject<SkillItemModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'skillId': skillId.toEJson(),
      'skillName': skillName.toEJson(),
      'skillDescription': skillDescription.toEJson(),
      'has': has.toEJson(),
    };
  }

  static EJsonValue _toEJson(SkillItemModel value) => value.toEJson();
  static SkillItemModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'skillId': EJsonValue skillId,
        'skillName': EJsonValue skillName,
        'skillDescription': EJsonValue skillDescription,
        'has': EJsonValue has,
      } =>
        SkillItemModel(
          fromEJson(skillId),
          fromEJson(skillName),
          fromEJson(skillDescription),
          fromEJson(has),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(SkillItemModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, SkillItemModel, 'SkillItemModel', [
      SchemaProperty('skillId', RealmPropertyType.int),
      SchemaProperty('skillName', RealmPropertyType.string),
      SchemaProperty('skillDescription', RealmPropertyType.string),
      SchemaProperty('has', RealmPropertyType.bool),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
