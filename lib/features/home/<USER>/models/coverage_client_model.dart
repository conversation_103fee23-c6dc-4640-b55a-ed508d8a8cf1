class CoverageClientsResponse {
  final CoverageClientsData? data;
  final String? message;

  CoverageClientsResponse({
    this.data,
    this.message,
  });

  factory CoverageClientsResponse.fromJson(Map<String, dynamic> json) {
    return CoverageClientsResponse(
      data: json['data'] != null
          ? CoverageClientsData.fromJson(json['data'])
          : null,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data?.toJson(),
      'message': message,
    };
  }
}

class CoverageClientsData {
  final List<CoverageClient> coverageClients;

  CoverageClientsData({
    required this.coverageClients,
  });

  factory CoverageClientsData.fromJson(Map<String, dynamic> json) {
    return CoverageClientsData(
      coverageClients: json['CoverageClients'] != null
          ? (json['CoverageClients'] as List)
              .map((item) => CoverageClient.fromJson(item))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'CoverageClients':
          coverageClients.map((client) => client.toJson()).toList(),
    };
  }
}

class CoverageClient {
  final int clientId;
  final String clientName;
  final String? clientLogoUrl;

  CoverageClient({
    required this.clientId,
    required this.clientName,
    this.clientLogoUrl,
  });

  factory CoverageClient.fromJson(Map<String, dynamic> json) {
    return CoverageClient(
      clientId: json['client_id'] ?? 0,
      clientName: json['client_name']?.toString() ?? '',
      clientLogoUrl: json['client_logo_url']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'client_id': clientId,
      'client_name': clientName,
      'client_logo_url': clientLogoUrl,
    };
  }

  // For display in UI
  String get displayName => clientName;
}
