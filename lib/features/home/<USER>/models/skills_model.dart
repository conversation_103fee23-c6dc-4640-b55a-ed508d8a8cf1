import 'package:realm/realm.dart';

part 'skills_model.realm.dart';

// Skills Model (structured fields)
@RealmModel()
abstract class _SkillsModel {
  @PrimaryKey()
  late String id; // "skills"

  // List of skills
  late List<_SkillItemModel> skills;
}

// Individual skill model
@RealmModel()
abstract class _SkillItemModel {
  late int skillId;
  late String skillName;
  late String skillDescription;
  late bool has;
}
