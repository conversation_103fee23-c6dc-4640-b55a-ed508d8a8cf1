// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'misc_setting_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class MiscSettingModel extends _MiscSettingModel
    with RealmEntity, RealmObjectBase, RealmObject {
  static var _defaultsSet = false;

  MiscSettingModel(
    int id, {
    String? reportEarlyHours,
    String? reportLateHours,
    String? radiusDistanceInMeters,
    String? imageMinWidthThreshold,
  }) {
    if (!_defaultsSet) {
      _defaultsSet = RealmObjectBase.setDefaults<MiscSettingModel>({
        'id': 0,
      });
    }
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'reportEarlyHours', reportEarlyHours);
    RealmObjectBase.set(this, 'reportLateHours', reportLateHours);
    RealmObjectBase.set(this, 'radiusDistanceInMeters', radiusDistanceInMeters);
    RealmObjectBase.set(this, 'imageMinWidthThreshold', imageMinWidthThreshold);
  }

  MiscSettingModel._();

  @override
  int get id => RealmObjectBase.get<int>(this, 'id') as int;
  @override
  set id(int value) => RealmObjectBase.set(this, 'id', value);

  @override
  String? get reportEarlyHours =>
      RealmObjectBase.get<String>(this, 'reportEarlyHours') as String?;
  @override
  set reportEarlyHours(String? value) =>
      RealmObjectBase.set(this, 'reportEarlyHours', value);

  @override
  String? get reportLateHours =>
      RealmObjectBase.get<String>(this, 'reportLateHours') as String?;
  @override
  set reportLateHours(String? value) =>
      RealmObjectBase.set(this, 'reportLateHours', value);

  @override
  String? get radiusDistanceInMeters =>
      RealmObjectBase.get<String>(this, 'radiusDistanceInMeters') as String?;
  @override
  set radiusDistanceInMeters(String? value) =>
      RealmObjectBase.set(this, 'radiusDistanceInMeters', value);

  @override
  String? get imageMinWidthThreshold =>
      RealmObjectBase.get<String>(this, 'imageMinWidthThreshold') as String?;
  @override
  set imageMinWidthThreshold(String? value) =>
      RealmObjectBase.set(this, 'imageMinWidthThreshold', value);

  @override
  Stream<RealmObjectChanges<MiscSettingModel>> get changes =>
      RealmObjectBase.getChanges<MiscSettingModel>(this);

  @override
  Stream<RealmObjectChanges<MiscSettingModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<MiscSettingModel>(this, keyPaths);

  @override
  MiscSettingModel freeze() =>
      RealmObjectBase.freezeObject<MiscSettingModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'reportEarlyHours': reportEarlyHours.toEJson(),
      'reportLateHours': reportLateHours.toEJson(),
      'radiusDistanceInMeters': radiusDistanceInMeters.toEJson(),
      'imageMinWidthThreshold': imageMinWidthThreshold.toEJson(),
    };
  }

  static EJsonValue _toEJson(MiscSettingModel value) => value.toEJson();
  static MiscSettingModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        MiscSettingModel(
          fromEJson(ejson['id'], defaultValue: 0),
          reportEarlyHours: fromEJson(ejson['reportEarlyHours']),
          reportLateHours: fromEJson(ejson['reportLateHours']),
          radiusDistanceInMeters: fromEJson(ejson['radiusDistanceInMeters']),
          imageMinWidthThreshold: fromEJson(ejson['imageMinWidthThreshold']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(MiscSettingModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, MiscSettingModel, 'MiscSettingModel', [
      SchemaProperty('id', RealmPropertyType.int, primaryKey: true),
      SchemaProperty('reportEarlyHours', RealmPropertyType.string,
          optional: true),
      SchemaProperty('reportLateHours', RealmPropertyType.string,
          optional: true),
      SchemaProperty('radiusDistanceInMeters', RealmPropertyType.string,
          optional: true),
      SchemaProperty('imageMinWidthThreshold', RealmPropertyType.string,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
