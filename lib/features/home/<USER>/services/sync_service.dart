import 'package:flutter/foundation.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_profile_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_misc_setting_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_history_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_alerts_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_availability_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_leave_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_skills_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_induction_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/update_pos_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/pos_request_entity.dart';
import 'package:storetrack_app/core/utils/sync_utils.dart';

class SyncService {
  SyncService._internal();

  static final SyncService _instance = SyncService._internal();

  factory SyncService() => _instance;

  final ValueNotifier<bool> isSyncing = ValueNotifier(false);

  Future<void> sync({String? emulatedUserId}) async {
    if (isSyncing.value) return;

    try {
      isSyncing.value = true;

      // Sequential photo and signature synchronization
      await _syncPhotosAndSignatures(emulatedUserId: emulatedUserId);

      // Final step: Submit reports for completed tasks
      await _submitReports(emulatedUserId: emulatedUserId);

      // Run basic sync operations in parallel
      await Future.wait([
        _getTasks(emulatedUserId: emulatedUserId),
        _syncCalendar(emulatedUserId: emulatedUserId),
        _syncProfile(emulatedUserId: emulatedUserId),
        _syncMiscSetting(emulatedUserId: emulatedUserId),
        // Add new sync methods
        _syncHistory(emulatedUserId: emulatedUserId),
        _syncAlerts(emulatedUserId: emulatedUserId),
        _syncAvailability(emulatedUserId: emulatedUserId),
        _syncLeave(emulatedUserId: emulatedUserId),
        _syncSkills(emulatedUserId: emulatedUserId),
        _syncInduction(emulatedUserId: emulatedUserId),
      ]);

      // Save sync completion time after successful sync
      await sl<DataManager>().saveLastSyncTime(DateTime.now());
    } finally {
      isSyncing.value = false;
    }
  }

  /// Sync method specifically for emulation - only calls 3 essential APIs
  /// 1. tasks_optimize (getTasks)
  /// 2. history
  /// 3. update_pos
  Future<void> syncForEmulation({required String emulatedUserId}) async {
    if (isSyncing.value) return;

    try {
      isSyncing.value = true;
      logger('🔄 Starting emulation sync for user: $emulatedUserId');

      // Run only the 3 essential APIs for emulation
      await Future.wait([
        _getTasks(emulatedUserId: emulatedUserId),
        _syncHistory(emulatedUserId: emulatedUserId),
        _syncUpdatePos(emulatedUserId: emulatedUserId),
      ]);

      logger('✅ Emulation sync completed successfully');

      // Save sync completion time after successful sync
      await sl<DataManager>().saveLastSyncTime(DateTime.now());
    } finally {
      isSyncing.value = false;
    }
  }

  Future<void> _getTasks({String? emulatedUserId}) async {
    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken() ?? '';
      final id = emulatedUserId ?? await dataManager.getUserId() ?? '';
      final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
      const String actualAppVersion = AppConstants.appVersion;

      final request = TasksRequestEntity(
        deviceUid: actualDeviceUid,
        userId: id,
        appversion: actualAppVersion,
        tasks: const [],
        token: token,
      );

      await sl<GetTasksUseCase>().call(request, isSync: true);

      // The repository now handles caching, so no need to save here.
      // if (result.isSuccess && result.data != null) {
      //   await sl<HomeLocalDataSource>().saveTasks(result.data!);
      // }
    } catch (e) {
      // Handle or log error appropriately
    }
  }

  Future<void> _syncCalendar({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      final params = GetCalendarParams(token: token, userId: id);
      await sl<GetCalendarUseCase>().call(params, isSync: true);
    } catch (e) {
      // Handle or log error appropriately
    }
  }

  Future<void> _syncProfile({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetProfileUseCase>()
          .call(token: token, userId: id, isSync: true);
    } catch (e) {
      // Handle or log error appropriately
    }
  }

  Future<void> _syncMiscSetting({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetMiscSettingUseCase>().call(token: token, userId: id);
    } catch (e) {
      // Handle or log error appropriately
    }
  }

  /// Sequential photo and signature synchronization
  ///
  /// This method performs the following operations in sequence:
  /// 1. Upload photos API endpoint
  /// 2. Sync photos API endpoint (after successful photo upload)
  /// 3. Upload signatures API endpoint
  /// 4. Sync signatures API endpoint (after successful signature upload)
  Future<void> _syncPhotosAndSignatures({String? emulatedUserId}) async {
    try {
      // Get all tasks from local database
      final tasks = await _getAllTasksFromDatabase();

      if (tasks.isEmpty) {
        logger('No tasks found for photo and signature sync');
        return;
      }

      // Step 1 & 2: Upload and sync photos
      logger('🚀 Starting photo upload and sync workflow');
      final photoSyncResult = await sl<HomeRepository>().uploadAndSyncPhotos(
        tasks: tasks,
      );

      if (photoSyncResult.isSuccess) {
        logger('✅ Photo upload and sync completed successfully');
      } else {
        logger('❌ Photo upload and sync failed: ${photoSyncResult.error}');
      }

      // Step 3 & 4: Upload and sync signatures
      logger('🚀 Starting signature upload and sync workflow');
      final signatureSyncResult =
          await sl<HomeRepository>().uploadAndSyncSignatures(
        tasks: tasks,
      );

      if (signatureSyncResult.isSuccess) {
        logger('✅ Signature upload and sync completed successfully');
      } else {
        logger(
            '❌ Signature upload and sync failed: ${signatureSyncResult.error}');
      }
    } catch (e) {
      logger('❌ Error during photo and signature sync: $e');
    }
  }

  /// Get all tasks from the local database
  Future<List<TaskDetail>> _getAllTasksFromDatabase() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final taskModels = realm.all<TaskDetailModel>();

      // Convert models to entities
      final tasks =
          taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

      logger('Retrieved ${tasks.length} tasks from local database');
      return tasks;
    } catch (e) {
      logger('❌ Error retrieving tasks from database: $e');
      return [];
    }
  }

  /// Submit reports for tasks that need to be submitted
  ///
  /// This method performs the final step in the sync process by submitting
  /// completed task reports to the server using the new submit report batch API.
  Future<void> _submitReports({String? emulatedUserId}) async {
    try {
      // Filter tasks that need to be submitted (have sync pending status)
      final tasksToSubmit = SyncUtils.getTasksToSubmit();

      if (tasksToSubmit.isEmpty) {
        logger('No tasks require report submission');
        return;
      }

      logger('🚀 Starting report submission for ${tasksToSubmit.length} tasks');

      // Submit each task individually
      for (final task in tasksToSubmit) {
        try {
          // Create submit report request for this task
          final submitRequest = await SyncUtils.createSubmitReportRequest(
            task: task,
          );

          // Submit the report using the new batch API
          final submitResult = await sl<HomeRepository>().submitReportBatch(
            submitRequest,
          );

          if (submitResult.isSuccess) {
            logger('✅ Report submitted successfully for task ${task.taskId}');

            // Clear sync pending status for this task
            await _clearTaskSyncPending(task.taskId?.toString() ?? '');
          } else {
            logger(
                '❌ Report submission failed for task ${task.taskId}: ${submitResult.error}');
          }
        } catch (e) {
          logger('❌ Error submitting report for task ${task.taskId}: $e');
        }
      }

      logger('✅ Report submission process completed');
    } catch (e) {
      logger('❌ Error during report submission: $e');
    }
  }

  /// Clear sync pending status for a task
  Future<void> _clearTaskSyncPending(String taskId) async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return;
      }

      realm.write(() {
        task.syncPending = false;
        task.isSynced = true;
      });

      logger('Successfully cleared sync pending status for task $taskId');
    } catch (e) {
      logger('Error clearing sync pending status for task $taskId: $e');
    }
  }

  // Add new sync methods for emulation support
  Future<void> _syncHistory({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetHistoryUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing history: $e');
    }
  }

  Future<void> _syncAlerts({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetAlertsUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing alerts: $e');
    }
  }

  Future<void> _syncAvailability({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetAvailabilityUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing availability: $e');
    }
  }

  Future<void> _syncLeave({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetLeaveUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing leave: $e');
    }
  }

  Future<void> _syncSkills({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetSkillsUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing skills: $e');
    }
  }

  Future<void> _syncInduction({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetInductionUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing induction: $e');
    }
  }

  Future<void> _syncUpdatePos({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';
      final String deviceUid = await sl<DataManager>().getOrCreateDeviceId();

      // Create a basic request to sync POS data for the emulated user
      final updatePosRequest = UpdatePosRequestEntity(
        token: token,
        userId: int.tryParse(id),
        pos: [], // Empty array for sync request
        deviceuid: deviceUid,
      );

      await sl<UpdatePosUseCase>().call(updatePosRequest);
      logger('✅ POS sync completed for user: $id');
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing update pos: $e');
    }
  }
}
