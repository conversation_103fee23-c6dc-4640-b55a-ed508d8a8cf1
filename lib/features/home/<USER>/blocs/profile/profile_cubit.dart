import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/profile_response_entity.dart';
import '../../../domain/usecases/get_profile_usecase.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  final GetProfileUseCase _getProfileUseCase;

  ProfileCubit(this._getProfileUseCase) : super(ProfileInitial());

  Future<void> loadProfile({
    required String token,
    required String userId,
    bool isSync = false,
  }) async {
    emit(ProfileLoading());

    final result = await _getProfileUseCase.call(
      token: token,
      userId: userId,
      isSync: isSync,
    );

    if (result.isSuccess && result.data != null) {
      emit(ProfileLoaded(result.data!));
    } else {
      emit(ProfileError(result.error ?? 'Unknown error occurred'));
    }
  }

  void resetState() {
    emit(ProfileInitial());
  }
}
