import 'package:bloc/bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:storetrack_app/core/services/location_service.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_count_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_open_count_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/send_checkin_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/checkin_request_entity.dart';
import 'package:storetrack_app/shared/models/result.dart';

import 'dashboard_state.dart';

class DashboardCubit extends Cubit<DashboardState> {
  final GetTasksUseCase _unscheduleTaskUseCase;
  final GetOpenCountUseCase _getOpenCountUseCase;
  final SendCheckinUseCase _sendCheckinUseCase;

  // Using GetIt to access these services
  final LocationService _locationService = GetIt.instance<LocationService>();
  final DataManager _dataManager = GetIt.instance<DataManager>();

  DashboardCubit(
    this._unscheduleTaskUseCase,
    this._getOpenCountUseCase,
    this._sendCheckinUseCase,
  ) : super(DashboardInitial());

  Future<void> fetchDashboardData(TasksRequestEntity request) async {
    emit(DashboardLoading());

    try {
      // Get current location for open count API
      final position = await _locationService.getCurrentPosition();

      // If we have location, prepare to call both usecases simultaneously
      if (position != null) {
        final userId = await _dataManager.getUserId();
        final token = await _dataManager.getAuthToken();

        if (userId != null && token != null) {
          // Create request for open count
          final openCountRequest = OpenCountRequestEntity(
            latitude: position.latitude!,
            longitude: position.longitude!,
            userId: userId,
            token: token,
          );

          // Call both usecases simultaneously using Future.wait
          final results = await Future.wait([
            _unscheduleTaskUseCase(request),
            // _getOpenCountUseCase(openCountRequest),
          ]);

          final operationNames = ['Tasks', 'OpenCount'];

          if (results.hasFailures) {
            final errorMessage =
                results.getCombinedErrorMessage(operationNames);
            emit(DashboardError(errorMessage));
            return;
          }

          // All succeeded, extract data
          final tasksResponse = results[0].data as TasksResponseEntity;
          // final openTaskResponse = results[1].data as OpenTaskResponseEntity;
          var openTaskResponse = OpenTaskResponseEntity(
              data: DataModel(
            countTaken: 0,
            countAvailable: 0,
            radiusLimit: 0,
            otShow: true,
            countAutoschedule: 0,
          ));
          final counts = _calculateCounts(tasksResponse);

          emit(DashboardLoaded(
            response: tasksResponse,
            countUnscheduled: counts['unscheduled'] ?? 0,
            countScheduled: counts['scheduled'] ?? 0,
            countPos: counts['pos'] ?? 0,
            countCompleted: counts['completed'] ?? 0,
            countToday: counts['today'] ?? 0,
            openTaskResponse: openTaskResponse,
            countTaken: openTaskResponse.data.countTaken,
            countAvailable: openTaskResponse.data.countAvailable,
            countAutoschedule: openTaskResponse.data.countAutoschedule,
            otShow: openTaskResponse.data.otShow,
          ));
          return;
        }
      }

      // If location or user data not available, just fetch tasks data
      logger(
          'Location or user data not available, just fetch tasks data qwe123');
      final tasksResult = await _unscheduleTaskUseCase(request);

      if (!tasksResult.isSuccess) {
        final errorMessage = tasksResult.error?.toString() ??
            'Unknown error occurred while fetching dashboard data.';
        emit(DashboardError(errorMessage));
        return;
      }

      final tasksResponse = tasksResult.data as TasksResponseEntity;
      final counts = _calculateCounts(tasksResponse);

      emit(DashboardLoaded(
        response: tasksResponse,
        countUnscheduled: counts['unscheduled'] ?? 0,
        countScheduled: counts['scheduled'] ?? 0,
        countPos: counts['pos'] ?? 0,
        countCompleted: counts['completed'] ?? 0,
        countToday: counts['today'] ?? 0,
      ));
    } catch (e) {
      emit(DashboardError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  Map<String, int> _calculateCounts(TasksResponseEntity response) {
    // Initialize counts
    int countUnscheduled = 0;
    int countScheduled = 0;
    int countCompleted = 0;
    int countToday = 0;
    int countPos = 0;

    // Get today's date at midnight for comparison
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Extract all tasks from the response
    List<TaskDetail> allTasks = response.addTasks ?? [];

    for (var task in allTasks) {
      // Count unscheduled tasks
      if (task.taskStatus == "Tentative" &&
          task.taskId != 0 &&
          task.isOpen == false) {
        countUnscheduled++;
      }

      // Count scheduled tasks
      if (task.taskStatus == "Confirmed" && task.isOpen == false) {
        countScheduled++;

        // Check if task is scheduled for today
        final scheduledDate = task.scheduledTimeStamp;
        if (scheduledDate != null) {
          final taskDate = DateTime(
              scheduledDate.year, scheduledDate.month, scheduledDate.day);
          if (taskDate.isAtSameMomentAs(today)) {
            countToday++;
          }
        }
      }

      // Count completed tasks
      if ((task.taskStatus == "Successful" ||
          task.taskStatus == "Unsuccessful")) {
        countCompleted++;
      }

      // Count POS tasks
      if (task.posRequired == true) {
        countPos++;
      }
    }

    return {
      'unscheduled': countUnscheduled,
      'scheduled': countScheduled,
      'completed': countCompleted,
      'today': countToday,
      'pos': countPos,
    };
  }

  void resetState() {
    emit(DashboardInitial());
  }

  // Get current day status
  Future<bool> getDayStarted() async {
    return await _dataManager.getDayStarted();
  }

  // Start or end day with odometer reading
  Future<void> startEndDay({required int odometerReading}) async {
    emit(CheckinLoading());

    try {
      final userId = await _dataManager.getUserId();
      final token = await _dataManager.getAuthToken();

      if (userId == null || token == null) {
        emit(
            const CheckinError('Authentication required. Please login again.'));
        return;
      }

      final isDayStarted = await _dataManager.getDayStarted();
      final checkinType = isDayStarted ? 1 : 0; // 1 = End Day, 0 = Start Day

      final request = CheckinRequestEntity(
        userId: userId,
        token: token,
        checkinType: checkinType,
        checkinKm: odometerReading,
      );

      final result = await _sendCheckinUseCase(request);

      if (result.isSuccess && result.data != null) {
        String? elapsedTime;

        if (checkinType == 0) {
          // Starting day
          await _dataManager.saveDayStarted(true);
          final startTime = DateTime.now().millisecondsSinceEpoch;
          await _dataManager.saveStartTime(startTime);
        } else {
          // Ending day
          await _dataManager.saveDayStarted(false);
          final startTime = await _dataManager.getStartTime();
          if (startTime != null) {
            final endTime = DateTime.now().millisecondsSinceEpoch;
            elapsedTime = _formatElapsedTime(endTime - startTime);
          }
          await _dataManager.saveStartTime(0);
        }

        emit(CheckinSuccess(
          response: result.data!,
          isDayEnded: checkinType == 1,
          elapsedTime: elapsedTime,
        ));
      } else {
        emit(CheckinError(result.error ?? 'Failed to send checkin'));
      }
    } catch (e) {
      emit(CheckinError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  // Force end previous day session
  Future<void> forceEndPreviousDay() async {
    emit(CheckinLoading());

    try {
      final userId = await _dataManager.getUserId();
      final token = await _dataManager.getAuthToken();

      if (userId == null || token == null) {
        emit(
            const CheckinError('Authentication required. Please login again.'));
        return;
      }

      final request = CheckinRequestEntity(
        userId: userId,
        token: token,
        checkinType: 1, // End Day
        checkinKm: 0, // 0 km for forced end
      );

      final result = await _sendCheckinUseCase(request);

      if (result.isSuccess && result.data != null) {
        await _dataManager.saveDayStarted(false);
        await _dataManager.saveStartTime(0);

        emit(CheckinSuccess(
          response: result.data!,
          isDayEnded: true,
          elapsedTime: null,
        ));
      } else {
        emit(CheckinError(result.error ?? 'Failed to end previous session'));
      }
    } catch (e) {
      emit(CheckinError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  // Check if start time is from today (same as Android logic)
  // Returns true if start time is TODAY → Normal end day flow
  // Returns false if start time is YESTERDAY or earlier → Forgot to end session flow
  Future<bool> isStartTimeToday() async {
    final startTime = await _dataManager.getStartTime();
    if (startTime == null) return false;

    final startDate = DateTime.fromMillisecondsSinceEpoch(startTime);
    final today = DateTime.now();

    // Compare only date parts (year, month, day) - same as Android's dateFormat.format()
    return startDate.year == today.year &&
        startDate.month == today.month &&
        startDate.day == today.day;
  }

  // Format elapsed time in human-readable format
  String _formatElapsedTime(int elapsedMilliseconds) {
    final duration = Duration(milliseconds: elapsedMilliseconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  // Get formatted start time
  Future<String?> getFormattedStartTime() async {
    final startTime = await _dataManager.getStartTime();
    if (startTime == null) return null;

    final startDate = DateTime.fromMillisecondsSinceEpoch(startTime);
    return '${startDate.day}/${startDate.month}/${startDate.year} ${startDate.hour.toString().padLeft(2, '0')}:${startDate.minute.toString().padLeft(2, '0')}';
  }
}
