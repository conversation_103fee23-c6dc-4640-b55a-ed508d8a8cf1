import 'package:storetrack_app/features/home/<USER>/entities/create_task_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/models/coverage_store_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/coverage_client_model.dart';

abstract class CreateTaskState {
  const CreateTaskState();
}

class CreateTaskInitial extends CreateTaskState {}

class CreateTaskLoading extends CreateTaskState {}

class CreateTaskSuccess extends CreateTaskState {
  final CreateTaskResponseEntity response;

  const CreateTaskSuccess(this.response);
}

class CreateTaskError extends CreateTaskState {
  final String message;

  const CreateTaskError(this.message);
}

class CoverageStoresLoaded extends CreateTaskState {
  final CoverageStoresResponse response;

  const CoverageStoresLoaded(this.response);
}

class CoverageClientsLoaded extends CreateTaskState {
  final CoverageClientsResponse response;

  const CoverageClientsLoaded(this.response);
}
