import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/create_task_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/create_task_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/create_task_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_coverage_stores_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_coverage_clients_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import 'create_task_state.dart';

class CreateTaskCubit extends Cubit<CreateTaskState> {
  final CreateTaskUseCase _createTaskUseCase;
  final GetCoverageStoresUseCase _getCoverageStoresUseCase;
  final GetCoverageClientsUseCase _getCoverageClientsUseCase;

  CreateTaskCubit(
    this._createTaskUseCase,
    this._getCoverageStoresUseCase,
    this._getCoverageClientsUseCase,
  ) : super(CreateTaskInitial());

  Future<void> createTasks(CreateTaskRequestEntity request) async {
    emit(CreateTaskLoading());

    try {
      final Result<CreateTaskResponseEntity> result =
          await _createTaskUseCase(request);

      if (result.isSuccess && result.data != null) {
        emit(CreateTaskSuccess(result.data!));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while creating tasks.';
        emit(CreateTaskError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in CreateTaskCubit: $e");
      emit(CreateTaskError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  void reset() {
    emit(CreateTaskInitial());
  }

  Future<void> fetchCoverageStores({
    required String userId,
    required String token,
  }) async {
    // Don't emit loading for data fetching operations to avoid conflicts
    try {
      logger("🔄 Fetching coverage stores for userId: $userId");
      final params = GetCoverageStoresParams(userId: userId, token: token);
      final result = await _getCoverageStoresUseCase(params);

      if (result.isSuccess && result.data != null) {
        logger(
            "✅ Coverage stores loaded successfully: ${result.data!.data?.coverageStores.length ?? 0} stores");
        emit(CoverageStoresLoaded(result.data!));
      } else {
        final errorMessage =
            result.error?.toString() ?? 'Failed to fetch coverage stores.';
        logger("❌ Error fetching coverage stores: $errorMessage");
        emit(CreateTaskError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in fetchCoverageStores: $e");
      emit(CreateTaskError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  Future<void> fetchCoverageClients({
    required String userId,
    required String token,
  }) async {
    // Don't emit loading for data fetching operations to avoid conflicts
    try {
      logger("🔄 Fetching coverage clients for userId: $userId");
      final params = GetCoverageClientsParams(userId: userId, token: token);
      final result = await _getCoverageClientsUseCase(params);

      if (result.isSuccess && result.data != null) {
        logger(
            "✅ Coverage clients loaded successfully: ${result.data!.data?.coverageClients.length ?? 0} clients");
        emit(CoverageClientsLoaded(result.data!));
      } else {
        final errorMessage =
            result.error?.toString() ?? 'Failed to fetch coverage clients.';
        logger("❌ Error fetching coverage clients: $errorMessage");
        emit(CreateTaskError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in fetchCoverageClients: $e");
      emit(CreateTaskError('An unexpected error occurred: ${e.toString()}'));
    }
  }
}
