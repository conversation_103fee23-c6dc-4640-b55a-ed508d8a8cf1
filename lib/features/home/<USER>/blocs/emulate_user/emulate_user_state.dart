part of 'emulate_user_cubit.dart';

abstract class EmulateUserState extends Equatable {
  const EmulateUserState();

  @override
  List<Object?> get props => [];
}

class EmulateUserInitial extends EmulateUserState {}

class EmulateUserLoading extends EmulateUserState {}

class EmulateUserLoaded extends EmulateUserState {
  final List<EmulateUserEntity> users;
  final List<EmulateUserEntity> filteredUsers;
  final String searchQuery;

  const EmulateUserLoaded({
    required this.users,
    required this.filteredUsers,
    this.searchQuery = '',
  });

  @override
  List<Object?> get props => [users, filteredUsers, searchQuery];

  EmulateUserLoaded copyWith({
    List<EmulateUserEntity>? users,
    List<EmulateUserEntity>? filteredUsers,
    String? searchQuery,
  }) {
    return EmulateUserLoaded(
      users: users ?? this.users,
      filteredUsers: filteredUsers ?? this.filteredUsers,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class EmulateUserError extends EmulateUserState {
  final String message;

  const EmulateUserError(this.message);

  @override
  List<Object?> get props => [message];
}

class EmulateUserEmulating extends EmulateUserState {
  final EmulateUserEntity user;
  final String currentApi;
  final int progress; // 0-10

  const EmulateUserEmulating({
    required this.user,
    required this.currentApi,
    required this.progress,
  });

  @override
  List<Object> get props => [user, currentApi, progress];
}

class EmulateUserEmulationSuccess extends EmulateUserState {
  final EmulateUserEntity user;
  final Map<String, bool> apiResults; // API name -> success/failure

  const EmulateUserEmulationSuccess({
    required this.user,
    required this.apiResults,
  });

  @override
  List<Object> get props => [user, apiResults];
}

class EmulateUserEmulationError extends EmulateUserState {
  final String message;
  final EmulateUserEntity? user;

  const EmulateUserEmulationError(this.message, {this.user});

  @override
  List<Object?> get props => [message, user];
}
