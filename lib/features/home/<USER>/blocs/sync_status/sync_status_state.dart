import 'package:equatable/equatable.dart';

enum SyncPhase {
  idle,
  photos,
  signatures,
  reports,
  tasks,
  calendar,
  profile,
  miscSettings,
  completed,
}

enum SyncStatus {
  idle,
  syncing,
  success,
  error,
  offline,
}

class SyncStatusState extends Equatable {
  final SyncStatus status;
  final SyncPhase currentPhase;
  final String? errorMessage;
  final DateTime? lastSyncTime;
  final bool isSmartSyncEnabled;
  final int retryCount;
  final Duration? timeSinceLastSync;
  final bool shouldSync;

  const SyncStatusState({
    this.status = SyncStatus.idle,
    this.currentPhase = SyncPhase.idle,
    this.errorMessage,
    this.lastSyncTime,
    this.isSmartSyncEnabled = true,
    this.retryCount = 0,
    this.timeSinceLastSync,
    this.shouldSync = true,
  });

  SyncStatusState copyWith({
    SyncStatus? status,
    SyncPhase? currentPhase,
    String? errorMessage,
    DateTime? lastSyncTime,
    bool? isSmartSyncEnabled,
    int? retryCount,
    Duration? timeSinceLastSync,
    bool? shouldSync,
  }) {
    return SyncStatusState(
      status: status ?? this.status,
      currentPhase: currentPhase ?? this.currentPhase,
      errorMessage: errorMessage ?? this.errorMessage,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      isSmartSyncEnabled: isSmartSyncEnabled ?? this.isSmartSyncEnabled,
      retryCount: retryCount ?? this.retryCount,
      timeSinceLastSync: timeSinceLastSync ?? this.timeSinceLastSync,
      shouldSync: shouldSync ?? this.shouldSync,
    );
  }

  @override
  List<Object?> get props => [
        status,
        currentPhase,
        errorMessage,
        lastSyncTime,
        isSmartSyncEnabled,
        retryCount,
        timeSinceLastSync,
        shouldSync,
      ];

  bool get isSyncing => status == SyncStatus.syncing;
  bool get hasError => status == SyncStatus.error;
  bool get isOffline => status == SyncStatus.offline;
  bool get isSuccess => status == SyncStatus.success;

  String get phaseDisplayName {
    switch (currentPhase) {
      case SyncPhase.idle:
        return 'Ready';
      case SyncPhase.photos:
        return 'Syncing Photos';
      case SyncPhase.signatures:
        return 'Syncing Signatures';
      case SyncPhase.reports:
        return 'Submitting Reports';
      case SyncPhase.tasks:
        return 'Downloading Tasks';
      case SyncPhase.calendar:
        return 'Syncing Calendar';
      case SyncPhase.profile:
        return 'Updating Profile';
      case SyncPhase.miscSettings:
        return 'Syncing Settings';
      case SyncPhase.completed:
        return 'Sync Complete';
    }
  }
}
