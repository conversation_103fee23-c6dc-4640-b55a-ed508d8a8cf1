import 'package:flutter/material.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';

class AllTasksList extends StatelessWidget {
  final List<schedule.TaskDetail> allApiTasks;
  final bool isCheckboxMode;
  final bool areAllItemsSelected;
  final Function(List<TaskDetail>, List<TaskDetail>) onSelectionChanged;
  final TaskDetail Function(schedule.TaskDetail) convertScheduleToDatum;
  final void Function(TaskDetail task)? onTaskTap;

  const AllTasksList({
    super.key,
    required this.allApiTasks,
    required this.isCheckboxMode,
    required this.areAllItemsSelected,
    required this.onSelectionChanged,
    required this.convertScheduleToDatum,
    this.onTaskTap,
  });

  @override
  Widget build(BuildContext context) {
    // For the "All" tab, show all scheduled tasks without date filtering
    List<schedule.TaskDetail> scheduledTasks = allApiTasks
        .where((task) =>
            task.taskStatus == "Confirmed" &&
            task.isOpen == false &&
            task.scheduledTimeStamp != null)
        .toList()
      // Sort by scheduled date and time
      ..sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));

    if (scheduledTasks.isEmpty) {
      return const EmptyState(message: 'No scheduled tasks available');
    }

    List<TaskDetail> tasksToShow =
        scheduledTasks.map((task) => convertScheduleToDatum(task)).toList();

    // For menu button, show the regular list with all tasks
    return ReorderableStoreList(
      tasks: tasksToShow,
      isCalendarMode: isCheckboxMode,
      showScheduledDate: true, // Show date in All tab
      showTickIndicator: true,
      showAllDisclosureIndicator: false,
      permanentlyDisableAllDisclosureIndicator: false,
      isOpenTask: true,
      onSelectionChanged: onSelectionChanged,
      selectAll: areAllItemsSelected,
      onTaskTap: onTaskTap,
    );
  }
}
