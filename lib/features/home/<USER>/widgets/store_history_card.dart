import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/previous_tasks_response_entity.dart';

class StoreHistoryCard extends StatelessWidget {
  final PreviousTaskEntity task;
  final VoidCallback? onTap;

  const StoreHistoryCard({
    super.key,
    required this.task,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        border: Border.all(
          color: AppColors.borderColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding + 4),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Cycle Name
                      Text(
                        task.cycleName ?? 'Unknown Cycle',
                        style: Theme.of(context)
                            .textTheme
                            .montserratTitleSmall
                            .copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      const Gap(12),

                      // Completed By
                      _buildInfoRow(
                        context,
                        'Completed By:',
                        task.completedBy ?? 'N/A',
                      ),
                      const Gap(6),

                      // Date Scheduled
                      _buildInfoRow(
                        context,
                        'Date Scheduled:',
                        task.dateSchedule != null
                            ? dateFormat.format(task.dateSchedule!)
                            : 'N/A',
                      ),
                      const Gap(6),

                      // Task Comment
                      _buildInfoRow(
                        context,
                        'Comment:',
                        task.taskComment ?? 'No comment',
                      ),
                    ],
                  ),
                ),
                const Gap(12),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.blackTint1,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style:
                Theme.of(context).textTheme.montserratParagraphSmall.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.blackTint1,
                    ),
          ),
        ),
        const Gap(12),
        Expanded(
          child: Text(
            value,
            style:
                Theme.of(context).textTheme.montserratParagraphSmall.copyWith(
                      color: AppColors.black,
                      fontWeight: FontWeight.w400,
                    ),
          ),
        ),
      ],
    );
  }
}
