import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/vacancy_entity.dart';

class VacancyCard extends StatelessWidget {
  final VacancyEntity vacancy;
  final VoidCallback? onApply;
  final VoidCallback? onRefer;
  final bool isApplying;
  final bool isReferring;

  const VacancyCard({
    super.key,
    required this.vacancy,
    this.onApply,
    this.onRefer,
    this.isApplying = false,
    this.isReferring = false,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.borderColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            // TODO: Navigate to vacancy details if needed
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Job Title
                Text(
                  vacancy.jobTitle,
                  style: textTheme.montserratTitleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.black,
                  ),
                ),
                const Gap(12),

                // Location and Salary
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.location_on_outlined,
                      size: 16,
                      color: AppColors.blackTint1,
                    ),
                    const Gap(6),
                    Expanded(
                      child: Text(
                        vacancy.jobLocation,
                        style: textTheme.montserratParagraphSmall.copyWith(
                          color: AppColors.blackTint1,
                          fontSize: 13,
                        ),
                      ),
                    ),
                    if (vacancy.salaryRange != null) ...[
                      const Gap(16),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.lightGrey2,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          vacancy.salaryRange!,
                          style: textTheme.montserratTitleXxsmall.copyWith(
                            color: AppColors.primaryBlue,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),

                const Gap(16),

                // Job Description
                Text(
                  vacancy.jobDescription,
                  style: textTheme.montserratParagraphSmall.copyWith(
                    color: AppColors.secondaryTextColor,
                    height: 1.5,
                    fontSize: 14,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),

                const Gap(20),

                // Action Buttons
                Row(
                  children: [
                    // Apply Button
                    if (vacancy.canApply)
                      Expanded(
                        child: _buildActionButton(
                          context: context,
                          text: 'Apply',
                          backgroundColor: AppColors.loginGreen,
                          isLoading: isApplying,
                          onPressed: isApplying || isReferring ? null : onApply,
                        ),
                      ),
                    if (vacancy.canApply && vacancy.canRefer) const Gap(12),

                    // Refer Button
                    if (vacancy.canRefer)
                      Expanded(
                        child: _buildActionButton(
                          context: context,
                          text: 'Refer',
                          backgroundColor: AppColors.primaryBlue,
                          isLoading: isReferring,
                          onPressed: isApplying || isReferring ? null : onRefer,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required String text,
    required Color backgroundColor,
    required bool isLoading,
    required VoidCallback? onPressed,
  }) {
    return Container(
      height: 44,
      decoration: BoxDecoration(
        color: onPressed != null ? backgroundColor : AppColors.blackTint2,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: onPressed,
          child: Center(
            child: isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    text,
                    style: Theme.of(context)
                        .textTheme
                        .montserratTitleExtraSmall
                        .copyWith(
                          color: onPressed != null
                              ? Colors.white
                              : AppColors.blackTint1,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
          ),
        ),
      ),
    );
  }
}
