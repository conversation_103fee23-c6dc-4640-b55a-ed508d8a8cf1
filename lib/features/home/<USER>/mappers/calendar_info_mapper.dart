import 'package:storetrack_app/features/home/<USER>/models/calendar_info_model.dart';

import '../../domain/entities/calendar_response_entity.dart';

class CalendarInfoMapper {
  static CalendarInfoModel toModel(CalendarInfo entity, int id) {
    return CalendarInfoModel(
      id,
      timestamp: entity.timestamp,
      dollarSymbol: entity.dollarSymbol,
      publicHoliday: entity.publicHoliday,
      budgetAmount: entity.budgetAmount?.toDouble(),
    );
  }

  static CalendarInfo toEntity(CalendarInfoModel model) {
    return CalendarInfo(
      timestamp: model.timestamp?.toLocal(),
      dollarSymbol: model.dollarSymbol,
      publicHoliday: model.publicHoliday,
      budgetAmount: model.budgetAmount,
    );
  }
}
