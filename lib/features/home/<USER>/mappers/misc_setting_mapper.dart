import '../models/misc_setting_model.dart';
import '../../domain/entities/misc_setting_response_entity.dart';

class MiscSettingMapper {
  static MiscSettingModel toModel(MiscSettingResponseEntity entity) {
    return MiscSettingModel(
      0, // Single misc setting entry
      reportEarlyHours: entity.data.reportEarlyHours.toString(),
      reportLateHours: entity.data.reportLateHours.toString(),
      radiusDistanceInMeters: entity.data.radiusDistanceInMeters.toString(),
      imageMinWidthThreshold: entity.data.imageMinWidthThreshold.toString(),
    );
  }

  static MiscSettingResponseEntity toEntity(MiscSettingModel model) {
    return MiscSettingResponseEntity(
      data: MiscSettingData(
        reportEarlyHours: int.tryParse(model.reportEarlyHours ?? '0') ?? 0,
        reportLateHours: int.tryParse(model.reportLateHours ?? '0') ?? 0,
        radiusDistanceInMeters:
            int.tryParse(model.radiusDistanceInMeters ?? '0') ?? 0,
        imageMinWidthThreshold:
            int.tryParse(model.imageMinWidthThreshold ?? '0') ?? 0,
      ),
    );
  }
}
