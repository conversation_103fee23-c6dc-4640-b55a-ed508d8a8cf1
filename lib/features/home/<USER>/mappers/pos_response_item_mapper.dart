import 'package:storetrack_app/features/home/<USER>/models/pos_response_item_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/pos_response_entity.dart';

class PosResponseItemMapper {
  static PosResponseItemModel toModel(Pos entity, int id) {
    return PosResponseItemModel(
      id,
      isSynced: false,
      taskId: entity.taskId,
      storeName: entity.storeName,
      clientName: entity.clientName,
      cycle: entity.cycle,
      rangeStart: entity.rangeStart,
      rangeEnd: entity.rangeEnd,
      scheduledDate: entity.scheduledDate,
      received: entity.received,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  static Pos toEntity(PosResponseItemModel model) {
    return Pos(
      taskId: model.taskId,
      storeName: model.storeName,
      clientName: model.clientName,
      cycle: model.cycle,
      rangeStart: model.rangeStart?.toLocal(),
      rangeEnd: model.rangeEnd?.toLocal(),
      scheduledDate: model.scheduledDate?.toLocal(),
      received: model.received,
    );
  }

  static List<PosResponseItemModel> toModelList(List<Pos> entities) {
    return entities.asMap().entries.map((entry) {
      final index = entry.key;
      final entity = entry.value;
      // Generate unique ID based on taskId and index
      final id = (entity.taskId ?? 0) * 1000 + index;
      return toModel(entity, id);
    }).toList();
  }

  static List<Pos> toEntityList(List<PosResponseItemModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }
}
