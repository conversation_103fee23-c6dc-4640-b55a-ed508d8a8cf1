import '../models/history_model.dart';
import '../models/history_response.dart';

class HistoryMapper {
  static HistoryModel toModel(HistoryResponse response) {
    // Convert response history items to history item models
    final historyModels = response.data?.history
            ?.map((item) => HistoryItemModel(
                  storeId: item.storeId,
                  storeName: item.storeName,
                  clientName: item.clientName,
                  cycle: item.cycle,
                  budget: item.budget,
                  minutes: item.minutes,
                  scheduledDate: item.scheduledDate,
                  latitude: item.latitude,
                  longitude: item.longitude,
                  forms: item.forms,
                  photos: item.photos,
                  unacceptedReasonId: item.unacceptedReasonId,
                  unacceptedReason: item.unacceptedReason,
                ))
            .toList() ??
        [];

    return HistoryModel(
      "history",
      history: historyModels,
    );
  }

  static HistoryResponse toEntity(HistoryModel model) {
    // Convert history item models back to response format
    final historyItems = model.history
        .map((historyModel) => HistoryItem(
              storeId: historyModel.storeId,
              storeName: historyModel.storeName,
              clientName: historyModel.clientName,
              cycle: historyModel.cycle,
              budget: historyModel.budget,
              minutes: historyModel.minutes,
              scheduledDate: historyModel.scheduledDate,
              latitude: historyModel.latitude,
              longitude: historyModel.longitude,
              forms: historyModel.forms,
              photos: historyModel.photos,
              unacceptedReasonId: historyModel.unacceptedReasonId,
              unacceptedReason: historyModel.unacceptedReason,
            ))
        .toList();

    return HistoryResponse(
      data: HistoryData(history: historyItems),
    );
  }

  static HistoryModel updateModel({
    required HistoryModel existingModel,
    required HistoryResponse response,
  }) {
    // Clear existing history and add new ones
    existingModel.history.clear();

    final historyModels = response.data?.history
            ?.map((item) => HistoryItemModel(
                  storeId: item.storeId,
                  storeName: item.storeName,
                  clientName: item.clientName,
                  cycle: item.cycle,
                  budget: item.budget,
                  minutes: item.minutes,
                  scheduledDate: item.scheduledDate,
                  latitude: item.latitude,
                  longitude: item.longitude,
                  forms: item.forms,
                  photos: item.photos,
                  unacceptedReasonId: item.unacceptedReasonId,
                  unacceptedReason: item.unacceptedReason,
                ))
            .toList() ??
        [];

    existingModel.history.addAll(historyModels);
    return existingModel;
  }
}
