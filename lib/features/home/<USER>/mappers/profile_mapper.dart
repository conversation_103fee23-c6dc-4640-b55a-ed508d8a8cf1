import '../models/profile_model.dart';
import '../../domain/entities/profile_response_entity.dart';

class ProfileMapper {
  static ProfileModel toModel(ProfileResponseEntity entity) {
    return ProfileModel(
      0, // Single profile entry
      token: entity.data?.token,
      userId: entity.data?.userId,
      contractorId: entity.data?.contractorId,
      countryId: entity.data?.countryId,
      stateId: entity.data?.stateId,
      firstName: entity.data?.firstName,
      lastName: entity.data?.lastName,
      address: entity.data?.address,
      email: entity.data?.email,
      country: entity.data?.country,
      state: entity.data?.state,
      suburb: entity.data?.suburb,
      postcode: entity.data?.postcode,
      pAddress: entity.data?.pAddress,
      pSuburb: entity.data?.pSuburb,
      pPostcode: entity.data?.pPostcode,
      pCountryId: entity.data?.pCountryId,
      pCountry: entity.data?.pCountry,
      pRegion: entity.data?.pRegion,
      pRegionId: entity.data?.pRegionId,
      pDeliveryComment: entity.data?.pDeliveryComment,
      mobile: entity.data?.mobile,
      profileImageUrl: entity.data?.profileImageUrl,
      modifiedTimeStampProfile: entity.data?.modifiedTimeStampProfile,
      adminAccess: entity.data?.adminAccess,
      createTask: entity.data?.createTask,
      orgIDs: entity.data?.orgIDs,
    );
  }

  static ProfileResponseEntity toEntity(ProfileModel model) {
    return ProfileResponseEntity(
      data: ProfileData(
        token: model.token,
        userId: model.userId,
        contractorId: model.contractorId,
        countryId: model.countryId,
        stateId: model.stateId,
        firstName: model.firstName,
        lastName: model.lastName,
        address: model.address,
        email: model.email,
        country: model.country,
        state: model.state,
        suburb: model.suburb,
        postcode: model.postcode,
        pAddress: model.pAddress,
        pSuburb: model.pSuburb,
        pPostcode: model.pPostcode,
        pCountryId: model.pCountryId,
        pCountry: model.pCountry,
        pRegion: model.pRegion,
        pRegionId: model.pRegionId,
        pDeliveryComment: model.pDeliveryComment,
        mobile: model.mobile,
        profileImageUrl: model.profileImageUrl,
        modifiedTimeStampProfile: model.modifiedTimeStampProfile,
        adminAccess: model.adminAccess,
        createTask: model.createTask,
        orgIDs: model.orgIDs,
      ),
    );
  }
}
