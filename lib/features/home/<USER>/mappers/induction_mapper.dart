import '../models/induction_model.dart';
import '../models/induction_response.dart';

class InductionMapper {
  static InductionModel toModel(InductionResponse response) {
    // Convert response inductions to induction item models
    final inductionModels = response.data?.inductions
            ?.map((induction) => InductionItemModel(
                  inductionId: induction.inductionId,
                  inductionCategoryId: induction.inductionCategoryId,
                  inductionName: induction.inductionName,
                  inductionCategory: induction.inductionCategory,
                  has: induction.has,
                  isEdit: induction.isEdit,
                ))
            .toList() ??
        [];

    return InductionModel(
      "induction",
      inductions: inductionModels,
    );
  }

  static InductionResponse toEntity(InductionModel model) {
    // Convert induction item models back to response format
    final inductions = model.inductions
        .map((inductionModel) => InductionItem(
              inductionId: inductionModel.inductionId,
              inductionCategoryId: inductionModel.inductionCategoryId,
              inductionName: inductionModel.inductionName,
              inductionCategory: inductionModel.inductionCategory,
              has: inductionModel.has,
              isEdit: inductionModel.isEdit,
            ))
        .toList();

    return InductionResponse(
      data: InductionData(inductions: inductions),
    );
  }

  static InductionModel updateModel({
    required InductionModel existingModel,
    required InductionResponse response,
  }) {
    // Clear existing inductions and add new ones
    existingModel.inductions.clear();

    final inductionModels = response.data?.inductions
            ?.map((induction) => InductionItemModel(
                  inductionId: induction.inductionId,
                  inductionCategoryId: induction.inductionCategoryId,
                  inductionName: induction.inductionName,
                  inductionCategory: induction.inductionCategory,
                  has: induction.has,
                  isEdit: induction.isEdit,
                ))
            .toList() ??
        [];

    existingModel.inductions.addAll(inductionModels);
    return existingModel;
  }
}
