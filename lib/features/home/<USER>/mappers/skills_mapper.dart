import '../models/skills_model.dart';
import '../models/skills_response.dart';

class SkillsMapper {
  static SkillsModel toModel(SkillsResponse response) {
    // Convert response skills to skill item models
    final skillModels = response.data?.skills
            .map((skill) => SkillItemModel(
                  skill.skillId,
                  skill.skillName,
                  skill.skillDescription,
                  skill.has,
                ))
            .toList() ??
        [];

    return SkillsModel(
      "skills",
      skills: skillModels,
    );
  }

  static SkillsResponse toEntity(SkillsModel model) {
    // Convert skill item models back to response format
    final skills = model.skills
        .map((skillModel) => Skill(
              skillId: skillModel.skillId,
              skillName: skillModel.skillName,
              skillDescription: skillModel.skillDescription,
              has: skillModel.has,
            ))
        .toList();

    return SkillsResponse(
      data: SkillsData(skills: skills),
    );
  }

  static SkillsModel updateModel({
    required SkillsModel existingModel,
    required SkillsResponse response,
  }) {
    // Clear existing skills and add new ones
    existingModel.skills.clear();

    final skillModels = response.data?.skills
            .map((skill) => SkillItemModel(
                  skill.skillId,
                  skill.skillName,
                  skill.skillDescription,
                  skill.has,
                ))
            .toList() ??
        [];

    existingModel.skills.addAll(skillModels);
    return existingModel;
  }
}
