import '../models/leave_model.dart';
import '../models/leave_response.dart';

class LeaveMapper {
  static LeaveModel toModel(LeaveResponse response) {
    // Convert response leaves to leave item models
    final leaveModels = response.data?.leaves
            .map((leave) => LeaveItemModel(
                  leaveId: leave.leaveId,
                  leaveDate: leave.leaveDate,
                  leaveTitle: leave.leaveTitle,
                  leaveDescription: leave.leaveDescription,
                  isSelected: leave.isSelected,
                ))
            .toList() ??
        [];

    return LeaveModel(
      "leave",
      leaves: leaveModels,
    );
  }

  static LeaveResponse toEntity(LeaveModel model) {
    // Convert leave item models back to response format
    final leaves = model.leaves
        .map((leaveModel) => Leave(
              leaveId: leaveModel.leaveId,
              leaveDate: leaveModel.leaveDate,
              leaveTitle: leaveModel.leaveTitle,
              leaveDescription: leaveModel.leaveDescription,
              isSelected: leaveModel.isSelected,
            ))
        .toList();

    return LeaveResponse(
      data: LeaveData(leaves: leaves),
    );
  }

  static LeaveModel updateModel({
    required LeaveModel existingModel,
    required LeaveResponse response,
  }) {
    // Clear existing leaves and add new ones
    existingModel.leaves.clear();

    final leaveModels = response.data?.leaves
            .map((leave) => LeaveItemModel(
                  leaveId: leave.leaveId,
                  leaveDate: leave.leaveDate,
                  leaveTitle: leave.leaveTitle,
                  leaveDescription: leave.leaveDescription,
                  isSelected: leave.isSelected,
                ))
            .toList() ??
        [];

    existingModel.leaves.addAll(leaveModels);
    return existingModel;
  }
}
