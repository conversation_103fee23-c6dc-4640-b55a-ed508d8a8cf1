import '../models/availability_model.dart';
import '../models/availability_response.dart';

class AvailabilityMapper {
  static AvailabilityModel toModel(AvailabilityResponse response) {
    // Convert response days to day availability models
    final dayModels = response.data?.days.map((day) {
          final daySpanModels = day.daySpans
              .map((span) => DaySpanModel(
                    span.dayEntryNumber,
                    span.startHour,
                    span.endHour,
                  ))
              .toList();

          return DayAvailabilityModel(
            day.dayNumber,
            day.dayOrder,
            day.dayDescription,
            daySpans: daySpanModels,
          );
        }).toList() ??
        [];

    return AvailabilityModel(
      "availability",
      days: dayModels,
    );
  }

  static AvailabilityResponse toEntity(AvailabilityModel model) {
    // Convert model days back to response format
    final days = model.days.map((dayModel) {
      final daySpans = dayModel.daySpans
          .map((spanModel) => DaySpan(
                dayEntryNumber: spanModel.dayEntryNumber,
                startHour: spanModel.startHour,
                endHour: spanModel.endHour,
              ))
          .toList();

      return DayAvailability(
        dayNumber: dayModel.dayNumber,
        dayOrder: dayModel.dayOrder,
        dayDescription: dayModel.dayDescription,
        daySpans: daySpans,
      );
    }).toList();

    return AvailabilityResponse(
      data: AvailabilityData(days: days),
    );
  }

  static AvailabilityModel updateModel({
    required AvailabilityModel existingModel,
    required AvailabilityResponse response,
  }) {
    // Clear existing days and add new ones
    existingModel.days.clear();

    final dayModels = response.data?.days.map((day) {
          final daySpanModels = day.daySpans
              .map((span) => DaySpanModel(
                    span.dayEntryNumber,
                    span.startHour,
                    span.endHour,
                  ))
              .toList();

          return DayAvailabilityModel(
            day.dayNumber,
            day.dayOrder,
            day.dayDescription,
            daySpans: daySpanModels,
          );
        }).toList() ??
        [];

    existingModel.days.addAll(dayModels);
    return existingModel;
  }
}
