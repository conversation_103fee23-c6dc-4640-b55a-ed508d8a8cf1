import 'package:storetrack_app/core/utils/logger.dart';

import '../../domain/entities/tasks_response_entity.dart';
import '../models/task_detail_model.dart';

class TaskDetailMapper {
  static TaskDetailModel toModel(TaskDetail entity, int id) {
    logger(
        'qwe122 toModel: ${entity.scheduledTimeStamp} ${entity.scheduledTimeStamp?.toLocal()} ${entity.storeName} ${entity.scheduledTimeStamp?.timeZoneName} ${entity.scheduledTimeStamp?.toLocal().timeZoneName} ${entity.scheduledTimeStamp?.toUtc()} ${entity.scheduledTimeStamp?.toUtc().timeZoneName}');

    return TaskDetailModel(
      id,
      isSynced: false,
      syncPending: entity.syncPending ?? false,
      taskId: entity.taskId?.toInt(),
      projectId: entity.projectId?.toInt(),
      scheduleId: entity.scheduleId?.toInt(),
      sentToPayroll: entity.sentToPayroll,
      showKm: entity.showKm,
      storeId: entity.storeId?.toInt(),
      client: entity.client,
      clientId: entity.clientId?.toInt(),
      clientLogoUrl: entity.clientLogoUrl,
      storeGroup: entity.storeGroup,
      storeGroupId: entity.storeGroupId?.toInt(),
      storeName: entity.storeName,
      storeEmail: entity.storeEmail,
      minutes: entity.minutes?.toInt(),
      budget: entity.budget?.toInt(),
      originalbudget: entity.originalbudget?.toInt(),
      comment: entity.comment,
      claimableKms: entity.claimableKms?.toInt(),
      flightDuration: entity.flightDuration?.toInt(),
      pages: entity.pages?.toInt(),
      location: entity.location,
      suburb: entity.suburb,
      latitude: entity.latitude?.toDouble(),
      longitude: entity.longitude?.toDouble(),
      taskLatitude: entity.taskLatitude?.toDouble(),
      taskLongitude: entity.taskLongitude?.toDouble(),
      cycle: entity.cycle,
      cycleId: entity.cycleId?.toInt(),
      canDelete: entity.canDelete,
      scheduledTimeStamp: entity.scheduledTimeStamp,
      submissionTimeStamp: entity.submissionTimeStamp,
      expires: entity.expires,
      onTask: entity.onTask,
      phone: entity.phone,
      rangeStart: entity.rangeStart,
      rangeEnd: entity.rangeEnd,
      reOpened: entity.reOpened,
      reOpenedReason: entity.reOpenedReason,
      taskStatus: entity.taskStatus,
      warehousejobId: entity.warehousejobId?.toInt(),
      connoteUrl: entity.connoteUrl,
      posRequired: entity.posRequired,
      isPosMandatory: entity.isPosMandatory,
      posReceived: entity.posReceived,
      photoFolder: _mapPhotoFolders(entity.photoFolder),
      signatureFolder: _mapSignatureFolders(entity.signatureFolder),
      forms: _mapForms(entity.forms),
      posItems: _mapPosItems(entity.posItems),
      documents: _mapDocuments(entity.documents),
      taskalerts: _mapTaskalerts(entity.taskalerts),
      taskmembers: _mapTaskmembers(entity.taskmembers),
      modifiedTimeStampDocuments: entity.modifiedTimeStampDocuments,
      modifiedTimeStampForms: entity.modifiedTimeStampForms,
      modifiedTimeStampMembers: entity.modifiedTimeStampMembers,
      modifiedTimeStampTask: entity.modifiedTimeStampTask,
      modifiedTimeStampPhotos: entity.modifiedTimeStampPhotos,
      modifiedTimeStampSignatures: entity.modifiedTimeStampSignatures,
      modifiedTimeStampSignaturetypes: entity.modifiedTimeStampSignaturetypes,
      posSentTo: entity.posSentTo,
      posSentToEmail: entity.posSentToEmail,
      modifiedTimeStampPhototypes: entity.modifiedTimeStampPhototypes,
      taskCommencementTimeStamp: entity.taskCommencementTimeStamp,
      taskStoppedTimeStamp: entity.taskStoppedTimeStamp,
      teamlead: entity.teamlead?.toInt(),
      followupTasks: _mapFollowupTasks(entity.followupTasks),
      stocktake: _mapStocktake(entity.stocktake),
      taskNote: entity.taskNote,
      disallowReschedule: entity.disallowReschedule,
      photoResPerc: entity.photoResPerc?.toInt(),
      liveImagesOnly: entity.liveImagesOnly,
      timeSchedule: entity.timeSchedule,
      scheduleTypeId: entity.scheduleTypeId?.toInt(),
      showFollowupIconMulti: entity.showFollowupIconMulti,
      followupSelectedMulti: entity.followupSelectedMulti,
      regionId: entity.regionId?.toInt(),
      isOpen: entity.isOpen,
      taskCount: entity.taskCount?.toInt(),
      ctFormsTotalCnt: entity.ctFormsTotalCnt?.toInt(),
      ctFormsCompletedCnt: entity.ctFormsCompletedCnt?.toInt(),
      preftime: entity.preftime,
      sendTo: entity.sendTo,
      kTotal: entity.kTotal?.toInt(),
      kCompleted: entity.kCompleted?.toInt(),
      submissionState: entity.submissionState,
      resumePauseItems: _mapResumePauseItems(entity.resumePauseItems),
    );
  }

  static TaskDetail toEntity(TaskDetailModel model) {
    logger(
        'qwe123 toEntity: ${model.scheduledTimeStamp} ${model.scheduledTimeStamp?.toLocal()} ${model.storeName} ${model.scheduledTimeStamp?.timeZoneName} ${model.scheduledTimeStamp?.toLocal().timeZoneName}');
    logger("qwe124 toEntity: ${model.taskmembers.length}");
    return TaskDetail(
      taskId: model.taskId,
      projectId: model.projectId,
      scheduleId: model.scheduleId,
      sentToPayroll: model.sentToPayroll,
      showKm: model.showKm,
      storeId: model.storeId,
      client: model.client,
      clientId: model.clientId,
      clientLogoUrl: model.clientLogoUrl,
      storeGroup: model.storeGroup,
      storeGroupId: model.storeGroupId,
      storeName: model.storeName,
      storeEmail: model.storeEmail,
      minutes: model.minutes,
      budget: model.budget,
      originalbudget: model.originalbudget,
      comment: model.comment,
      claimableKms: model.claimableKms,
      flightDuration: model.flightDuration,
      pages: model.pages,
      location: model.location,
      suburb: model.suburb,
      latitude: model.latitude,
      longitude: model.longitude,
      taskLatitude: model.taskLatitude,
      taskLongitude: model.taskLongitude,
      cycle: model.cycle,
      cycleId: model.cycleId,
      canDelete: model.canDelete,
      scheduledTimeStamp: model.scheduledTimeStamp?.toLocal(),
      submissionTimeStamp: model.submissionTimeStamp?.toLocal(),
      expires: model.expires?.toLocal(),
      onTask: model.onTask,
      phone: model.phone,
      rangeStart: model.rangeStart?.toLocal(),
      rangeEnd: model.rangeEnd?.toLocal(),
      reOpened: model.reOpened,
      reOpenedReason: model.reOpenedReason,
      taskStatus: model.taskStatus,
      warehousejobId: model.warehousejobId,
      connoteUrl: model.connoteUrl,
      posRequired: model.posRequired,
      isPosMandatory: model.isPosMandatory,
      posReceived: model.posReceived,
      photoFolder: _mapPhotoFoldersToEntity(model.photoFolder),
      signatureFolder: _mapSignatureFoldersToEntity(model.signatureFolder),
      forms: _mapFormsToEntity(model.forms),
      posItems: _mapPosItemsToEntity(model.posItems),
      documents: _mapDocumentsToEntity(model.documents),
      taskalerts: _mapTaskalertsToEntity(model.taskalerts),
      taskmembers: _mapTaskmembersToEntity(model.taskmembers),
      modifiedTimeStampDocuments: model.modifiedTimeStampDocuments?.toLocal(),
      modifiedTimeStampForms: model.modifiedTimeStampForms?.toLocal(),
      modifiedTimeStampMembers: model.modifiedTimeStampMembers?.toLocal(),
      modifiedTimeStampTask: model.modifiedTimeStampTask?.toLocal(),
      modifiedTimeStampPhotos: model.modifiedTimeStampPhotos?.toLocal(),
      modifiedTimeStampSignatures: model.modifiedTimeStampSignatures?.toLocal(),
      modifiedTimeStampSignaturetypes:
          model.modifiedTimeStampSignaturetypes?.toLocal(),
      posSentTo: model.posSentTo,
      posSentToEmail: model.posSentToEmail,
      modifiedTimeStampPhototypes: model.modifiedTimeStampPhototypes?.toLocal(),
      taskCommencementTimeStamp: model.taskCommencementTimeStamp?.toLocal(),
      taskStoppedTimeStamp: model.taskStoppedTimeStamp?.toLocal(),
      teamlead: model.teamlead,
      followupTasks: _mapFollowupTasksToEntity(model.followupTasks),
      stocktake: _mapStocktakeToEntity(model.stocktake),
      taskNote: model.taskNote,
      disallowReschedule: model.disallowReschedule,
      photoResPerc: model.photoResPerc,
      liveImagesOnly: model.liveImagesOnly,
      timeSchedule: model.timeSchedule,
      scheduleTypeId: model.scheduleTypeId,
      showFollowupIconMulti: model.showFollowupIconMulti,
      followupSelectedMulti: model.followupSelectedMulti,
      regionId: model.regionId,
      isOpen: model.isOpen,
      taskCount: model.taskCount,
      ctFormsTotalCnt: model.ctFormsTotalCnt,
      ctFormsCompletedCnt: model.ctFormsCompletedCnt,
      preftime: model.preftime,
      sendTo: model.sendTo,
      submissionState: model.submissionState,
      resumePauseItems: _mapResumePauseItemsToEntity(model.resumePauseItems),
      syncPending: model.syncPending,
    );
  }

  // Helper methods for mapping nested objects
  static List<PhotoFolderModel> _mapPhotoFolders(List<PhotoFolder>? folders) {
    if (folders == null) return [];
    return folders
        .map((folder) => PhotoFolderModel(
              photos: _mapPhotos(folder.photos),
              attribute: folder.attribute,
              folderId: folder.folderId?.toInt(),
              folderName: folder.folderName,
              folderPictureAmount: folder.folderPictureAmount?.toInt(),
              imageRec: folder.imageRec,
              modifiedTimeStampPhototype: folder.modifiedTimeStampPhototype,
            ))
        .toList();
  }

  static List<PhotoFolder> _mapPhotoFoldersToEntity(
      Iterable<PhotoFolderModel> models) {
    return models
        .map((model) => PhotoFolder(
              photos: _mapPhotosToEntity(model.photos),
              attribute: model.attribute,
              folderId: model.folderId,
              folderName: model.folderName,
              folderPictureAmount: model.folderPictureAmount,
              imageRec: model.imageRec,
              modifiedTimeStampPhototype: model.modifiedTimeStampPhototype,
            ))
        .toList();
  }

  static List<SignatureFolderModel> _mapSignatureFolders(
      List<SignatureFolder>? folders) {
    if (folders == null) return [];
    return folders
        .map((folder) => SignatureFolderModel(
              signatures: _mapSignatures(folder.signatures),
              attribute: folder.attribute,
              folderId: folder.folderId?.toInt(),
              folderName: folder.folderName,
              modifiedTimeStampSignaturetype:
                  folder.modifiedTimeStampSignaturetype,
            ))
        .toList();
  }

  static List<SignatureFolder> _mapSignatureFoldersToEntity(
      Iterable<SignatureFolderModel> models) {
    return models
        .map((model) => SignatureFolder(
              signatures: _mapSignaturesToEntity(model.signatures),
              attribute: model.attribute,
              folderId: model.folderId,
              folderName: model.folderName,
              modifiedTimeStampSignaturetype:
                  model.modifiedTimeStampSignaturetype,
            ))
        .toList();
  }

  static List<FormModel> _mapForms(List<Form>? forms) {
    if (forms == null) return [];
    return forms
        .map((form) => FormModel(
              formId: form.formId?.toInt(),
              formInstanceId: form.formInstanceId?.toInt(),
              formName: form.formName,
              briefUrl: form.briefUrl,
              questions: _mapQuestions(form.questions),
              questionAnswers: _mapQuestionAnswers(form.questionAnswers),
              modifiedTimeStampForm: form.modifiedTimeStampForm,
              dateStart: form.dateStart,
              isVisionForm: form.isVisionForm,
              visionFormUrl: form.visionFormUrl,
              isMandatory: form.isMandatory,
              formPreview: form.formPreview,
              formTypeId: form.formTypeId?.toInt(),
              formCompleted: form.formCompleted,
              formAllowForward: form.formAllowForward,
              formShowPrice: form.formShowPrice,
              minQty: form.minQty?.toInt(),
              showQuestions: form.showQuestions,
              kTotal: form.kTotal?.toInt(),
              kCompleted: form.kCompleted?.toInt(),
            ))
        .toList();
  }

  static List<Form> _mapFormsToEntity(Iterable<FormModel> models) {
    return models
        .map((model) => Form(
              formId: model.formId,
              formInstanceId: model.formInstanceId,
              formName: model.formName,
              briefUrl: model.briefUrl,
              questions: _mapQuestionsToEntity(model.questions),
              questionAnswers:
                  _mapQuestionAnswersToEntity(model.questionAnswers),
              modifiedTimeStampForm: model.modifiedTimeStampForm?.toLocal(),
              dateStart: model.dateStart?.toLocal(),
              isVisionForm: model.isVisionForm,
              visionFormUrl: model.visionFormUrl,
              isMandatory: model.isMandatory,
              formPreview: model.formPreview,
              formTypeId: model.formTypeId,
              formCompleted: model.formCompleted,
              formAllowForward: model.formAllowForward,
              formShowPrice: model.formShowPrice,
              minQty: model.minQty,
              showQuestions: model.showQuestions,
            ))
        .toList();
  }

  static List<PosItemModel> _mapPosItems(List<PosItem>? items) {
    if (items == null) return [];
    return items
        .map((item) => PosItemModel(
              itemName: item.itemName,
              itemAmount: item.itemAmount?.toInt(),
              photoUrl: item.photoUrl,
            ))
        .toList();
  }

  static List<PosItem> _mapPosItemsToEntity(Iterable<PosItemModel> models) {
    return models
        .map((model) => PosItem(
              itemName: model.itemName,
              itemAmount: model.itemAmount,
              photoUrl: model.photoUrl,
            ))
        .toList();
  }

  static List<DocumentModel> _mapDocuments(List<Document>? documents) {
    if (documents == null) return [];
    return documents
        .map((doc) => DocumentModel(
              projectId: doc.projectId?.toInt(),
              documentId: doc.documentId?.toInt(),
              documentTypeId: doc.documentTypeId?.toInt(),
              documentName: doc.documentName,
              documentIconLink: doc.documentIconLink,
              files: _mapFileElements(doc.files),
              modifiedTimeStampDocument: doc.modifiedTimeStampDocument,
            ))
        .toList();
  }

  static List<Document> _mapDocumentsToEntity(Iterable<DocumentModel> models) {
    return models
        .map((model) => Document(
              projectId: model.projectId,
              documentId: model.documentId,
              documentTypeId: model.documentTypeId,
              documentName: model.documentName,
              documentIconLink: model.documentIconLink,
              files: _mapFileElementsToEntity(model.files),
              modifiedTimeStampDocument: model.modifiedTimeStampDocument,
            ))
        .toList();
  }

  static List<TaskalertModel> _mapTaskalerts(List<Taskalert>? alerts) {
    if (alerts == null) return [];
    return alerts
        .map((alert) => TaskalertModel(
              messageId: alert.messageId?.toInt(),
              schedulepeopleid: alert.schedulepeopleid?.toInt(),
              subject: alert.subject,
              message: alert.message,
            ))
        .toList();
  }

  static List<Taskalert> _mapTaskalertsToEntity(
      Iterable<TaskalertModel> models) {
    return models
        .map((model) => Taskalert(
              messageId: model.messageId,
              schedulepeopleid: model.schedulepeopleid,
              subject: model.subject,
              message: model.message,
            ))
        .toList();
  }

  static List<TaskmemberModel> _mapTaskmembers(List<Taskmember>? members) {
    if (members == null) return [];
    return members
        .map((member) => TaskmemberModel(
              fullname: member.fullname,
              teamLead: member.teamLead?.toInt(),
              email: member.email,
              scheduleId: member.scheduleId?.toInt(),
              taskId: member.taskId?.toInt(),
            ))
        .toList();
  }

  static List<Taskmember> _mapTaskmembersToEntity(
      Iterable<TaskmemberModel> models) {
    return models
        .map((model) => Taskmember(
              fullname: model.fullname,
              teamLead: model.teamLead,
              email: model.email,
              scheduleId: model.scheduleId,
              taskId: model.taskId,
            ))
        .toList();
  }

  static List<FollowupTaskModel> _mapFollowupTasks(List<FollowupTask>? tasks) {
    if (tasks == null) return [];
    return tasks
        .map((task) => FollowupTaskModel(
              showFollowupIcon: task.showFollowupIcon,
              followupSelected: task.followupSelected,
              selectedVisitDate: task.selectedVisitDate,
              selectedFollowupTypeId: task.selectedFollowupTypeId?.toInt(),
              selectedFollowupItemId: task.selectedFollowupItemId?.toInt(),
              selectedBudget: task.selectedBudget?.toInt(),
              selectedFollowupType: task.selectedFollowupType,
              selectedFollowupItem: task.selectedFollowupItem,
              selectedScheduleNote: task.selectedScheduleNote,
              followupNumber: task.followupNumber?.toInt(),
            ))
        .toList();
  }

  static List<FollowupTask> _mapFollowupTasksToEntity(
      Iterable<FollowupTaskModel> models) {
    return models
        .map((model) => FollowupTask(
              showFollowupIcon: model.showFollowupIcon,
              followupSelected: model.followupSelected,
              selectedVisitDate: model.selectedVisitDate?.toLocal(),
              selectedFollowupTypeId: model.selectedFollowupTypeId,
              selectedFollowupItemId: model.selectedFollowupItemId,
              selectedBudget: model.selectedBudget,
              selectedFollowupType: model.selectedFollowupType,
              selectedFollowupItem: model.selectedFollowupItem,
              selectedScheduleNote: model.selectedScheduleNote,
              followupNumber: model.followupNumber,
            ))
        .toList();
  }

  static List<StocktakeModel> _mapStocktake(List<Stocktake>? stocktake) {
    if (stocktake == null) return [];
    return stocktake
        .map((item) => StocktakeModel(
              taskId: item.taskId?.toInt(),
              itemId: item.itemId?.toInt(),
              itemCode: item.itemCode,
              itemName: item.itemName,
              itemGroup: item.itemGroup,
              imageUrl: item.imageUrl,
              itemLocation: item.itemLocation,
              itemQty: item.itemQty?.toInt(),
            ))
        .toList();
  }

  static List<Stocktake> _mapStocktakeToEntity(
      Iterable<StocktakeModel> models) {
    return models
        .map((model) => Stocktake(
              taskId: model.taskId,
              itemId: model.itemId,
              itemCode: model.itemCode,
              itemName: model.itemName,
              itemGroup: model.itemGroup,
              imageUrl: model.imageUrl,
              itemLocation: model.itemLocation,
              itemQty: model.itemQty,
            ))
        .toList();
  }

  // Photo mapping methods
  static List<PhotoModel> _mapPhotos(List<Photo>? photos) {
    if (photos == null) return [];
    return photos
        .map((photo) => PhotoModel(
              formId: photo.formId?.toInt(),
              questionId: photo.questionId?.toInt(),
              measurementId: photo.measurementId?.toInt(),
              folderId: photo.folderId?.toInt(),
              photoId: photo.photoId?.toInt(),
              photoUrl: photo.photoUrl,
              thumbnailUrl: photo.thumbnailUrl,
              caption: photo.caption,
              modifiedTimeStampPhoto: photo.modifiedTimeStampPhoto,
              cannotUploadMandatory: photo.cannotUploadMandatory,
              userDeletedPhoto: photo.userDeletedPhoto,
              imageRec: photo.imageRec,
              questionpartId: photo.questionpartId?.toInt(),
              questionPartMultiId: photo.questionPartMultiId,
              measurementPhototypeId: photo.measurementPhototypeId?.toInt(),
              combineTypeId: photo.combineTypeId?.toInt(),
              photoTagId: photo.photoTagId?.toInt(),
              photoCombinetypeId: photo.photoCombinetypeId?.toInt(),
              localPath: photo.localPath,
              isEdited: photo.isEdited,
            ))
        .toList();
  }

  static List<Photo> _mapPhotosToEntity(Iterable<PhotoModel> models) {
    return models
        .map((model) => Photo(
              formId: model.formId,
              questionId: model.questionId,
              measurementId: model.measurementId,
              folderId: model.folderId,
              photoId: model.photoId,
              photoUrl: model.photoUrl,
              thumbnailUrl: model.thumbnailUrl,
              caption: model.caption,
              modifiedTimeStampPhoto: model.modifiedTimeStampPhoto,
              cannotUploadMandatory: model.cannotUploadMandatory,
              userDeletedPhoto: model.userDeletedPhoto,
              imageRec: model.imageRec,
              questionpartId: model.questionpartId,
              questionPartMultiId: model.questionPartMultiId,
              measurementPhototypeId: model.measurementPhototypeId,
              combineTypeId: model.combineTypeId,
              photoTagId: model.photoTagId,
              photoCombinetypeId: model.photoCombinetypeId,
              localPath: model.localPath,
              isEdited: model.isEdited,
            ))
        .toList();
  }

  // Signature mapping methods
  static List<SignatureModel> _mapSignatures(List<Signature>? signatures) {
    if (signatures == null) return [];
    return signatures
        .map((signature) => SignatureModel(
              signatureId: signature.signatureId?.toInt(),
              formId: signature.formId?.toInt(),
              questionId: signature.questionId?.toInt(),
              signatureUrl: signature.signatureUrl,
              thumbnailUrl: signature.thumbnailUrl,
              signedBy: signature.signedBy,
              modifiedTimeStampSignature: signature.modifiedTimeStampSignature,
              userDeletedSignature: signature.userDeletedSignature,
              cannotUploadMandatory: signature.cannotUploadMandatory,
              localPath: signature.localPath,
              isEdited: signature.isEdited,
            ))
        .toList();
  }

  static List<Signature> _mapSignaturesToEntity(
      Iterable<SignatureModel> models) {
    return models
        .map((model) => Signature(
              signatureId: model.signatureId,
              formId: model.formId,
              questionId: model.questionId,
              signatureUrl: model.signatureUrl,
              thumbnailUrl: model.thumbnailUrl,
              signedBy: model.signedBy,
              modifiedTimeStampSignature: model.modifiedTimeStampSignature,
              userDeletedSignature: model.userDeletedSignature,
              cannotUploadMandatory: model.cannotUploadMandatory,
              localPath: model.localPath,
              isEdited: model.isEdited,
            ))
        .toList();
  }

  // FileElement mapping methods
  static List<FileElementModel> _mapFileElements(List<FileElement>? files) {
    if (files == null) return [];
    return files
        .map((file) => FileElementModel(
              documentId: file.documentId?.toInt(),
              projectId: file.projectId?.toInt(),
              documentFileLink: file.documentFileLink,
              fileId: file.fileId?.toInt(),
              modifiedTimeStampFile: file.modifiedTimeStampFile,
            ))
        .toList();
  }

  static List<FileElement> _mapFileElementsToEntity(
      Iterable<FileElementModel> models) {
    return models
        .map((model) => FileElement(
              documentId: model.documentId,
              projectId: model.projectId,
              documentFileLink: model.documentFileLink,
              fileId: model.fileId,
              modifiedTimeStampFile: model.modifiedTimeStampFile,
            ))
        .toList();
  }

  // QuestionAnswer mapping methods
  static List<QuestionAnswerModel> _mapQuestionAnswers(
      List<QuestionAnswer>? answers) {
    if (answers == null) return [];
    return answers
        .map((answer) => QuestionAnswerModel(
              taskId: answer.taskId?.toInt(),
              formId: answer.formId?.toInt(),
              questionId: answer.questionId?.toInt(),
              questionpartId: answer.questionpartId?.toInt(),
              flip: answer.flip,
              questionPartMultiId: answer.questionPartMultiId,
              measurementId: answer.measurementId?.toInt(),
              measurementTypeId: answer.measurementTypeId?.toInt(),
              measurementOptionId: answer.measurementOptionId?.toInt(),
              measurementOptionIds: answer.measurementOptionIds,
              measurementTextResult: answer.measurementTextResult,
              isComment: answer.isComment,
              commentTypeId: answer.commentTypeId?.toInt(),
              order: answer.order,
            ))
        .toList();
  }

  static List<QuestionAnswer> _mapQuestionAnswersToEntity(
      Iterable<QuestionAnswerModel> models) {
    return models
        .map((model) => QuestionAnswer(
              taskId: model.taskId,
              formId: model.formId,
              questionId: model.questionId,
              questionpartId: model.questionpartId,
              flip: model.flip,
              questionPartMultiId: model.questionPartMultiId,
              measurementId: model.measurementId,
              measurementTypeId: model.measurementTypeId,
              measurementOptionId: model.measurementOptionId,
              measurementOptionIds: model.measurementOptionIds,
              measurementTextResult: model.measurementTextResult,
              isComment: model.isComment,
              commentTypeId: model.commentTypeId,
              order: model.order,
            ))
        .toList();
  }

  // Question mapping methods
  static List<QuestionModel> _mapQuestions(List<Question>? questions) {
    if (questions == null) return [];
    return questions
        .map((question) => QuestionModel(
              questionId: question.questionId?.toInt(),
              questionOrderId: question.questionOrderId?.toInt(),
              questionDescription: question.questionDescription,
              isComment: question.isComment,
              isCommentMandatory: question.isCommentMandatory,
              showQuestions: question.showQuestions,
              hasSignature: question.hasSignature,
              isSignatureMandatory: question.isSignatureMandatory,
              signatureUrl: question.signatureUrl,
              photoUrl: question.photoUrl,
              questionBrief: question.questionBrief,
              questionParts: _mapQuestionParts(question.questionParts),
              measurements: _mapMeasurements(question.measurements),
              questionConditions:
                  _mapQuestionConditions(question.questionConditions),
              commentTypes: _mapCommentTypes(question.commentTypes),
              modifiedTimeStampQuestion: question.modifiedTimeStampQuestion,
              targetByCycle: question.targetByCycle,
              targetByGroup: question.targetByGroup,
              targetByCompany: question.targetByCompany,
              targetByRegion: question.targetByRegion,
              targetByBudget: question.targetByBudget,
              isMll: question.isMll,
              photoTagsTwo: _mapPhotoTagsT(question.photoTagsTwo),
              photoTagsThree: _mapPhotoTagsT(question.photoTagsThree),
              isMulti: question.isMulti,
              multiMeasurementId: question.multiMeasurementId?.toInt(),
              isMultiOneAnswer: question.isMultiOneAnswer,
              flip: question.flip,
              questionTypeId: question.questionTypeId?.toInt(),
            ))
        .toList();
  }

  static List<Question> _mapQuestionsToEntity(Iterable<QuestionModel> models) {
    return models
        .map((model) => Question(
              questionId: model.questionId,
              questionOrderId: model.questionOrderId,
              questionDescription: model.questionDescription,
              isComment: model.isComment,
              isCommentMandatory: model.isCommentMandatory,
              showQuestions: model.showQuestions,
              hasSignature: model.hasSignature,
              isSignatureMandatory: model.isSignatureMandatory,
              signatureUrl: model.signatureUrl,
              photoUrl: model.photoUrl,
              questionBrief: model.questionBrief,
              questionParts: _mapQuestionPartsToEntity(model.questionParts),
              measurements: _mapMeasurementsToEntity(model.measurements),
              questionConditions:
                  _mapQuestionConditionsToEntity(model.questionConditions),
              commentTypes: _mapCommentTypesToEntity(model.commentTypes),
              modifiedTimeStampQuestion: model.modifiedTimeStampQuestion,
              targetByCycle: model.targetByCycle,
              targetByGroup: model.targetByGroup,
              targetByCompany: model.targetByCompany,
              targetByRegion: model.targetByRegion,
              targetByBudget: model.targetByBudget,
              isMll: model.isMll,
              photoTagsTwo: _mapPhotoTagsTToEntity(model.photoTagsTwo),
              photoTagsThree: _mapPhotoTagsTToEntity(model.photoTagsThree),
              isMulti: model.isMulti,
              multiMeasurementId: model.multiMeasurementId,
              isMultiOneAnswer: model.isMultiOneAnswer,
              flip: model.flip,
              questionTypeId: model.questionTypeId,
            ))
        .toList();
  }

  /// Map a single QuestionModel to Question entity
  static Question mapSingleQuestionToEntity(QuestionModel model) {
    return Question(
      questionId: model.questionId,
      questionOrderId: model.questionOrderId,
      questionDescription: model.questionDescription,
      isComment: model.isComment,
      isCommentMandatory: model.isCommentMandatory,
      showQuestions: model.showQuestions,
      hasSignature: model.hasSignature,
      isSignatureMandatory: model.isSignatureMandatory,
      signatureUrl: model.signatureUrl,
      photoUrl: model.photoUrl,
      questionBrief: model.questionBrief,
      questionParts: _mapQuestionPartsToEntity(model.questionParts),
      measurements: _mapMeasurementsToEntity(model.measurements),
      questionConditions:
          _mapQuestionConditionsToEntity(model.questionConditions),
      commentTypes: _mapCommentTypesToEntity(model.commentTypes),
      modifiedTimeStampQuestion: model.modifiedTimeStampQuestion,
      targetByCycle: model.targetByCycle,
      targetByGroup: model.targetByGroup,
      targetByCompany: model.targetByCompany,
      targetByRegion: model.targetByRegion,
      targetByBudget: model.targetByBudget,
      isMll: model.isMll,
      photoTagsTwo: _mapPhotoTagsTToEntity(model.photoTagsTwo),
      photoTagsThree: _mapPhotoTagsTToEntity(model.photoTagsThree),
      isMulti: model.isMulti,
      multiMeasurementId: model.multiMeasurementId,
      isMultiOneAnswer: model.isMultiOneAnswer,
      flip: model.flip,
      questionTypeId: model.questionTypeId,
    );
  }

  // CommentType mapping methods
  static List<CommentTypeModel> _mapCommentTypes(
      List<CommentType>? commentTypes) {
    if (commentTypes == null) return [];
    return commentTypes
        .map((commentType) => CommentTypeModel(
              commentTypeId: commentType.commentTypeId?.toInt(),
              commentType: commentType.commentType,
            ))
        .toList();
  }

  static List<CommentType> _mapCommentTypesToEntity(
      Iterable<CommentTypeModel> models) {
    return models
        .map((model) => CommentType(
              commentTypeId: model.commentTypeId,
              commentType: model.commentType,
            ))
        .toList();
  }

  // QuestionPart mapping methods
  static List<QuestionPartModel> _mapQuestionParts(
      List<QuestionPart>? questionParts) {
    if (questionParts == null) return [];
    return questionParts
        .map((questionPart) => QuestionPartModel(
              projectid: questionPart.projectid?.toInt(),
              questionpartId: questionPart.questionpartId?.toInt(),
              questionpartDescription: questionPart.questionpartDescription,
              price: questionPart.price,
              modifiedTimeStampQuestionpart:
                  questionPart.modifiedTimeStampQuestionpart,
              targetByCycle: questionPart.targetByCycle,
              targetByGroup: questionPart.targetByGroup,
              targetByCompany: questionPart.targetByCompany,
              targetByRegion: questionPart.targetByRegion,
              targetByBudget: questionPart.targetByBudget,
              osaForm: questionPart.osaForm,
              companyId: questionPart.companyId?.toInt(),
              itemImage: questionPart.itemImage,
              targeted: questionPart.targeted?.toInt(),
            ))
        .toList();
  }

  static List<QuestionPart> _mapQuestionPartsToEntity(
      Iterable<QuestionPartModel> models) {
    return models
        .map((model) => QuestionPart(
              projectid: model.projectid,
              questionpartId: model.questionpartId,
              questionpartDescription: model.questionpartDescription,
              price: model.price,
              modifiedTimeStampQuestionpart:
                  model.modifiedTimeStampQuestionpart,
              targetByCycle: model.targetByCycle,
              targetByGroup: model.targetByGroup,
              targetByCompany: model.targetByCompany,
              targetByRegion: model.targetByRegion,
              targetByBudget: model.targetByBudget,
              osaForm: model.osaForm,
              companyId: model.companyId,
              itemImage: model.itemImage,
              targeted: model.targeted,
            ))
        .toList();
  }

  // QuestionCondition mapping methods
  static List<QuestionConditionModel> _mapQuestionConditions(
      List<QuestionCondition>? questionConditions) {
    if (questionConditions == null) return [];
    return questionConditions
        .map((questionCondition) => QuestionConditionModel(
              measurementId: questionCondition.measurementId?.toInt(),
              measurementOptionId:
                  questionCondition.measurementOptionId?.toInt(),
              actionQuestionId: questionCondition.actionQuestionId?.toInt(),
              action: questionCondition.action,
              modifiedTimeStampQuestioncondition:
                  questionCondition.modifiedTimeStampQuestioncondition,
            ))
        .toList();
  }

  static List<QuestionCondition> _mapQuestionConditionsToEntity(
      Iterable<QuestionConditionModel> models) {
    return models
        .map((model) => QuestionCondition(
              measurementId: model.measurementId,
              measurementOptionId: model.measurementOptionId,
              actionQuestionId: model.actionQuestionId,
              action: model.action,
              modifiedTimeStampQuestioncondition:
                  model.modifiedTimeStampQuestioncondition,
            ))
        .toList();
  }

  // PhotoTagsT mapping methods
  static List<PhotoTagsTModel> _mapPhotoTagsT(List<PhotoTagsT>? photoTags) {
    if (photoTags == null) return [];
    return photoTags
        .map((photoTag) => PhotoTagsTModel(
              questionpartId: photoTag.questionpartId?.toInt(),
              measurementId: photoTag.measurementId?.toInt(),
              isMandatory: photoTag.isMandatory,
              photoResPerc: photoTag.photoResPerc?.toInt(),
              liveImagesOnly: photoTag.liveImagesOnly,
              photoTagId: photoTag.photoTagId?.toInt(),
              photoTag: photoTag.photoTag,
              numberOfPhotos: photoTag.numberOfPhotos?.toInt(),
              measurementPhototypeId: photoTag.measurementPhototypeId?.toInt(),
              imageRec: photoTag.imageRec,
              userPhotos: _mapPhotos(photoTag.userPhotos),
            ))
        .toList();
  }

  static List<PhotoTagsT> _mapPhotoTagsTToEntity(
      Iterable<PhotoTagsTModel> models) {
    return models
        .map((model) => PhotoTagsT(
              questionpartId: model.questionpartId,
              measurementId: model.measurementId,
              isMandatory: model.isMandatory,
              photoResPerc: model.photoResPerc,
              liveImagesOnly: model.liveImagesOnly,
              photoTagId: model.photoTagId,
              photoTag: model.photoTag,
              numberOfPhotos: model.numberOfPhotos,
              measurementPhototypeId: model.measurementPhototypeId,
              imageRec: model.imageRec,
              userPhotos: _mapPhotosToEntity(model.userPhotos),
            ))
        .toList();
  }

  // Measurement mapping methods
  static List<MeasurementModel> _mapMeasurements(
      List<Measurement>? measurements) {
    if (measurements == null) return [];
    return measurements
        .map((measurement) => MeasurementModel(
              measurementId: measurement.measurementId?.toInt(),
              measurementTypeId: measurement.measurementTypeId?.toInt(),
              measurementDescription: measurement.measurementDescription,
              measurementTypeName: measurement.measurementTypeName,
              defaultAction: measurement.defaultAction,
              measurementOptions:
                  _mapMeasurementOptions(measurement.measurementOptions),
              measurementConditions:
                  _mapMeasurementConditions(measurement.measurementConditions),
              measurementConditionsMultiple: _mapMeasurementConditions(
                  measurement.measurementConditionsMultiple),
              measurementValidations: _mapMeasurementValidations(
                  measurement.measurementValidations),
              measurementDefaultsResult: measurement.measurementDefaultsResult,
              modifiedTimeStampMeasurement:
                  measurement.modifiedTimeStampMeasurement,
              modifiedTimeStampMeasurementDefaultsResult:
                  measurement.modifiedTimeStampMeasurementDefaultsResult,
              measurementOrderId: measurement.measurementOrderId?.toInt(),
              mandatoryPhototypesCount:
                  measurement.mandatoryPhototypesCount?.toInt(),
              optionalPhototypesCount:
                  measurement.optionalPhototypesCount?.toInt(),
              measurementImage: measurement.measurementImage,
              companyid: measurement.companyid?.toInt(),
              measurementPhototypesDeprecated:
                  _mapMeasurementPhototypesDeprecated(
                      measurement.measurementPhototypesDeprecated),
              modifiedTimeStampMeasurementvalidation:
                  measurement.modifiedTimeStampMeasurementvalidation,
              validationTypeId: measurement.validationTypeId?.toInt(),
              required: measurement.required,
              rangeValidation: measurement.rangeValidation,
              expressionValidation: measurement.expressionValidation,
              errorMessage: measurement.errorMessage,
              modifiedTimeStampMeasurementdefault:
                  measurement.modifiedTimeStampMeasurementdefault,
            ))
        .toList();
  }

  static List<Measurement> _mapMeasurementsToEntity(
      Iterable<MeasurementModel> models) {
    return models
        .map((model) => Measurement(
              measurementId: model.measurementId,
              measurementTypeId: model.measurementTypeId,
              measurementDescription: model.measurementDescription,
              measurementTypeName: model.measurementTypeName,
              defaultAction: model.defaultAction,
              measurementOptions:
                  _mapMeasurementOptionsToEntity(model.measurementOptions),
              measurementConditions: _mapMeasurementConditionsToEntity(
                  model.measurementConditions),
              measurementConditionsMultiple: _mapMeasurementConditionsToEntity(
                  model.measurementConditionsMultiple),
              measurementValidations: _mapMeasurementValidationsToEntity(
                  model.measurementValidations),
              measurementDefaultsResult: model.measurementDefaultsResult,
              modifiedTimeStampMeasurement: model.modifiedTimeStampMeasurement,
              modifiedTimeStampMeasurementDefaultsResult:
                  model.modifiedTimeStampMeasurementDefaultsResult,
              measurementOrderId: model.measurementOrderId,
              mandatoryPhototypesCount: model.mandatoryPhototypesCount,
              optionalPhototypesCount: model.optionalPhototypesCount,
              measurementImage: model.measurementImage,
              companyid: model.companyid,
              measurementPhototypesDeprecated:
                  _mapMeasurementPhototypesDeprecatedToEntity(
                      model.measurementPhototypesDeprecated),
              modifiedTimeStampMeasurementvalidation:
                  model.modifiedTimeStampMeasurementvalidation,
              validationTypeId: model.validationTypeId,
              required: model.required,
              rangeValidation: model.rangeValidation,
              expressionValidation: model.expressionValidation,
              errorMessage: model.errorMessage,
              modifiedTimeStampMeasurementdefault:
                  model.modifiedTimeStampMeasurementdefault,
            ))
        .toList();
  }

  // MeasurementOption mapping methods
  static List<MeasurementOptionModel> _mapMeasurementOptions(
      List<MeasurementOption>? options) {
    if (options == null) return [];
    return options
        .map((option) => MeasurementOptionModel(
              measurementId: option.measurementId?.toInt(),
              measurementOptionId: option.measurementOptionId?.toInt(),
              measurementOptionDescription: option.measurementOptionDescription,
              modifiedTimeStampMeasurementoption:
                  option.modifiedTimeStampMeasurementoption,
              budgetOffset: option.budgetOffset?.toInt(),
              budgetOffsetType: option.budgetOffsetType?.toInt(),
              isAnswer: option.isAnswer,
            ))
        .toList();
  }

  static List<MeasurementOption> _mapMeasurementOptionsToEntity(
      Iterable<MeasurementOptionModel> models) {
    return models
        .map((model) => MeasurementOption(
              measurementId: model.measurementId,
              measurementOptionId: model.measurementOptionId,
              measurementOptionDescription: model.measurementOptionDescription,
              modifiedTimeStampMeasurementoption:
                  model.modifiedTimeStampMeasurementoption,
              budgetOffset: model.budgetOffset,
              budgetOffsetType: model.budgetOffsetType,
              isAnswer: model.isAnswer,
            ))
        .toList();
  }

  // MeasurementCondition mapping methods
  static List<MeasurementConditionModel> _mapMeasurementConditions(
      List<MeasurementCondition>? conditions) {
    if (conditions == null) return [];
    return conditions
        .map((condition) => MeasurementConditionModel(
              measurementId: condition.measurementId?.toInt(),
              measurementOptionId: condition.measurementOptionId?.toInt(),
              actionMeasurementId: condition.actionMeasurementId?.toInt(),
              action: condition.action,
              modifiedTimeStampMeasurementconidtion:
                  condition.modifiedTimeStampMeasurementconidtion,
            ))
        .toList();
  }

  static List<MeasurementCondition> _mapMeasurementConditionsToEntity(
      Iterable<MeasurementConditionModel> models) {
    return models
        .map((model) => MeasurementCondition(
              measurementId: model.measurementId,
              measurementOptionId: model.measurementOptionId,
              actionMeasurementId: model.actionMeasurementId,
              action: model.action,
              modifiedTimeStampMeasurementconidtion:
                  model.modifiedTimeStampMeasurementconidtion,
            ))
        .toList();
  }

  // MeasurementValidation mapping methods
  static List<MeasurementValidationModel> _mapMeasurementValidations(
      List<MeasurementValidation>? validations) {
    if (validations == null) return [];
    return validations
        .map((validation) => MeasurementValidationModel(
              measurementId: validation.measurementId?.toInt(),
              validationTypeId: validation.validationTypeId?.toInt(),
              required: validation.required,
              rangeValidation: validation.rangeValidation,
              expressionValidation: validation.expressionValidation,
              errorMessage: validation.errorMessage,
              modifiedTimeStampMeasurementvalidation:
                  validation.modifiedTimeStampMeasurementvalidation,
            ))
        .toList();
  }

  static List<MeasurementValidation> _mapMeasurementValidationsToEntity(
      Iterable<MeasurementValidationModel> models) {
    return models
        .map((model) => MeasurementValidation(
              measurementId: model.measurementId,
              validationTypeId: model.validationTypeId,
              required: model.required,
              rangeValidation: model.rangeValidation,
              expressionValidation: model.expressionValidation,
              errorMessage: model.errorMessage,
              modifiedTimeStampMeasurementvalidation:
                  model.modifiedTimeStampMeasurementvalidation,
            ))
        .toList();
  }

  // MeasurementPhototypesDeprecated mapping methods
  static List<MeasurementPhototypesDeprecatedModel>
      _mapMeasurementPhototypesDeprecated(
          List<MeasurementPhototypesDeprecated>? phototypes) {
    if (phototypes == null) return [];
    return phototypes
        .map((phototype) => MeasurementPhototypesDeprecatedModel(
              uploadedPictureAmount: phototype.uploadedPictureAmount?.toInt(),
              uploadedBlank: phototype.uploadedBlank,
              uploadedAndCompleted: phototype.uploadedAndCompleted,
              photos: _mapPhotos(phototype.photos),
              attribute: phototype.attribute,
              folderId: phototype.folderId?.toInt(),
              folderName: phototype.folderName,
              folderPictureAmount: phototype.folderPictureAmount?.toInt(),
              imageRec: phototype.imageRec,
              modifiedTimeStampPhototype: phototype.modifiedTimeStampPhototype,
            ))
        .toList();
  }

  static List<MeasurementPhototypesDeprecated>
      _mapMeasurementPhototypesDeprecatedToEntity(
          Iterable<MeasurementPhototypesDeprecatedModel> models) {
    return models
        .map((model) => MeasurementPhototypesDeprecated(
              uploadedPictureAmount: model.uploadedPictureAmount,
              uploadedBlank: model.uploadedBlank,
              uploadedAndCompleted: model.uploadedAndCompleted,
              photos: _mapPhotosToEntity(model.photos),
              attribute: model.attribute,
              folderId: model.folderId,
              folderName: model.folderName,
              folderPictureAmount: model.folderPictureAmount,
              imageRec: model.imageRec,
              modifiedTimeStampPhototype: model.modifiedTimeStampPhototype,
            ))
        .toList();
  }

  static List<ResumePauseItemModel> _mapResumePauseItems(
      List<ResumePauseItem>? resumePauseItems) {
    if (resumePauseItems == null) return [];
    return resumePauseItems
        .map((item) => ResumePauseItemModel(
              resumeOrderID: item.resumeOrderID,
              resumeDate: item.resumeDate,
              pauseDate: item.pauseDate,
            ))
        .toList();
  }

  static List<ResumePauseItem> _mapResumePauseItemsToEntity(
      List<ResumePauseItemModel>? resumePauseItems) {
    if (resumePauseItems == null) return [];
    return resumePauseItems
        .map((item) => ResumePauseItem(
              resumeOrderID: item.resumeOrderID?.toString(),
              resumeDate: item.resumeDate?.toLocal(),
              pauseDate: item.pauseDate?.toLocal(),
            ))
        .toList();
  }
}
