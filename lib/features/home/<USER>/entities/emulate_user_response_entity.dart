import 'dart:convert';
import 'package:equatable/equatable.dart';

class EmulateUserResponseEntity {
  final EmulateUserData? data;

  const EmulateUserResponseEntity({
    this.data,
  });

  factory EmulateUserResponseEntity.fromRawJson(String str) =>
      EmulateUserResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EmulateUserResponseEntity.fromJson(Map<String, dynamic> json) =>
      EmulateUserResponseEntity(
        data: json["data"] == null
            ? null
            : EmulateUserData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class EmulateUserData {
  final ManagerInfo? managerInfo;
  final List<EmulateUserEntity>? activeUsersInfo;

  const EmulateUserData({
    this.managerInfo,
    this.activeUsersInfo,
  });

  factory EmulateUserData.fromJson(Map<String, dynamic> json) =>
      EmulateUserData(
        managerInfo: json["managerInfo"] == null
            ? null
            : ManagerInfo.fromJson(json["managerInfo"]),
        activeUsersInfo: json["activeUsersInfo"] == null
            ? null
            : List<EmulateUserEntity>.from(json["activeUsersInfo"]
                .map((x) => EmulateUserEntity.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "managerInfo": managerInfo?.toJson(),
        "activeUsersInfo": activeUsersInfo?.map((x) => x.toJson()).toList(),
      };
}

class ManagerInfo extends Equatable {
  final int? adminUserId;
  final String? firstname;
  final String? lastname;
  final bool? adminAccess;
  final bool? adminUniversal;
  final int? countryId;
  final String? deviceToken;

  const ManagerInfo({
    this.adminUserId,
    this.firstname,
    this.lastname,
    this.adminAccess,
    this.adminUniversal,
    this.countryId,
    this.deviceToken,
  });

  factory ManagerInfo.fromJson(Map<String, dynamic> json) => ManagerInfo(
        adminUserId: json["admin_user_id"],
        firstname: json["firstname"],
        lastname: json["lastname"],
        adminAccess: json["admin_access"],
        adminUniversal: json["admin_universal"],
        countryId: json["country_id"],
        deviceToken: json["device_token"],
      );

  Map<String, dynamic> toJson() => {
        "admin_user_id": adminUserId,
        "firstname": firstname,
        "lastname": lastname,
        "admin_access": adminAccess,
        "admin_universal": adminUniversal,
        "country_id": countryId,
        "device_token": deviceToken,
      };

  @override
  List<Object?> get props => [
        adminUserId,
        firstname,
        lastname,
        adminAccess,
        adminUniversal,
        countryId,
        deviceToken,
      ];
}

class EmulateUserEntity extends Equatable {
  final int? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? state;
  final String? contractor;
  final bool? isActive;

  const EmulateUserEntity({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.state,
    this.contractor,
    this.isActive,
  });

  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();

  String get displayName =>
      fullName.isNotEmpty ? fullName : email ?? 'Unknown User';

  factory EmulateUserEntity.fromJson(Map<String, dynamic> json) =>
      EmulateUserEntity(
        userId: json["user_id"],
        // Handle both formats: "firstname"/"lastname" and "first_name"/"last_name"
        firstName: json["firstname"] ?? json["first_name"],
        lastName: json["lastname"] ?? json["last_name"],
        email: json["email"],
        state: json["state"],
        contractor: json["contractor"],
        isActive: json["is_active"],
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "firstname": firstName,
        "lastname": lastName,
        "email": email,
        "state": state,
        "contractor": contractor,
        "is_active": isActive,
      };

  @override
  List<Object?> get props => [
        userId,
        firstName,
        lastName,
        email,
        state,
        contractor,
        isActive,
      ];
}
