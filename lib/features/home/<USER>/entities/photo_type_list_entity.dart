import 'dart:convert';

/// Entity class for photo type list operations
/// Used for update_phototypes and add_phototypes processing
class PhotoTypeListModel {
  String? taskId;
  List<PhotoTypeModel>? photoTypes;
  DateTime? modifiedTimeStampPhototypes;

  PhotoTypeListModel({
    this.taskId,
    this.photoTypes,
    this.modifiedTimeStampPhototypes,
  });

  factory PhotoTypeListModel.fromRawJson(String str) =>
      PhotoTypeListModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PhotoTypeListModel.fromJson(Map<String, dynamic> json) =>
      PhotoTypeListModel(
        taskId: json["task_id"]?.toString(),
        photoTypes: json["photo_types"] == null
            ? []
            : List<PhotoTypeModel>.from(
                json["photo_types"]!.map((x) => PhotoTypeModel.fromJson(x))),
        modifiedTimeStampPhototypes:
            json["modified_time_stamp_phototypes"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_phototypes"]),
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "photo_types": photoTypes == null
            ? []
            : List<dynamic>.from(photoTypes!.map((x) => x.toJson())),
        "modified_time_stamp_phototypes":
            modifiedTimeStampPhototypes?.toIso8601String(),
      };
}

/// Individual photo type model
class PhotoTypeModel {
  String? phototypeId;
  String? phototypeName;
  int? phototypePictureAmount;
  bool? mandatory;
  DateTime? modifiedTimeStampPhototype;

  PhotoTypeModel({
    this.phototypeId,
    this.phototypeName,
    this.phototypePictureAmount,
    this.mandatory,
    this.modifiedTimeStampPhototype,
  });

  factory PhotoTypeModel.fromRawJson(String str) =>
      PhotoTypeModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PhotoTypeModel.fromJson(Map<String, dynamic> json) => PhotoTypeModel(
        phototypeId: json["phototype_id"]?.toString(),
        phototypeName: json["phototype_name"],
        phototypePictureAmount: json["phototype_picture_amount"],
        mandatory: json["mandatory"],
        modifiedTimeStampPhototype:
            json["modified_time_stamp_phototype"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_phototype"]),
      );

  Map<String, dynamic> toJson() => {
        "phototype_id": phototypeId,
        "phototype_name": phototypeName,
        "phototype_picture_amount": phototypePictureAmount,
        "mandatory": mandatory,
        "modified_time_stamp_phototype":
            modifiedTimeStampPhototype?.toIso8601String(),
      };
}
