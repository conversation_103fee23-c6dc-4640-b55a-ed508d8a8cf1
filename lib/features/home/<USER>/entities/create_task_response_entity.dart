import 'dart:convert';
import 'package:equatable/equatable.dart';

class CreateTaskResponseEntity extends Equatable {
  final bool success;
  final String message;
  final List<int> createdTaskIds;
  final int totalTasksCreated;

  const CreateTaskResponseEntity({
    required this.success,
    required this.message,
    required this.createdTaskIds,
    required this.totalTasksCreated,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'created_task_ids': createdTaskIds,
      'total_tasks_created': totalTasksCreated,
    };
  }

  factory CreateTaskResponseEntity.fromJson(Map<String, dynamic> json) {
    return CreateTaskResponseEntity(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      createdTaskIds: List<int>.from(json['created_task_ids'] ?? []),
      totalTasksCreated: json['total_tasks_created'] ?? 0,
    );
  }

  String toRawJson() => json.encode(toJson());

  factory CreateTaskResponseEntity.fromRawJson(String str) =>
      CreateTaskResponseEntity.fromJson(json.decode(str));

  @override
  List<Object?> get props => [
        success,
        message,
        createdTaskIds,
        totalTasksCreated,
      ];
}
