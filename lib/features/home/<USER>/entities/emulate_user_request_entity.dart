import 'dart:convert';

class EmulateUserRequestEntity {
  final String userId;
  final String token;

  const EmulateUserRequestEntity({
    required this.userId,
    required this.token,
  });

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "token": token,
      };

  String toRawJson() => json.encode(toJson());

  factory EmulateUserRequestEntity.fromJson(Map<String, dynamic> json) =>
      EmulateUserRequestEntity(
        userId: json["user_id"] ?? "",
        token: json["token"] ?? "",
      );

  factory EmulateUserRequestEntity.fromRawJson(String str) =>
      EmulateUserRequestEntity.fromJson(json.decode(str));
}
