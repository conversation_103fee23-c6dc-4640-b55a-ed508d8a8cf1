import 'dart:convert';

/// Entity class for photo type deletion operations
/// Used for delete_phototypes processing
class PhotoTypeDeleteListModel {
  String? taskId;
  List<String> phototypeIDsToBeDeleted;

  PhotoTypeDeleteListModel({
    this.taskId,
    this.phototypeIDsToBeDeleted = const [],
  });

  factory PhotoTypeDeleteListModel.fromRawJson(String str) =>
      PhotoTypeDeleteListModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PhotoTypeDeleteListModel.fromJson(Map<String, dynamic> json) =>
      PhotoTypeDeleteListModel(
        taskId: json["task_id"]?.toString(),
        phototypeIDsToBeDeleted: json["phototype_ids_to_be_deleted"] == null
            ? []
            : List<String>.from(
                json["phototype_ids_to_be_deleted"]!.map((x) => x.toString())),
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "phototype_ids_to_be_deleted": phototypeIDsToBeDeleted,
      };
}
