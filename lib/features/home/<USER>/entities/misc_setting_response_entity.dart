import 'dart:convert';

class MiscSettingResponseEntity {
  MiscSettingData data;

  MiscSettingResponseEntity({
    required this.data,
  });

  factory MiscSettingResponseEntity.fromRawJson(String str) =>
      MiscSettingResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MiscSettingResponseEntity.fromJson(Map<String, dynamic> json) =>
      MiscSettingResponseEntity(
        data: MiscSettingData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data.toJson(),
      };
}

class MiscSettingData {
  int reportEarlyHours;
  int reportLateHours;
  int radiusDistanceInMeters;
  int imageMinWidthThreshold;

  MiscSettingData({
    required this.reportEarlyHours,
    required this.reportLateHours,
    required this.radiusDistanceInMeters,
    required this.imageMinWidthThreshold,
  });

  factory MiscSettingData.fromRawJson(String str) =>
      MiscSettingData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MiscSettingData.fromJson(Map<String, dynamic> json) =>
      MiscSettingData(
        reportEarlyHours: json["report_early_hours"],
        reportLateHours: json["report_late_hours"],
        radiusDistanceInMeters: json["radius_distance_in_meters"],
        imageMinWidthThreshold: json["image_min_width_threshold"],
      );

  Map<String, dynamic> toJson() => {
        "report_early_hours": reportEarlyHours,
        "report_late_hours": reportLateHours,
        "radius_distance_in_meters": radiusDistanceInMeters,
        "image_min_width_threshold": imageMinWidthThreshold,
      };
}
