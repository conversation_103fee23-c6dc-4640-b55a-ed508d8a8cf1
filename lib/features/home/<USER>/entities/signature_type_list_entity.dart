import 'dart:convert';

/// Entity class for signature type list operations
/// Used for update_signaturetypes and add_signaturetypes processing
class SignatureTypeListModel {
  String? taskId;
  List<SignatureTypeModel>? signatureTypes;
  DateTime? modifiedTimeStampSignaturetypes;

  SignatureTypeListModel({
    this.taskId,
    this.signatureTypes,
    this.modifiedTimeStampSignaturetypes,
  });

  factory SignatureTypeListModel.fromRawJson(String str) =>
      SignatureTypeListModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SignatureTypeListModel.fromJson(Map<String, dynamic> json) =>
      SignatureTypeListModel(
        taskId: json["task_id"]?.toString(),
        signatureTypes: json["signature_types"] == null
            ? []
            : List<SignatureTypeModel>.from(json["signature_types"]!
                .map((x) => SignatureTypeModel.from<PERSON><PERSON>(x))),
        modifiedTimeStampSignaturetypes:
            json["modified_time_stamp_signaturetypes"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signaturetypes"]),
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "signature_types": signatureTypes == null
            ? []
            : List<dynamic>.from(signatureTypes!.map((x) => x.toJson())),
        "modified_time_stamp_signaturetypes":
            modifiedTimeStampSignaturetypes?.toIso8601String(),
      };
}

/// Individual signature type model
class SignatureTypeModel {
  String? signaturetypeId;
  String? signaturetypeName;
  bool? mandatory;
  DateTime? modifiedTimeStampSignaturetype;

  SignatureTypeModel({
    this.signaturetypeId,
    this.signaturetypeName,
    this.mandatory,
    this.modifiedTimeStampSignaturetype,
  });

  factory SignatureTypeModel.fromRawJson(String str) =>
      SignatureTypeModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SignatureTypeModel.fromJson(Map<String, dynamic> json) =>
      SignatureTypeModel(
        signaturetypeId: json["signaturetype_id"]?.toString(),
        signaturetypeName: json["signaturetype_name"],
        mandatory: json["mandatory"],
        modifiedTimeStampSignaturetype:
            json["modified_time_stamp_signaturetype"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signaturetype"]),
      );

  Map<String, dynamic> toJson() => {
        "signaturetype_id": signaturetypeId,
        "signaturetype_name": signaturetypeName,
        "mandatory": mandatory,
        "modified_time_stamp_signaturetype":
            modifiedTimeStampSignaturetype?.toIso8601String(),
      };
}
