import 'package:equatable/equatable.dart';

import 'dart:convert';

class ProfileResponseEntity {
  ProfileData? data;

  ProfileResponseEntity({
    this.data,
  });

  factory ProfileResponseEntity.fromRawJson(String str) =>
      ProfileResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProfileResponseEntity.fromJson(Map<String, dynamic> json) =>
      ProfileResponseEntity(
        data: json["data"] == null ? null : ProfileData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class ProfileData {
  String? token;
  int? userId;
  int? contractorId;
  int? countryId;
  int? stateId;
  String? firstName;
  String? lastName;
  String? address;
  String? email;
  String? country;
  String? state;
  String? suburb;
  String? postcode;
  String? pAddress;
  String? pSuburb;
  String? pPostcode;
  int? pCountryId;
  String? pCountry;
  String? pRegion;
  int? pRegionId;
  String? pDeliveryComment;
  String? mobile;
  String? profileImageUrl;
  String? modifiedTimeStampProfile;
  bool? adminAccess;
  bool? createTask;
  String? orgIDs;

  ProfileData({
    this.token,
    this.userId,
    this.contractorId,
    this.countryId,
    this.stateId,
    this.firstName,
    this.lastName,
    this.address,
    this.email,
    this.country,
    this.state,
    this.suburb,
    this.postcode,
    this.pAddress,
    this.pSuburb,
    this.pPostcode,
    this.pCountryId,
    this.pCountry,
    this.pRegion,
    this.pRegionId,
    this.pDeliveryComment,
    this.mobile,
    this.profileImageUrl,
    this.modifiedTimeStampProfile,
    this.adminAccess,
    this.createTask,
    this.orgIDs,
  });

  factory ProfileData.fromRawJson(String str) =>
      ProfileData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProfileData.fromJson(Map<String, dynamic> json) => ProfileData(
        token: json["token"],
        userId: json["user_id"],
        contractorId: json["contractor_id"],
        countryId: json["country_id"],
        stateId: json["state_id"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        address: json["address"],
        email: json["email"],
        country: json["country"],
        state: json["state"],
        suburb: json["suburb"],
        postcode: json["postcode"],
        pAddress: json["P_Address"],
        pSuburb: json["P_Suburb"],
        pPostcode: json["P_Postcode"],
        pCountryId: json["P_Country_id"],
        pCountry: json["P_Country"],
        pRegion: json["P_Region"],
        pRegionId: json["P_Region_id"],
        pDeliveryComment: json["P_Delivery_Comment"],
        mobile: json["mobile"],
        profileImageUrl: json["profile_image_url"],
        modifiedTimeStampProfile: json["modified_time_stamp_profile"],
        adminAccess: json["admin_access"],
        createTask: json["create_task"],
        orgIDs: json["OrgIDs"],
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "user_id": userId,
        "contractor_id": contractorId,
        "country_id": countryId,
        "state_id": stateId,
        "first_name": firstName,
        "last_name": lastName,
        "address": address,
        "email": email,
        "country": country,
        "state": state,
        "suburb": suburb,
        "postcode": postcode,
        "P_Address": pAddress,
        "P_Suburb": pSuburb,
        "P_Postcode": pPostcode,
        "P_Country_id": pCountryId,
        "P_Country": pCountry,
        "P_Region": pRegion,
        "P_Region_id": pRegionId,
        "P_Delivery_Comment": pDeliveryComment,
        "mobile": mobile,
        "profile_image_url": profileImageUrl,
        "modified_time_stamp_profile": modifiedTimeStampProfile,
        "admin_access": adminAccess,
        "create_task": createTask,
        "OrgIDs": orgIDs,
      };

  ProfileDataEntity toEntity() => ProfileDataEntity(
        token: token,
        userId: userId,
        contractorId: contractorId,
        countryId: countryId,
        stateId: stateId,
        firstName: firstName,
        lastName: lastName,
        address: address,
        email: email,
        country: country,
        state: state,
        suburb: suburb,
        postcode: postcode,
        pAddress: pAddress,
        pSuburb: pSuburb,
        pPostcode: pPostcode,
        pCountryId: pCountryId,
        pCountry: pCountry,
        pRegion: pRegion,
        pRegionId: pRegionId,
        pDeliveryComment: pDeliveryComment,
        mobile: mobile,
        profileImageUrl: profileImageUrl,
        modifiedTimeStampProfile: modifiedTimeStampProfile,
        adminAccess: adminAccess,
        createTask: createTask,
        orgIDs: orgIDs,
      );
}

class ProfileDataEntity extends Equatable {
  final String? token;
  final int? userId;
  final int? contractorId;
  final int? countryId;
  final int? stateId;
  final String? firstName;
  final String? lastName;
  final String? address;
  final String? email;
  final String? country;
  final String? state;
  final String? suburb;
  final String? postcode;
  final String? pAddress;
  final String? pSuburb;
  final String? pPostcode;
  final int? pCountryId;
  final String? pCountry;
  final String? pRegion;
  final int? pRegionId;
  final String? pDeliveryComment;
  final String? mobile;
  final String? profileImageUrl;
  final String? modifiedTimeStampProfile;
  final bool? adminAccess;
  final bool? createTask;
  final String? orgIDs;

  const ProfileDataEntity({
    this.token,
    this.userId,
    this.contractorId,
    this.countryId,
    this.stateId,
    this.firstName,
    this.lastName,
    this.address,
    this.email,
    this.country,
    this.state,
    this.suburb,
    this.postcode,
    this.pAddress,
    this.pSuburb,
    this.pPostcode,
    this.pCountryId,
    this.pCountry,
    this.pRegion,
    this.pRegionId,
    this.pDeliveryComment,
    this.mobile,
    this.profileImageUrl,
    this.modifiedTimeStampProfile,
    this.adminAccess,
    this.createTask,
    this.orgIDs,
  });

  @override
  List<Object?> get props => [
        token,
        userId,
        contractorId,
        countryId,
        stateId,
        firstName,
        lastName,
        address,
        email,
        country,
        state,
        suburb,
        postcode,
        pAddress,
        pSuburb,
        pPostcode,
        pCountryId,
        pCountry,
        pRegion,
        pRegionId,
        pDeliveryComment,
        mobile,
        profileImageUrl,
        modifiedTimeStampProfile,
        adminAccess,
        createTask,
        orgIDs,
      ];
}
