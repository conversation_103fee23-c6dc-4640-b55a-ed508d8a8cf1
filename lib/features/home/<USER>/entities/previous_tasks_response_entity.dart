import 'dart:convert';

import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class PreviousTasksResponseEntity {
  Data? data;

  PreviousTasksResponseEntity({
    this.data,
  });

  factory PreviousTasksResponseEntity.fromRawJson(String str) =>
      PreviousTasksResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PreviousTasksResponseEntity.fromJson(Map<String, dynamic> json) =>
      PreviousTasksResponseEntity(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  List<PreviousTaskEntity>? previousTask;

  Data({
    this.previousTask,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        previousTask: json["previousTask"] == null
            ? []
            : List<PreviousTaskEntity>.from(json["previousTask"]!
                .map((x) => PreviousTaskEntity.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "previousTask": previousTask == null
            ? []
            : List<dynamic>.from(previousTask!.map((x) => x.toJson())),
      };
}

class PreviousTaskEntity {
  int? taskId;
  int? projectId;
  int? scheduleId;
  String? completedBy;
  String? cycleName;
  String? taskComment;
  DateTime? dateSchedule;
  DateTime? dateStart;
  DateTime? dateFinish;
  List<PhotoFolder>? photoFolder;
  List<SignatureFolder>? signatureFolder;
  List<Form>? forms;

  PreviousTaskEntity({
    this.taskId,
    this.projectId,
    this.scheduleId,
    this.completedBy,
    this.cycleName,
    this.taskComment,
    this.dateSchedule,
    this.dateStart,
    this.dateFinish,
    this.photoFolder,
    this.signatureFolder,
    this.forms,
  });

  factory PreviousTaskEntity.fromRawJson(String str) =>
      PreviousTaskEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PreviousTaskEntity.fromJson(Map<String, dynamic> json) =>
      PreviousTaskEntity(
        taskId: json["task_id"],
        projectId: json["project_id"],
        scheduleId: json["schedule_id"],
        completedBy: json["completed_by"],
        cycleName: json["cycle_name"],
        taskComment: json["task_comment"],
        dateSchedule: json["date_schedule"] == null
            ? null
            : DateTime.parse(json["date_schedule"]),
        dateStart: json["date_start"] == null
            ? null
            : DateTime.parse(json["date_start"]),
        dateFinish: json["date_finish"] == null
            ? null
            : DateTime.parse(json["date_finish"]),
        photoFolder: json["photo_folder"] == null
            ? []
            : List<PhotoFolder>.from(json["photo_folder"]!.map((x) => PhotoFolder.fromJson(x))),
        signatureFolder: json["signature_folder"] == null
            ? []
            : List<SignatureFolder>.from(json["signature_folder"]!.map((x) => SignatureFolder.fromJson(x))),
        forms: json["forms"] == null
            ? []
            : List<Form>.from(json["forms"]!.map((x) => Form.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "project_id": projectId,
        "schedule_id": scheduleId,
        "completed_by": completedBy,
        "cycle_name": cycleName,
        "task_comment": taskComment,
        "date_schedule": dateSchedule?.toIso8601String(),
        "date_start": dateStart?.toIso8601String(),
        "date_finish": dateFinish?.toIso8601String(),
        "photo_folder": photoFolder == null
            ? []
            : List<PhotoFolder>.from(photoFolder!.map((x) => x)),
        "signature_folder": signatureFolder == null
            ? []
            : List<SignatureFolder>.from(signatureFolder!.map((x) => x)),
        "forms": forms == null
            ? []
            : List<Form>.from(forms!.map((x) => x.toJson())),
      };
}