import 'dart:convert';
import 'package:storetrack_app/features/home/<USER>/entities/vacancy_entity.dart';

class VacanciesResponseEntity {
  VacanciesDataEntity data;

  VacanciesResponseEntity({
    required this.data,
  });

  factory VacanciesResponseEntity.fromRawJson(String str) =>
      VacanciesResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VacanciesResponseEntity.fromJson(Map<String, dynamic> json) =>
      VacanciesResponseEntity(
        data: VacanciesDataEntity.fromJson(json["data"] ?? {}),
      );

  Map<String, dynamic> toJson() => {
        "data": data.toJson(),
      };
}

class VacanciesDataEntity {
  List<VacancyEntity> vacancies;

  VacanciesDataEntity({
    required this.vacancies,
  });

  factory VacanciesDataEntity.fromRawJson(String str) =>
      VacanciesDataEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VacanciesDataEntity.fromJson(Map<String, dynamic> json) =>
      VacanciesDataEntity(
        vacancies: json["vacancies"] != null
            ? List<VacancyEntity>.from(
                json["vacancies"].map((x) => _vacancyFromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        "vacancies":
            List<dynamic>.from(vacancies.map((x) => _vacancyToJson(x))),
      };

  static VacancyEntity _vacancyFromJson(Map<String, dynamic> json) =>
      VacancyEntity(
        id: json["rec_id"] ?? 0,
        jobTitle: json["pos_title"] ?? "",
        jobLocation: _formatLocation(json["suburb"], json["state"]),
        jobDescription: json["pos_description"] ?? "",
        companyName: null, // Not provided in API response
        salaryRange: null, // Not provided in API response
        employmentType: null, // Not provided in API response
        postedDate: null, // Not provided in API response
        canApply: true, // Default to true
        canRefer: true, // Default to true
      );

  static Map<String, dynamic> _vacancyToJson(VacancyEntity vacancy) => {
        "rec_id": vacancy.id,
        "pos_title": vacancy.jobTitle,
        "suburb": vacancy.jobLocation.split(',').first.trim(),
        "state": vacancy.jobLocation.split(',').length > 1
            ? vacancy.jobLocation.split(',')[1].trim()
            : "",
        "pos_description": vacancy.jobDescription,
        "company_name": vacancy.companyName,
        "salary_range": vacancy.salaryRange,
        "employment_type": vacancy.employmentType,
        "posted_date": vacancy.postedDate?.toIso8601String(),
        "can_apply": vacancy.canApply,
        "can_refer": vacancy.canRefer,
      };

  static String _formatLocation(dynamic suburb, dynamic state) {
    final suburbStr = (suburb ?? "").toString().trim();
    final stateStr = (state ?? "").toString().trim();

    if (suburbStr.isEmpty && stateStr.isEmpty) return "";
    if (suburbStr.isEmpty) return stateStr;
    if (stateStr.isEmpty) return suburbStr;

    return "$suburbStr, $stateStr";
  }
}
