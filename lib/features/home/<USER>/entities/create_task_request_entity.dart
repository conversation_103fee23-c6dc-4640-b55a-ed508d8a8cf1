import 'dart:convert';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/models/coverage_store_model.dart';

class CreateTaskRequestEntity extends Equatable {
  final String userId;
  final String token;
  final String scheduledDate;
  final int taskDuration;
  final List<CoverageStore> coverageStores;
  final int clientId;

  const CreateTaskRequestEntity({
    required this.userId,
    required this.token,
    required this.scheduledDate,
    required this.taskDuration,
    required this.coverageStores,
    required this.clientId,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'token': token,
      'scheduled_date': scheduledDate,
      'task_duration': taskDuration,
      'coverageStores': coverageStores.map((store) => store.toJson()).toList(),
      'client_id': clientId,
    };
  }

  factory CreateTaskRequestEntity.fromJson(Map<String, dynamic> json) {
    return CreateTaskRequestEntity(
      userId: json['user_id'] ?? '',
      token: json['token'] ?? '',
      scheduledDate: json['scheduled_date'] ?? '',
      taskDuration: json['task_duration'] ?? 0,
      coverageStores: json['coverageStores'] != null
          ? (json['coverageStores'] as List)
              .map((item) => CoverageStore.fromJson(item))
              .toList()
          : [],
      clientId: json['client_id'] ?? 0,
    );
  }

  String toRawJson() => json.encode(toJson());

  factory CreateTaskRequestEntity.fromRawJson(String str) =>
      CreateTaskRequestEntity.fromJson(json.decode(str));

  @override
  List<Object?> get props => [
        userId,
        token,
        scheduledDate,
        taskDuration,
        coverageStores,
        clientId,
      ];
}
