import 'package:storetrack_app/features/home/<USER>/entities/create_task_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/create_task_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class CreateTaskUseCase {
  final HomeRepository _repository;

  CreateTaskUseCase(this._repository);

  Future<Result<CreateTaskResponseEntity>> call(
      CreateTaskRequestEntity request) async {
    return await _repository.createTasks(request);
  }
}
