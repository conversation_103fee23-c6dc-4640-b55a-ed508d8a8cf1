import 'package:storetrack_app/shared/models/result.dart';
import '../../data/models/notification_response.dart';
import '../../data/repositories/home_repository.dart';
import '../../data/models/notification_req.dart';

class GetAlertsUseCase {
  final HomeRepository repository;

  GetAlertsUseCase(this.repository);

  Future<Result<NotificationResponse>> call({
    required String token,
    required String userId,
    bool isSync = false,
  }) async {
    final request = NotificationReqParams(
      id: userId,
      token: token,
    );

    return await repository.getAlerts(
      request,
      isSync: isSync,
    );
  }
}
