import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/shared/models/result.dart';

class SaveInductionUseCase {
  final HomeRepository repository;

  SaveInductionUseCase(this.repository);

  Future<Result<bool>> call({
    required String token,
    required String userId,
    required List<Map<String, dynamic>> inductions,
  }) async {
    return await repository.updateInduction(
      token: token,
      userId: userId,
      inductions: inductions,
    );
  }
}
