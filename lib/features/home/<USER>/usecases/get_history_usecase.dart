import 'package:storetrack_app/shared/models/result.dart';
import '../../data/repositories/home_repository.dart';
import '../../data/models/history_response.dart';

class GetHistoryUseCase {
  final HomeRepository repository;

  GetHistoryUseCase(this.repository);

  Future<Result<HistoryResponse>> call({
    required String token,
    required String userId,
    bool isSync = false,
  }) async {
    return await repository.getHistory(
      token: token,
      userId: userId,
      isSync: isSync,
    );
  }
}
