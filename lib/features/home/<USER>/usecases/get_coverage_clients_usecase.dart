import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/repositories/home_repository.dart';
import '../../data/models/coverage_client_model.dart';

class GetCoverageClientsUseCase
    implements
        UseCase<Result<CoverageClientsResponse>, GetCoverageClientsParams> {
  final HomeRepository repository;

  GetCoverageClientsUseCase(this.repository);

  @override
  Future<Result<CoverageClientsResponse>> call(
      GetCoverageClientsParams params) async {
    return await repository.getCoverageClients(
      userId: params.userId,
      token: params.token,
    );
  }
}

class GetCoverageClientsParams {
  final String userId;
  final String token;

  GetCoverageClientsParams({
    required this.userId,
    required this.token,
  });
}
