import '../../../../shared/models/result.dart';
import '../../data/models/leave_response.dart';
import '../../data/repositories/home_repository.dart';

class GetLeaveUseCase {
  final HomeRepository _repository;

  GetLeaveUseCase(this._repository);

  Future<Result<LeaveResponse>> call({
    required String token,
    required String userId,
    bool isSync = false,
  }) async {
    return await _repository.getLeave(
      token: token,
      userId: userId,
      isSync: isSync,
    );
  }
}
