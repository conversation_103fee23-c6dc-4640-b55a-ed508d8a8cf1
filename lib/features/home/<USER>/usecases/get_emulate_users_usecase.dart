import '../../../../shared/models/result.dart';
import '../entities/emulate_user_request_entity.dart';
import '../entities/emulate_user_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class GetEmulateUsersUseCase {
  final HomeRepository _repository;

  GetEmulateUsersUseCase(this._repository);

  Future<Result<EmulateUserResponseEntity>> call({
    required String userId,
    required String token,
  }) async {
    final request = EmulateUserRequestEntity(
      userId: userId,
      token: token,
    );

    return await _repository.getEmulateUsers(request);
  }
}
