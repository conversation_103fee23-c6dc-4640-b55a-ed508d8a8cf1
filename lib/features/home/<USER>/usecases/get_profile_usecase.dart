import '../../../../shared/models/result.dart';
import '../entities/profile_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class GetProfileUseCase {
  final HomeRepository _repository;

  GetProfileUseCase(this._repository);

  Future<Result<ProfileResponseEntity>> call({
    required String token,
    required String userId,
    bool isSync = false,
  }) async {
    return await _repository.getProfile(
      token: token,
      userId: userId,
      isSync: isSync,
    );
  }
}
