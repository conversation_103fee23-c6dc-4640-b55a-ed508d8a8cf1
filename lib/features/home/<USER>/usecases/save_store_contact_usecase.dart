import 'package:storetrack_app/shared/models/result.dart';
import '../../data/models/store_contact_model.dart';
import '../../data/repositories/home_repository.dart';

class SaveStoreContactUseCase {
  final HomeRepository repository;

  SaveStoreContactUseCase(this.repository);

  Future<Result<bool>> call({
    required StoreContactRequest request,
  }) async {
    return await repository.updateStoreContact(
      request: request,
    );
  }
}
