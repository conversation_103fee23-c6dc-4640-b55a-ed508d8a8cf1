import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/repositories/home_repository.dart';
import '../../data/models/coverage_store_model.dart';

class GetCoverageStoresUseCase
    implements
        UseCase<Result<CoverageStoresResponse>, GetCoverageStoresParams> {
  final HomeRepository repository;

  GetCoverageStoresUseCase(this.repository);

  @override
  Future<Result<CoverageStoresResponse>> call(
      GetCoverageStoresParams params) async {
    return await repository.getCoverageStores(
      userId: params.userId,
      token: params.token,
    );
  }
}

class GetCoverageStoresParams {
  final String userId;
  final String token;

  GetCoverageStoresParams({
    required this.userId,
    required this.token,
  });
}
