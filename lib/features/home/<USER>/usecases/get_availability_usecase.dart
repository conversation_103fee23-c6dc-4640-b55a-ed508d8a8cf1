import 'package:storetrack_app/shared/models/result.dart';
import '../../data/models/availability_response.dart';
import '../../data/repositories/home_repository.dart';

class GetAvailabilityUseCase {
  final HomeRepository repository;

  GetAvailabilityUseCase(this.repository);

  Future<Result<AvailabilityResponse>> call({
    required String token,
    required String userId,
    bool isSync = false,
  }) async {
    return await repository.getAvailability(
      token: token,
      userId: userId,
      isSync: isSync,
    );
  }
}
