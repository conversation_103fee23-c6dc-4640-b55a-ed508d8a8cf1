import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/core/utils/task_utils.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';

import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/unschedule/unschedule_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/calendar_bottom_sheet.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';

import '../../../../config/themes/app_colors.dart';
import '../../../../core/storage/data_manager.dart';
import '../blocs/unschedule/unschedule_state.dart';

@RoutePage()
class UnscheduledPage extends StatefulWidget {
  const UnscheduledPage({super.key});

  @override
  State<UnscheduledPage> createState() => _UnscheduledPageState();
}

class _UnscheduledPageState extends State<UnscheduledPage> {
  bool _isCheckboxMode = false;
  String actualDeviceUid = '';
  late String actualUserId;
  final String actualAppVersion = AppConstants.appVersion;
  final List<String> actualTasksToUnschedule = [];
  late String actualUserToken;

  // Task lists
  List<TaskDetail> allApiTasks = []; // All tasks from API
  List<TaskDetail> unscheduledTasks = []; // Filtered unscheduled tasks
  List<TaskDetail> selectedItems = [];
  bool _areAllItemsSelected = false;

  // Calendar state
  DateTime selectedDate = DateTime.now();

  // Use a regular global key instead of a typed one to avoid errors
  final GlobalKey _storeListKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    _refreshData();
  }

  Future<void> _refreshData() async {
    try {
      // Get user ID, token, and device ID from DataManager
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();

      // Fetch unscheduled tasks
      if (mounted) {
        context.read<UnscheduleTaskCubit>().getData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize: ${e.toString()}',
        );
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Implementation of select all that works directly with the data
  void _selectAllItems() {
    setState(() {
      _areAllItemsSelected = !_areAllItemsSelected;

      if (_areAllItemsSelected) {
        // We're selecting all items - create a fresh list with all unscheduled tasks
        selectedItems = List.from(unscheduledTasks);
      } else {
        // We're deselecting all - clear the list
        selectedItems.clear();
      }
    });
  }

  // Filter tasks for the selected date
  void _filterTasksForSelectedDate() {
    if (allApiTasks.isEmpty) {
      // If we don't have any data yet, we need to make an API call
      _initializeData();
      return;
    }

    // Filter unscheduled tasks for display
    unscheduledTasks = allApiTasks
        .where((task) =>
            task.taskStatus == "Tentative" &&
            task.taskId != 0 &&
            task.isOpen == false)
        .toList()
      ..sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));
  }

  // Build the scrollable content that includes task list
  Widget _buildScrollableContent(BuildContext context) {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content section based on selected button
          _buildUnscheduledTaskList()
        ],
      ),
    );
  }

  // Build unscheduled tasks list content
  Widget _buildUnscheduledTaskList() {
    if (unscheduledTasks.isEmpty) {
      return const EmptyState(message: 'No unscheduled tasks available');
    }

    return ReorderableStoreList(
      key: _storeListKey,
      tasks: unscheduledTasks,
      isCalendarMode: _isCheckboxMode,
      showScheduledDate: false,
      showTickIndicator: true,
      showAllDisclosureIndicator: false,
      permanentlyDisableAllDisclosureIndicator: false,
      isOpenTask: false,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<UnscheduleTaskCubit, UnscheduleTaskState>(
        listener: (context, state) {
      if (state is UnscheduleTaskSuccess) {
        setState(() {
          var tasksResponse = state.tasksResponse;
          // Store all tasks from API
          allApiTasks = tasksResponse.addTasks ?? [];

          // Filter unscheduled tasks for display
          _filterTasksForSelectedDate();
        });
      } else if (state is UnscheduleTaskError) {
        // Show error message
        SnackBarService.error(
          context: context,
          message: state.message,
        );
      }
    }, builder: (context, state) {
      return Scaffold(
        backgroundColor: AppColors.lightGrey2,
        appBar: CustomAppBar(
          title: 'Unscheduled',
          actions: [
            IconButton(
              icon: Image.asset(
                AppAssets.calendarTime,
                width: 24,
                color:
                    _isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
              ),
              onPressed: () {
                setState(() {
                  _isCheckboxMode = !_isCheckboxMode;
                  if (!_isCheckboxMode) {
                    // Clear selection when exiting checkbox mode
                    selectedItems.clear();
                    _areAllItemsSelected = false;
                  }
                });
              },
            ),
          ],
        ),
        body: state is UnscheduleTaskLoading
            ? const Center(child: CircularProgressIndicator())
            : state is UnscheduleTaskError
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading tasks',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: _initializeData,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  )
                : BlocListener<SyncCubit, SyncState>(
                    listener: (context, state) {
                      if (state is SyncSuccess) {
                        _refreshData();
                      }
                    },
                    child: RefreshIndicator(
                      onRefresh: _initializeData,
                      child: _buildScrollableContent(context),
                    ),
                  ),
        floatingActionButton: _isCheckboxMode &&
                unscheduledTasks.isNotEmpty &&
                selectedItems.isNotEmpty
            ? Container(
                height: 56,
                decoration: BoxDecoration(
                  color: AppColors.midGrey,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.all(8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isCheckboxMode = false;
                          selectedItems.clear();
                          _areAllItemsSelected = false;
                        });
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.close_rounded,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    const Gap(8),
                    SizedBox(
                      width: 96,
                      child: AppButton(
                        text: _areAllItemsSelected
                            ? "Deselect all"
                            : "Select all",
                        color: Colors.white,
                        textColor: AppColors.black,
                        onPressed: _selectAllItems,
                        height: 40,
                      ),
                    ),
                    const Gap(8),
                    SizedBox(
                      width: 96,
                      child: AppButton(
                        text: "Schedule",
                        color: AppColors.primaryBlue,
                        onPressed: () {
                          // Show calendar bottom sheet
                          _showCalendarBottomSheet(context);
                        },
                        height: 40,
                      ),
                    ),
                  ],
                ),
              )
            : null,
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      );
    });
  }

  void _handleSelectionChanged(
      List<TaskDetail> allItems, List<TaskDetail> selectedItems) {
    setState(() {
      this.selectedItems = selectedItems;
      _areAllItemsSelected = selectedItems.length == unscheduledTasks.length &&
          unscheduledTasks.isNotEmpty;
    });
  }

  void _showCalendarBottomSheet(BuildContext context) {
    // Store selected items count to avoid async gap issues
    final itemsCount = selectedItems.length;

    // Get all valid task start dates
    // We need to find the LATEST start date as the minimum allowed date
    final rangeStartDates = selectedItems
        .map((datum) => datum.rangeStart)
        .whereType<DateTime>()
        .toList();

    // Get all valid task end dates or expiry dates
    // We need to find the EARLIEST end date as the maximum allowed date
    final rangeEndDates = selectedItems
        .map((datum) => datum.expires ?? datum.rangeEnd)
        .whereType<DateTime>()
        .toList();

    // Set default dates if no valid dates are found
    DateTime? minAllowedDate;
    DateTime? maxAllowedDate;

    // Only apply date restrictions if we have valid dates
    if (rangeStartDates.isNotEmpty) {
      // Find the latest start date (most restrictive minimum)
      minAllowedDate = rangeStartDates.reduce((a, b) => a.isAfter(b) ? a : b);
    }

    if (rangeEndDates.isNotEmpty) {
      // Find the earliest end date (most restrictive maximum)
      maxAllowedDate = rangeEndDates.reduce((a, b) => a.isBefore(b) ? a : b);
    }

    // Check if the min date is after the max date (no valid date range)
    // if (minAllowedDate != null &&
    //     maxAllowedDate != null &&
    //     minAllowedDate.isAfter(maxAllowedDate)) {
    //   SnackBarService.warning(
    //     context: context,
    //     message:
    //         'No common date range available for the selected tasks. The latest start date (${DateFormat('MMM dd, yyyy').format(minAllowedDate)}) is after the earliest end date (${DateFormat('MMM dd, yyyy').format(maxAllowedDate)}).',
    //   );
    //   return;
    // }

    // Get calendar response and cubit before the async gap
    final calendarResponse = (context.read<UnscheduleTaskCubit>().state
            is UnscheduleTaskSuccess)
        ? (context.read<UnscheduleTaskCubit>().state as UnscheduleTaskSuccess)
            .calendarResponse
        : null;

    // Get the cubit before the async gap
    final UnscheduleTaskCubit cubit = context.read<UnscheduleTaskCubit>();

    // Get all scheduled tasks to show grey circles for dates with tasks
    List<DateTime> taskDates = [];
    if (cubit.state is UnscheduleTaskSuccess) {
      var tasksResponse = (cubit.state as UnscheduleTaskSuccess).tasksResponse;
      var allTasks = tasksResponse.addTasks ?? [];

      // Add scheduled tasks to taskDates
      for (var task in allTasks) {
        if (task.scheduledTimeStamp != null &&
            task.taskStatus == "Confirmed" &&
            task.isOpen != true) {
          taskDates.add(task.scheduledTimeStamp!);
        }
      }
    }

    // Define the callback function to handle date selection
    void handleDateSelection(DateTime? selectedDate) async {
      // Check if the widget is still mounted and a date was selected
      if (selectedDate != null && mounted && selectedItems.isNotEmpty) {
        // submission timestamp is current time in timezone "Australia/NSW", locale "en", "AU"
        final submissionTimeStamp = DateTime.now();

        // Show scheduling message
        SnackBarService.info(
          context: context,
          message:
              'Scheduling $itemsCount tasks for ${DateFormat('MMM dd, yyyy').format(selectedDate)}',
        );

        // Update scheduling data for each selected task in the database
        for (var task in selectedItems) {
          await TaskUtils.updateTaskSchedulingData(
            taskId: task.taskId.toString(),
            scheduledTimeStamp: selectedDate,
            submissionTimeStamp: submissionTimeStamp,
            taskStatus: "Confirmed",
            submissionState: 1,
          );
        }

        await SyncService().sync();

        // Clear selection after scheduling
        setState(() {
          _isCheckboxMode = false;
          selectedItems.clear();
          _areAllItemsSelected = false;
        });
      }
    }

    // Show calendar bottom sheet
    CalendarBottomSheet.show(
      context: context,
      // minDate: minAllowedDate,
      // maxDate: maxAllowedDate,
      calendarResponse: calendarResponse,
      taskDates: taskDates,
    ).then(handleDateSelection);
  }
}
