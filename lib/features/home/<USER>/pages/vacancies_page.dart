import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/vacancies/vacancies_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/vacancy_card.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/retry_widget.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_profile_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/pages/referral_form_page.dart';

@RoutePage()
class VacanciesPage extends StatelessWidget {
  const VacanciesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<VacanciesCubit>()..fetchVacancies(),
      child: const VacanciesView(),
    );
  }
}

class VacanciesView extends StatelessWidget {
  const VacanciesView({super.key});

  Future<void> _showApplyConfirmationDialog(
      BuildContext context, int vacancyId) async {
    // Fetch user profile data for confirmation dialog
    final dataManager = sl<DataManager>();
    final token = await dataManager.getAuthToken();
    final userId = await dataManager.getUserId();

    if (token == null || userId == null) {
      SnackBarService.error(
        context: context,
        message: 'Authentication required. Please login again.',
      );
      return;
    }

    // Get user profile for confirmation display
    final profileUseCase = sl<GetProfileUseCase>();
    final profileResult = await profileUseCase.call(
      token: token,
      userId: userId,
    );

    if (!context.mounted) return;

    if (!profileResult.isSuccess || profileResult.data == null) {
      SnackBarService.error(
        context: context,
        message: 'Failed to load user profile. Please try again.',
      );
      return;
    }

    final userData = profileResult.data!.data!;
    final userName =
        '${userData.firstName ?? ''} ${userData.lastName ?? ''}'.trim();
    final userEmail = userData.email ?? '';
    final userSuburb = userData.suburb ?? '';
    final userState = userData.state ?? '';
    final userPhone = userData.mobile ?? '';

    final messageDetails = [
      if (userName.isNotEmpty) 'Name: $userName',
      if (userEmail.isNotEmpty) 'Email: $userEmail',
      if (userSuburb.isNotEmpty) 'Suburb: $userSuburb',
      if (userState.isNotEmpty) 'State: $userState',
      if (userPhone.isNotEmpty) 'Phone: $userPhone',
    ].join('\n');

    final message =
        'Are you sure you want to apply for this vacancy?\n\n$messageDetails';

    // Show confirmation dialog
    ConfirmDialog.show(
      context: context,
      title: 'Confirm Application',
      message: message,
      confirmText: 'Confirm',
      cancelText: 'No',
      onConfirm: () {
        context.read<VacanciesCubit>().applyToVacancy(vacancyId);
      },
    );
  }

  void _showReferDialog(BuildContext context, int vacancyId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<VacanciesCubit>(),
          child: ReferralFormPage(vacancyId: vacancyId),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Vacancies',
        showBackButton: true,
      ),
      body: BlocConsumer<VacanciesCubit, VacanciesState>(
        listener: (context, state) {
          if (state is VacancyActionSuccess) {
            SnackBarService.success(
              context: context,
              message: state.message,
            );
          } else if (state is VacancyActionError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
        },
        builder: (context, state) {
          if (state is VacanciesLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else if (state is VacanciesError) {
            return RetryWidget(
              color: AppColors.lightGrey2,
              onRetry: () {
                context.read<VacanciesCubit>().fetchVacancies();
              },
            );
          } else if (state is VacanciesLoaded ||
              state is VacancyActionInProgress ||
              state is VacancyActionSuccess ||
              state is VacancyActionError) {
            final vacancies = _getVacanciesFromState(state);

            if (vacancies.isEmpty) {
              return const EmptyState(message: 'No Vacancies Available');
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<VacanciesCubit>().fetchVacancies();
              },
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 8),
                itemCount: vacancies.length,
                itemBuilder: (context, index) {
                  final vacancy = vacancies[index];
                  final isApplying = state is VacancyActionInProgress &&
                      state.vacancyId == vacancy.id &&
                      state.action == 'apply';
                  final isReferring = state is VacancyActionInProgress &&
                      state.vacancyId == vacancy.id &&
                      state.action == 'refer';

                  return VacancyCard(
                    vacancy: vacancy,
                    isApplying: isApplying,
                    isReferring: isReferring,
                    onApply: () {
                      _showApplyConfirmationDialog(context, vacancy.id);
                    },
                    onRefer: () {
                      _showReferDialog(context, vacancy.id);
                    },
                  );
                },
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  List<dynamic> _getVacanciesFromState(VacanciesState state) {
    if (state is VacanciesLoaded) {
      return state.vacancies;
    } else if (state is VacancyActionInProgress) {
      return state.vacancies;
    } else if (state is VacancyActionSuccess) {
      return state.vacancies;
    } else if (state is VacancyActionError) {
      return state.vacancies;
    }
    return [];
  }
}
