import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/date_time_utils.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/auto_schedule/auto_schedule_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import 'package:storetrack_app/di/service_locator.dart';

@RoutePage()
class AutoSchedulePage extends StatefulWidget {
  const AutoSchedulePage({super.key});

  @override
  State<AutoSchedulePage> createState() => _AutoSchedulePageState();
}

class _AutoSchedulePageState extends State<AutoSchedulePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late WebViewController _webViewController;
  bool _isWebViewLoading = true;
  String _selectedView = 'schedule'; // 'grid' or 'schedule'
  int _selectedDayOffset = 0; // 0, 7, 14, 21 for the 4 weeks
  String _selectedButton = 'week1'; // 'week1', 'week2', 'week3', 'week4'

  // Week commencing date strings for tab labels
  late List<String> _weekLabels;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);
    _initializeWebView();
    _setupWeekLabels();

    // Load initial auto schedule data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context
          .read<AutoScheduleCubit>()
          .loadAutoScheduleLink(dayOffset: _selectedDayOffset);
    });
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isWebViewLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isWebViewLoading = false;
            });
          },
        ),
      );
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        switch (_tabController.index) {
          case 0:
            _selectedDayOffset = 0;
            _selectedButton = 'week1';
            break;
          case 1:
            _selectedDayOffset = 7;
            _selectedButton = 'week2';
            break;
          case 2:
            _selectedDayOffset = 14;
            _selectedButton = 'week3';
            break;
          case 3:
            _selectedDayOffset = 21;
            _selectedButton = 'week4';
            break;
        }
      });

      // Load auto schedule content for the selected week
      _refreshAutoScheduleContent(_selectedDayOffset);
    }
  }

  void _handleButtonPressed(String buttonKey) {
    setState(() {
      _selectedButton = buttonKey;
      switch (buttonKey) {
        case 'week1':
          _selectedDayOffset = 0;
          _tabController.animateTo(0);
          break;
        case 'week2':
          _selectedDayOffset = 7;
          _tabController.animateTo(1);
          break;
        case 'week3':
          _selectedDayOffset = 14;
          _tabController.animateTo(2);
          break;
        case 'week4':
          _selectedDayOffset = 21;
          _tabController.animateTo(3);
          break;
      }
    });

    // Load auto schedule content for the selected week
    _refreshAutoScheduleContent(_selectedDayOffset);
  }

  void _setupWeekLabels() {
    _weekLabels = [
      'WC ${DateTimeUtils.formatWeekCommencingForTab(DateTimeUtils.getWeekCommencingDate(-1))}',
      'WC ${DateTimeUtils.formatWeekCommencingForTab(DateTimeUtils.getWeekCommencingDate(6))}',
      'WC ${DateTimeUtils.formatWeekCommencingForTab(DateTimeUtils.getWeekCommencingDate(13))}',
      'WC ${DateTimeUtils.formatWeekCommencingForTab(DateTimeUtils.getWeekCommencingDate(20))}',
    ];
  }

  void _refreshAutoScheduleContent(int dayOffset) {
    if (_selectedView == 'schedule') {
      // Only refresh if we're in schedule view
      context
          .read<AutoScheduleCubit>()
          .loadAutoScheduleLink(dayOffset: dayOffset);
    }
  }

  @override
  void dispose() {
    // Call sync when returning to dashboard from auto schedule
    sl<SyncService>().sync();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AutoScheduleCubit, AutoScheduleState>(
      listener: (context, state) {
        if (state is AutoScheduleLoaded) {
          // Load the URL into the WebView when we get the link
          _webViewController.loadRequest(Uri.parse(state.autoScheduleUrl));
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.lightGrey2,
        appBar: CustomAppBar(
          title: 'Auto Schedule',
          backgroundColor: Colors.white,
          titleColor: AppColors.black,
          // actions: [
          //   // Toggle between grid and schedule view
          //   IconButton(
          //     onPressed: () {
          //       setState(() {
          //         _selectedView = _selectedView == 'grid' ? 'schedule' : 'grid';
          //       });

          //       // If switching to schedule view, load the content for current tab
          //       if (_selectedView == 'schedule') {
          //         context.read<AutoScheduleCubit>().loadAutoScheduleLink(dayOffset: _selectedDayOffset);
          //       }
          //     },
          // icon: Icon(
          //   _selectedView == 'grid' ? Icons.calendar_view_week : Icons.grid_view,
          //   color: AppColors.primaryBlue,
          // ),
          // ),
          // ],
          bottom: _selectedView == 'schedule'
              ? PreferredSize(
                  preferredSize: const Size.fromHeight(48),
                  child: Container(
                    color: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Container(
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F5F5),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFFE0E0E0),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          // Week 1 button
                          _buildToggleButton(
                            context,
                            _weekLabels[0],
                            'week1',
                            const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              bottomLeft: Radius.circular(8),
                            ),
                          ),
                          // Week 2 button
                          _buildToggleButton(
                            context,
                            _weekLabels[1],
                            'week2',
                            BorderRadius.zero,
                          ),
                          // Week 3 button
                          _buildToggleButton(
                            context,
                            _weekLabels[2],
                            'week3',
                            BorderRadius.zero,
                          ),
                          // Week 4 button
                          _buildToggleButton(
                            context,
                            _weekLabels[3],
                            'week4',
                            const BorderRadius.only(
                              topRight: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              : null,
        ),
        body: BlocBuilder<AutoScheduleCubit, AutoScheduleState>(
          builder: (context, state) {
            if (state is AutoScheduleLoading) {
              return _buildLoadingView();
            } else if (state is AutoScheduleError) {
              return _buildErrorView(state.message, context);
            } else {
              return _buildContent();
            }
          },
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_selectedView == 'grid') {
      return _buildGridView();
    } else {
      return _buildScheduleView();
    }
  }

  Widget _buildGridView() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(15),
      child: GridView.count(
        crossAxisCount: 3,
        childAspectRatio: 1.0,
        mainAxisSpacing: 0,
        crossAxisSpacing: 0,
        children: [
          _buildGridItem(
            'Auto Schedule Tasks',
            Icons.schedule,
            onTap: () {
              setState(() {
                _selectedView = 'schedule';
              });
              // Load schedule content for current tab
              context
                  .read<AutoScheduleCubit>()
                  .loadAutoScheduleLink(dayOffset: _selectedDayOffset);
            },
          ),
          // Add more grid items as needed
        ],
      ),
    );
  }

  Widget _buildGridItem(String title, IconData icon, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        margin: const EdgeInsets.all(4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 40,
              color: AppColors.primaryBlue,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: Theme.of(context)
                    .textTheme
                    .montserratNavigationPrimaryMedium
                    .copyWith(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: AppColors.blackTint1,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleView() {
    return Stack(
      children: [
        // WebView for auto schedule content
        WebViewWidget(controller: _webViewController),

        // Loading indicator overlay
        if (_isWebViewLoading)
          Container(
            color: Colors.white,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColors.primaryBlue,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Loading auto schedule...',
                    style: TextStyle(
                      color: AppColors.blackTint1,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildLoadingView() {
    return Container(
      color: AppColors.lightGrey2,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppColors.primaryBlue,
            ),
            SizedBox(height: 16),
            Text(
              'Loading auto schedule...',
              style: TextStyle(
                color: AppColors.blackTint1,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(String message, BuildContext context) {
    return Container(
      color: AppColors.lightGrey2,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.blackTint1,
              ),
              const SizedBox(height: 16),
              Text(
                'Error',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  context
                      .read<AutoScheduleCubit>()
                      .loadAutoScheduleLink(dayOffset: _selectedDayOffset);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildToggleButton(
    BuildContext context,
    String text,
    String buttonKey,
    BorderRadius borderRadius,
  ) {
    final bool isSelected = _selectedButton == buttonKey;

    return Expanded(
      child: ElevatedButton(
        onPressed: () => _handleButtonPressed(buttonKey),
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor:
              isSelected ? AppColors.primaryBlue : const Color(0xFFF5F5F5),
          foregroundColor: isSelected ? Colors.white : AppColors.blackTint1,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
            side: const BorderSide(
              color: Colors.grey,
              width: 0,
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          text,
          style: Theme.of(context)
              .textTheme
              .montserratNavigationPrimaryMedium
              .copyWith(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w600,
                fontSize: 10,
              ),
        ),
      ),
    );
  }
}
