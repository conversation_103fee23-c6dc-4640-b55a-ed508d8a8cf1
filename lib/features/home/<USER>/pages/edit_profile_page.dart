import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import '../blocs/edit_profile/edit_profile_cubit.dart';
import '../../domain/entities/profile_response_entity.dart';
import '../../data/models/update_profile_request.dart';

@RoutePage()
class EditProfilePage extends StatelessWidget {
  final ProfileResponseEntity? profileData;

  const EditProfilePage({
    super.key,
    this.profileData,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<EditProfileCubit>(),
      child: _EditProfileView(profileData: profileData),
    );
  }
}

class _EditProfileView extends StatefulWidget {
  final ProfileResponseEntity? profileData;

  const _EditProfileView({this.profileData});

  @override
  State<_EditProfileView> createState() => _EditProfileViewState();
}

class _EditProfileViewState extends State<_EditProfileView> {
  // Form controllers
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _suburbController = TextEditingController();
  final TextEditingController _postcodeController = TextEditingController();
  final TextEditingController _deliveryController = TextEditingController();
  final TextEditingController _postalAddressController =
      TextEditingController();
  final TextEditingController _postalSuburbController = TextEditingController();
  final TextEditingController _postalPostcodeController =
      TextEditingController();

  // Dropdown values
  String? _selectedCountry;
  String? _selectedState;
  String? _selectedPostalCountry;
  String? _selectedPostalState;

  // Checkbox state
  bool _postalSameAsHome = false;

  // Profile data
  ProfileResponseEntity? _profileData;

  // Form key
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    // If profile data is passed, use it directly, otherwise load from API
    if (widget.profileData != null) {
      _prefillFormData(widget.profileData!);
    } else {
      _loadProfileData();
    }
  }

  /// Loads the current user's profile data from storage and triggers profile loading
  Future<void> _loadProfileData() async {
    final dataManager = sl<DataManager>();
    final token = await dataManager.getAuthToken();
    final userId = await dataManager.getUserId();

    if (token != null && userId != null) {
      if (mounted) {
        context.read<EditProfileCubit>().loadProfile(
              token: token,
              userId: userId,
            );
      }
    }
  }

  /// Pre-fills form fields with existing profile data
  void _prefillFormData(ProfileResponseEntity profileResponse) {
    final data = profileResponse.data;
    if (data != null) {
      setState(() {
        _profileData = profileResponse;

        // Prefill basic information
        _mobileController.text = data.mobile ?? '';
        _addressController.text = data.address ?? '';
        _suburbController.text = data.suburb ?? '';
        _postcodeController.text = data.postcode ?? '';
        _deliveryController.text = data.pDeliveryComment ?? '';

        // Prefill postal address
        _postalAddressController.text = data.pAddress ?? '';
        _postalSuburbController.text = data.pSuburb ?? '';
        _postalPostcodeController.text = data.pPostcode ?? '';

        // Set dropdown selections
        _selectedCountry = data.country;
        _selectedState = data.state;
        _selectedPostalCountry = data.pCountry;
        _selectedPostalState = data.pRegion;
      });
    }
  }

  /// Handles the "postal same as home" checkbox toggle
  void _handlePostalSameAsHomeToggle(bool? value) {
    setState(() {
      _postalSameAsHome = value ?? false;

      if (_postalSameAsHome) {
        // Copy home address to postal address
        _postalAddressController.text = _addressController.text;
        _postalSuburbController.text = _suburbController.text;
        _postalPostcodeController.text = _postcodeController.text;
        _selectedPostalCountry = _selectedCountry;
        _selectedPostalState = _selectedState;
      } else {
        // Clear postal address fields
        _postalAddressController.clear();
        _postalSuburbController.clear();
        _postalPostcodeController.clear();
        _selectedPostalCountry = null;
        _selectedPostalState = null;
      }
    });
  }

  /// Saves the profile changes
  Future<void> _saveProfileChanges() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token == null || userId == null) {
          SnackBarService.error(
            context: context,
            message: 'Authentication error. Please login again.',
          );
          return;
        }

        // Helper function to get country ID from country name
        String getCountryId(String? country) {
          switch (country) {
            case 'Australia':
              return '14';
            case 'New Zealand':
              return '13';
            default:
              return '14'; // Default to Australia
          }
        }

        // Helper function to get state ID from state name
        String getStateId(String? state) {
          switch (state) {
            case 'NSW':
              return '4';
            case 'VIC':
              return '5';
            case 'QLD':
              return '6';
            case 'SA':
              return '7';
            case 'WA':
              return '8';
            case 'TAS':
              return '9';
            case 'NT':
              return '10';
            case 'ACT':
              return '11';
            default:
              return '4'; // Default to NSW
          }
        }

        final request = UpdateProfileRequest(
          userId: userId,
          token: token,
          address: _addressController.text.trim(),
          countryId: getCountryId(_selectedCountry),
          country: _selectedCountry ?? 'Australia',
          stateId: getStateId(_selectedState),
          state: _selectedState ?? 'NSW',
          suburb: _suburbController.text.trim(),
          postcode: _postcodeController.text.trim(),
          pAddress: _postalAddressController.text.trim(),
          pCountryId: getCountryId(_selectedPostalCountry),
          pCountry: _selectedPostalCountry ?? 'Australia',
          pSuburb: _postalSuburbController.text.trim(),
          pPostcode: _postalPostcodeController.text.trim(),
          pRegion: _selectedPostalState ?? 'NSW',
          pDeliveryComment: _deliveryController.text.trim(),
          mobile: _mobileController.text.trim(),
          deviceLatitude: 1.0, // Default values as per the example
          deviceLongitude: 1.0,
        );

        if (mounted) {
          context.read<EditProfileCubit>().updateProfile(request: request);
        }
      } catch (e) {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Error preparing profile update: ${e.toString()}',
          );
        }
      }
    }
  }

  @override
  void dispose() {
    // Dispose all controllers to prevent memory leaks
    _mobileController.dispose();
    _addressController.dispose();
    _suburbController.dispose();
    _postcodeController.dispose();
    _deliveryController.dispose();
    _postalAddressController.dispose();
    _postalSuburbController.dispose();
    _postalPostcodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Edit Profile',
      ),
      body: BlocListener<EditProfileCubit, EditProfileState>(
        listener: (context, state) {
          if (state is EditProfileLoaded) {
            _prefillFormData(state.profileResponse);
          } else if (state is EditProfileUpdateSuccess) {
            SnackBarService.success(
              context: context,
              message: 'Profile updated successfully!',
            );
            // Optionally navigate back or refresh the data
            _loadProfileData(); // Reload to get updated data
          } else if (state is EditProfileError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
        },
        child: BlocBuilder<EditProfileCubit, EditProfileState>(
          builder: (context, state) {
            if (state is EditProfileLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  color: AppColors.primaryBlue,
                ),
              );
            }

            return Stack(
              children: [
                Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        // Header Card
                        _buildHeaderCard(),
                        const Gap(16),

                        // Personal Information Card
                        _buildSectionCard(
                          title: 'Personal Information',
                          icon: Icons.person_outline,
                          children: [
                            _buildPersonalInfoFields(_profileData?.data),
                          ],
                        ),
                        const Gap(16),

                        // Address Information Card
                        _buildSectionCard(
                          title: 'Address Information',
                          icon: Icons.location_on_outlined,
                          children: [
                            _buildAddressInfoFields(),
                          ],
                        ),
                        const Gap(16),

                        // Delivery Instructions Card
                        _buildSectionCard(
                          title: 'Delivery Instructions',
                          icon: Icons.comment_outlined,
                          children: [
                            _buildDeliveryInstructionsField(),
                          ],
                        ),
                        const Gap(16),

                        // Postal Address Card
                        _buildSectionCard(
                          title: 'Postal Address',
                          icon: Icons.markunread_mailbox_outlined,
                          children: [
                            _buildPostalAddressFields(),
                          ],
                        ),
                        const Gap(24),

                        // Save button
                        _buildSaveButton(),
                        const Gap(24),
                      ],
                    ),
                  ),
                ),

                // Loading overlay when updating
                if (state is EditProfileUpdating)
                  Container(
                    color: AppColors.black.withValues(alpha: 0.3),
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.primaryBlue,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// Builds the header card with profile information
  Widget _buildHeaderCard() {
    final data = _profileData?.data;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Square profile image
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.borderColor,
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: data?.profileImageUrl?.isNotEmpty == true
                  ? Image.network(
                      data!.profileImageUrl!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultSquareAvatar();
                      },
                    )
                  : _buildDefaultSquareAvatar(),
            ),
          ),
          const Gap(16),
          Text(
            '${data?.firstName ?? 'John'} ${data?.lastName ?? 'Doe'}',
            style: AppTypography.montserratSemiBold.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: AppColors.black,
            ),
          ),
          const Gap(4),
          Text(
            data?.email ?? '<EMAIL>',
            style: AppTypography.montserratParagraphSmall.copyWith(
              color: AppColors.blackTint1,
              fontSize: 14,
            ),
          ),
          const Gap(12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.2)),
            ),
            child: Text(
              'Employee ID: ${data?.contractorId?.toString() ?? '123456789'}',
              style: AppTypography.montserratTitleXxsmall.copyWith(
                color: AppColors.primaryBlue,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const Gap(8),
          Text(
            'Update your personal information and preferences',
            style: AppTypography.montserratParagraphSmall.copyWith(
              color: AppColors.blackTint1,
              fontSize: 13,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Builds a section card with title and content
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: AppColors.primaryBlue,
                size: 20,
              ),
              const Gap(12),
              Text(
                title,
                style: AppTypography.montserratHeadingMedium.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.black,
                ),
              ),
            ],
          ),
          const Gap(20),
          ...children,
        ],
      ),
    );
  }

  /// Builds the default square avatar
  Widget _buildDefaultSquareAvatar() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: AppColors.lightGrey2,
        borderRadius: BorderRadius.circular(7),
      ),
      child: const Icon(
        Icons.person_rounded,
        size: 40,
        color: AppColors.blackTint1,
      ),
    );
  }

  /// Builds personal information fields
  Widget _buildPersonalInfoFields(dynamic data) {
    return Column(
      children: [
        _buildModernTextField(
          label: 'Mobile Number',
          controller: _mobileController,
          keyboardType: TextInputType.phone,
          hintText: 'Enter your mobile number',
          prefixIcon: Icons.phone_outlined,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Mobile number is required';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Builds address information fields
  Widget _buildAddressInfoFields() {
    return Column(
      children: [
        _buildModernTextField(
          label: 'Address',
          controller: _addressController,
          hintText: 'Enter your street address',
          prefixIcon: Icons.location_on_outlined,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Address is required';
            }
            return null;
          },
        ),
        const Gap(20),

        // Country and State row
        Row(
          children: [
            Expanded(
              child: _buildModernDropdownField(
                label: 'Country',
                value: _selectedCountry,
                items: ['Australia', 'New Zealand', 'United States'],
                onChanged: (value) => setState(() => _selectedCountry = value),
                prefixIcon: Icons.public_outlined,
                validator: (value) {
                  if (value == null) {
                    return 'Country is required';
                  }
                  return null;
                },
              ),
            ),
            const Gap(16),
            Expanded(
              child: _buildModernDropdownField(
                label: 'State',
                value: _selectedState,
                items: ['NSW', 'VIC', 'QLD', 'SA', 'WA', 'TAS', 'NT', 'ACT'],
                onChanged: (value) => setState(() => _selectedState = value),
                prefixIcon: Icons.map_outlined,
                validator: (value) {
                  if (value == null) {
                    return 'State is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const Gap(20),

        // Suburb and Postcode row
        Row(
          children: [
            Expanded(
              child: _buildModernTextField(
                label: 'Suburb',
                controller: _suburbController,
                hintText: 'Enter suburb',
                prefixIcon: Icons.home_work_outlined,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Suburb is required';
                  }
                  return null;
                },
              ),
            ),
            const Gap(16),
            Expanded(
              child: _buildModernTextField(
                label: 'Postcode',
                controller: _postcodeController,
                keyboardType: TextInputType.number,
                hintText: 'Enter postcode',
                prefixIcon: Icons.local_post_office_outlined,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Postcode is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds delivery instructions field
  Widget _buildDeliveryInstructionsField() {
    return _buildModernTextField(
      label: 'Special Instructions',
      controller: _deliveryController,
      hintText: 'Enter any special delivery instructions',
      prefixIcon: Icons.note_add_outlined,
      maxLines: 3,
    );
  }

  /// Builds postal address fields
  Widget _buildPostalAddressFields() {
    return Column(
      children: [
        // Modern checkbox
        _buildModernCheckbox(
          title: 'Postal Address Same As Home Address',
          value: _postalSameAsHome,
          onChanged: _handlePostalSameAsHomeToggle,
        ),

        // Postal address fields (show only if not same as home)
        if (!_postalSameAsHome) ...[
          const Gap(24),
          _buildModernTextField(
            label: 'Postal Address',
            controller: _postalAddressController,
            hintText: 'Enter postal address',
            prefixIcon: Icons.markunread_mailbox_outlined,
          ),
          const Gap(20),

          // Postal Country and State row
          Row(
            children: [
              Expanded(
                child: _buildModernDropdownField(
                  label: 'Postal Country',
                  value: _selectedPostalCountry,
                  items: ['Australia', 'New Zealand', 'United States'],
                  onChanged: (value) =>
                      setState(() => _selectedPostalCountry = value),
                  prefixIcon: Icons.public_outlined,
                ),
              ),
              const Gap(16),
              Expanded(
                child: _buildModernDropdownField(
                  label: 'Postal State',
                  value: _selectedPostalState,
                  items: ['NSW', 'VIC', 'QLD', 'SA', 'WA', 'TAS', 'NT', 'ACT'],
                  onChanged: (value) =>
                      setState(() => _selectedPostalState = value),
                  prefixIcon: Icons.map_outlined,
                ),
              ),
            ],
          ),
          const Gap(20),

          // Postal Suburb and Postcode row
          Row(
            children: [
              Expanded(
                child: _buildModernTextField(
                  label: 'Postal Suburb',
                  controller: _postalSuburbController,
                  hintText: 'Enter postal suburb',
                  prefixIcon: Icons.home_work_outlined,
                ),
              ),
              const Gap(16),
              Expanded(
                child: _buildModernTextField(
                  label: 'Postal Postcode',
                  controller: _postalPostcodeController,
                  keyboardType: TextInputType.number,
                  hintText: 'Enter postal postcode',
                  prefixIcon: Icons.local_post_office_outlined,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Builds the save button using AppButton
  Widget _buildSaveButton() {
    return AppButton(
      text: 'Save Changes',
      color: AppColors.primaryBlue,
      onPressed: _saveProfileChanges,
      height: 50,
    );
  }

  /// Builds a text field with traditional styling
  Widget _buildModernTextField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    String? hintText,
    IconData? prefixIcon,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.montserratTitleSmall,
        ),
        const Gap(8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hintText,
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppColors.blackTint1,
                    size: 20,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.primaryBlue,
                width: 1.5,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1.5),
            ),
            filled: true,
            fillColor: AppColors.lightGrey1,
            contentPadding: EdgeInsets.symmetric(
              horizontal: prefixIcon != null ? 12.0 : 16.0,
              vertical: maxLines > 1 ? 16.0 : 14.0,
            ),
            hintStyle: AppTypography.montserratParagraphSmall.copyWith(
              color: AppColors.blackTint1,
            ),
          ),
          style: AppTypography.montserratFormsfield,
        ),
      ],
    );
  }

  /// Builds a dropdown field with traditional styling
  Widget _buildModernDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required void Function(String?) onChanged,
    IconData? prefixIcon,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.montserratTitleSmall,
        ),
        const Gap(8),
        DropdownButtonFormField<String>(
          value: value,
          validator: validator,
          decoration: InputDecoration(
            hintText: 'Select...',
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppColors.blackTint1,
                    size: 20,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.primaryBlue,
                width: 1.5,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1.5),
            ),
            filled: true,
            fillColor: AppColors.lightGrey1,
            contentPadding: EdgeInsets.symmetric(
              horizontal: prefixIcon != null ? 12.0 : 16.0,
              vertical: 14.0,
            ),
          ),
          isExpanded: true,
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(
                item,
                style: AppTypography.montserratFormsfield,
              ),
            );
          }).toList(),
          onChanged: onChanged,
          icon: const Icon(
            Icons.keyboard_arrow_down_rounded,
            color: AppColors.blackTint1,
          ),
        ),
      ],
    );
  }

  /// Builds a checkbox widget with traditional styling
  Widget _buildModernCheckbox({
    required String title,
    required bool value,
    required void Function(bool?) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: value ? AppColors.lightGrey1 : AppColors.lightGrey1,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: value ? AppColors.primaryBlue : AppColors.borderColor,
          width: 1,
        ),
      ),
      child: GestureDetector(
        onTap: () => onChanged(!value),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: value ? AppColors.primaryBlue : Colors.transparent,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: value ? AppColors.primaryBlue : AppColors.blackTint2,
                  width: 2,
                ),
              ),
              child: value
                  ? const Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    )
                  : null,
            ),
            const Gap(12),
            Expanded(
              child: Text(
                title,
                style: AppTypography.montserratTitleSmall,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
