import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/services/photo_service.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/extensions/keyboard_type_extensions.dart';

import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/dropdown_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/checkbox_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/multi_select_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/counter_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/date_picker_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/radio_button_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';

@RoutePage()
class FlipPage extends StatefulWidget {
  final num? questionId;
  final num? taskId;
  final num? formId;

  const FlipPage({
    super.key,
    required this.questionId,
    this.taskId,
    this.formId,
  });

  @override
  State<FlipPage> createState() => _FlipPageState();
}

class _FlipPageState extends State<FlipPage> {
  // Core data
  Question? _question;
  List<QuestionPart> _questionParts = [];
  bool _isLoading = true;
  String? _errorMessage;

  // Measurement state (nested maps: questionPartId -> measurementId -> value)
  final Map<num, Map<num, dynamic>> _measurementValues = {};
  final Map<num, Map<num, String?>> _validationErrors = {};
  final Map<num, Map<num, String?>> _photoValidationErrors = {};
  final Map<num, Map<num, bool>> _widgetVisibility = {};
  final Map<num, Map<num, List<String>>> _measurementPhotos = {};

  // Card expansion state - only one card can be expanded at a time (accordion)
  int? _expandedCardIndex;

  // Photo service
  late final PhotoService _photoService;

  @override
  void initState() {
    super.initState();
    _photoService = sl<PhotoService>();
    _loadQuestionDataFromDatabase();
  }

  /// Get page title from first question part's description
  String get _pageTitle {
    if (_questionParts.isNotEmpty &&
        _questionParts.first.questionpartDescription != null) {
      return _questionParts.first.questionpartDescription!;
    }
    return 'Details';
  }

  /// Get the maximum number of measurements across all question parts
  /// According to user clarification, all question parts will have same number of measurements
  int get _maxMeasurementCount {
    if (_questionParts.isEmpty || _question?.measurements == null) return 0;
    return _question!.measurements!.length;
  }

  /// Load question and question parts data from database
  Future<void> _loadQuestionDataFromDatabase() async {
    if (widget.taskId == null ||
        widget.formId == null ||
        widget.questionId == null) {
      setState(() {
        _errorMessage = 'Missing required parameters';
        _isLoading = false;
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Use FormUtils to get task and form data
      final taskWithForm = await FormUtils.getTaskWithFormModel(
        widget.taskId!.toInt(),
        widget.formId!.toInt(),
      );

      if (taskWithForm == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Task or form not found in database';
        });
        return;
      }

      final taskEntity = taskWithForm['task'] as TaskDetail;
      final formEntity = taskEntity.forms
          ?.where((form) => form.formId == widget.formId)
          .firstOrNull;

      if (formEntity == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Form entity not found';
        });
        return;
      }

      // Find the question
      final questionEntity = formEntity.questions
          ?.where((q) => q.questionId == widget.questionId)
          .firstOrNull;

      if (questionEntity == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Question not found';
        });
        return;
      }

      logger(
          'FlipPage: Question loaded successfully: ${questionEntity.questionDescription}');
      logger(
          'FlipPage: Question has ${questionEntity.questionParts?.length ?? 0} question parts');
      logger(
          'FlipPage: Question has ${questionEntity.measurements?.length ?? 0} measurements');

      setState(() {
        _question = questionEntity;
        _questionParts = questionEntity.questionParts ?? [];
        _isLoading = false;
      });

      // Initialize form-related data after loading
      await _initializeFormData();

      logger('FlipPage: Initialization complete');
    } catch (e) {
      logger('FlipPage: ERROR loading question: ${e.toString()}');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading question: ${e.toString()}';
      });
    }
  }

  /// Initialize form-related data after question is loaded
  Future<void> _initializeFormData() async {
    // Initialize measurement state for all question parts
    _initializeMeasurementState();

    // Load saved data from database
    await _loadSavedData();

    // Load saved photos
    await _loadSavedPhotos();

    // Process initial conditional logic
    _processInitialConditionalLogic();

    logger('FlipPage: Form data initialization complete');
  }

  /// Initialize measurement state maps for all question parts
  void _initializeMeasurementState() {
    if (_question?.measurements == null) return;

    for (final questionPart in _questionParts) {
      if (questionPart.questionpartId == null) continue;

      final questionPartId = questionPart.questionpartId!;

      // Initialize maps for this question part
      _measurementValues[questionPartId] = {};
      _validationErrors[questionPartId] = {};
      _photoValidationErrors[questionPartId] = {};
      _widgetVisibility[questionPartId] = {};
      _measurementPhotos[questionPartId] = {};

      // Initialize each measurement
      for (final measurement in _question!.measurements!) {
        if (measurement.measurementId == null) continue;

        final measurementId = measurement.measurementId!;

        // Initialize visibility (default to true, conditional logic will update)
        _widgetVisibility[questionPartId]![measurementId] = true;

        // Initialize default values based on measurement type
        _measurementValues[questionPartId]![measurementId] =
            _getDefaultValue(measurement);

        // Initialize error states
        _validationErrors[questionPartId]![measurementId] = null;
        _photoValidationErrors[questionPartId]![measurementId] = null;

        // Initialize photo lists
        _measurementPhotos[questionPartId]![measurementId] = [];
      }
    }

    logger(
        'FlipPage: Initialized measurement state for ${_questionParts.length} question parts');
  }

  /// Get default value for a measurement based on its type
  dynamic _getDefaultValue(Measurement measurement) {
    switch (measurement.measurementTypeId) {
      case 9: // Date picker
        return null;
      case 1: // Text field
      case 2: // Text field
        return '';
      case 7: // Counter
        return 0;
      case 4: // Dropdown
      case 5: // Dropdown
      case 8: // Radio button
        return null;
      case 3: // Checkbox
        return false;
      case 6: // Multi-select
        return <String>[];
      default:
        return null;
    }
  }

  /// Load saved data from database for all question parts
  Future<void> _loadSavedData() async {
    if (widget.taskId == null ||
        widget.formId == null ||
        _question?.questionId == null) {
      logger('FlipPage: Missing required parameters for loading saved data');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        logger('FlipPage: Task not found for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        logger('FlipPage: Form not found for formId: ${widget.formId}');
        return;
      }

      // Get all saved QuestionAnswer objects for this question
      final questionAnswers = formModel.questionAnswers
          .where((qa) => qa.questionId == _question!.questionId!.toInt())
          .toList();

      if (questionAnswers.isEmpty) {
        logger('FlipPage: No saved answers found for this question');
        return;
      }

      logger(
          'FlipPage: Found ${questionAnswers.length} saved answers for question ${_question!.questionId}');

      // Populate measurement values from saved data
      for (final qa in questionAnswers) {
        if (qa.measurementId != null && qa.questionpartId != null) {
          final measurementId = qa.measurementId!;
          final questionPartId = qa.questionpartId!;

          // Find the measurement to determine its type
          final measurement = _findMeasurementById(measurementId);
          if (measurement == null) continue;

          // Restore value based on measurement type
          dynamic restoredValue =
              _restoreValueFromQuestionAnswer(qa, measurement);

          if (restoredValue != null) {
            setState(() {
              if (_measurementValues[questionPartId] != null) {
                _measurementValues[questionPartId]![measurementId] =
                    restoredValue;
              }
            });

            logger(
                'FlipPage: Restored value for questionPart $questionPartId, measurement $measurementId: $restoredValue');
          }
        }
      }
    } catch (e) {
      logger('FlipPage: Error loading saved data: $e');
    }
  }

  /// Find measurement by ID from the question's measurements list
  Measurement? _findMeasurementById(num measurementId) {
    final measurements = _question?.measurements ?? [];
    try {
      return measurements.firstWhere(
        (measurement) => measurement.measurementId == measurementId,
      );
    } catch (e) {
      return null;
    }
  }

  /// Restore value from QuestionAnswer based on measurement type
  dynamic _restoreValueFromQuestionAnswer(
      QuestionAnswerModel qa, Measurement measurement) {
    switch (measurement.measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        return qa.measurementTextResult ?? '';

      case 3: // Checkbox
        // Parse text result as boolean
        if (qa.measurementTextResult != null) {
          final textResult = qa.measurementTextResult!.toLowerCase();
          return textResult == 'true' || textResult == '1';
        }
        return false;

      case 4: // Dropdown
      case 5: // Dropdown
      case 8: // Radio button
        // Return the text result for dropdowns/radio buttons
        return qa.measurementTextResult;

      case 6: // Multi-select
        // Parse text result as list of strings
        if (qa.measurementTextResult != null) {
          try {
            // Assuming the text result is stored as comma-separated values
            final values = qa.measurementTextResult!.split(',');
            return values
                .map((v) => v.trim())
                .where((v) => v.isNotEmpty)
                .toList();
          } catch (e) {
            logger('FlipPage: Error parsing multi-select value: $e');
            return <String>[];
          }
        }
        return <String>[];

      case 7: // Counter
        // Parse text result as integer
        if (qa.measurementTextResult != null) {
          return int.tryParse(qa.measurementTextResult!) ?? 0;
        }
        return 0;

      case 9: // Date picker
        // Parse text result as DateTime string
        if (qa.measurementTextResult != null) {
          try {
            String dateString = qa.measurementTextResult!;
            return dateString;
          } catch (e) {
            logger('FlipPage: Error parsing date: $e');
            return null;
          }
        }
        return null;

      default:
        return qa.measurementTextResult;
    }
  }

  /// Load saved photos for all measurements
  Future<void> _loadSavedPhotos() async {
    if (widget.taskId == null || _question?.measurements == null) {
      logger(
          'FlipPage: TaskId or Measurements is null, cannot load saved photos');
      return;
    }

    try {
      // Clear existing photo data
      for (final questionPartId in _measurementPhotos.keys) {
        _measurementPhotos[questionPartId]?.clear();
      }

      final taskId = widget.taskId!.toInt();

      final savedPhotos = await _photoService.getPhotosFromTask(
        taskId: taskId,
        folderId: null,
      );

      // Filter photos for this question and level 3 (photo_tags_three)
      final filteredPhotos = savedPhotos.where((photo) {
        return photo.questionId == _question!.questionId!.toInt() &&
            photo.combineTypeId == 3; // Level 3 for photo_tags_three
      }).toList();

      if (filteredPhotos.isNotEmpty && mounted) {
        setState(() {
          // Group photos by question part ID and measurement ID
          for (final photo in filteredPhotos) {
            if (photo.questionpartId != null &&
                photo.measurementId != null &&
                photo.photoUrl != null) {
              final questionPartId = photo.questionpartId!;
              final measurementId = photo.measurementId!;

              // Ensure the nested maps exist
              if (!_measurementPhotos.containsKey(questionPartId)) {
                _measurementPhotos[questionPartId] = {};
              }
              if (!_measurementPhotos[questionPartId]!
                  .containsKey(measurementId)) {
                _measurementPhotos[questionPartId]![measurementId] = [];
              }

              _measurementPhotos[questionPartId]![measurementId]!
                  .add(photo.photoUrl!);
            }
          }
        });
      }

      logger('FlipPage: Loaded ${filteredPhotos.length} photos for FlipPage');
      logger('FlipPage: Photo distribution: $_measurementPhotos');
    } catch (e) {
      logger('FlipPage: Error loading saved photos: $e');
    }
  }

  /// Process initial conditional logic for all question parts
  void _processInitialConditionalLogic() {
    if (_question?.measurements == null || _questionParts.isEmpty) {
      logger(
          'FlipPage: No measurements or question parts for conditional logic');
      return;
    }

    logger(
        'FlipPage: Processing initial conditional logic for ${_questionParts.length} question parts');

    // Process conditional logic for each question part
    for (final questionPart in _questionParts) {
      if (questionPart.questionpartId == null) continue;

      final questionPartId = questionPart.questionpartId!;

      // Process each measurement in this question part
      for (final measurement in _question!.measurements!) {
        if (measurement.measurementId == null) continue;

        final measurementId = measurement.measurementId!;
        final currentValue = _measurementValues[questionPartId]?[measurementId];

        // Only process conditional logic for measurements that have values
        if (currentValue != null && _canTriggerConditionalLogic(measurement)) {
          _processConditionalLogic(
            questionPartId,
            measurementId,
            currentValue,
            isInitialization: true,
          );
        }
      }
    }

    logger('FlipPage: Initial conditional logic processing complete');
  }

  /// Check if a measurement can trigger conditional logic
  bool _canTriggerConditionalLogic(Measurement measurement) {
    // Check if this measurement has any conditional logic defined
    final hasConditions =
        (measurement.measurementConditions?.isNotEmpty ?? false) ||
            (measurement.measurementConditionsMultiple?.isNotEmpty ?? false);

    // Only certain widget types can trigger conditional logic
    final canTrigger = [3, 4, 5, 6, 8].contains(measurement.measurementTypeId);

    return hasConditions && canTrigger;
  }

  /// Process conditional logic for widget selections across all question parts
  /// This method applies conditional logic based on the question's is_mll property
  void _processConditionalLogic(
    num questionPartId,
    num measurementId,
    dynamic selectedValue, {
    bool isInitialization = false,
  }) {
    if (_question?.isMll == null || selectedValue == null) return;

    final measurement = _findMeasurementById(measurementId);
    if (measurement == null) return;

    logger(
        'FlipPage: Processing conditional logic for questionPart=$questionPartId, measurement=$measurementId, value=$selectedValue');

    // Get the selected option ID based on widget type
    final selectedOptionId =
        _getSelectedOptionIdForConditionalLogic(measurement, selectedValue);
    if (selectedOptionId == null) {
      logger('FlipPage: Could not determine option ID for conditional logic');
      return;
    }

    // Check conditions based on is_mll property
    if (_question!.isMll == false) {
      // Use measurement_conditions array
      _processConditions(
        measurement.measurementConditions,
        measurementId,
        selectedOptionId,
        isInitialization: isInitialization,
      );
    } else {
      // Use measurement_conditions_multiple array
      _processConditions(
        measurement.measurementConditionsMultiple,
        measurementId,
        selectedOptionId,
        isInitialization: isInitialization,
      );
    }
  }

  /// Get the measurement option ID for conditional logic based on widget type
  num? _getSelectedOptionIdForConditionalLogic(
      Measurement measurement, dynamic selectedValue) {
    switch (measurement.measurementTypeId) {
      case 3: // Checkbox
        // For checkboxes, use 1 for true and 2 for false
        return selectedValue == true ? 1 : 2;

      case 4: // Dropdown
      case 5: // Dropdown
      case 8: // Radio button
        return _getSelectedOptionId(measurement, selectedValue);

      case 6: // Multi-select
        // For multi-select, conditional logic might need special handling
        // For now, return null as multi-select conditional logic is complex
        return null;

      default:
        return null;
    }
  }

  /// Get the measurement option ID for the selected value
  num? _getSelectedOptionId(Measurement measurement, dynamic selectedValue) {
    if (measurement.measurementOptions == null || selectedValue == null) {
      return null;
    }

    try {
      final selectedOption = measurement.measurementOptions!
          .firstWhere((o) => o.measurementOptionDescription == selectedValue);
      return selectedOption.measurementOptionId;
    } catch (e) {
      return null;
    }
  }

  /// Process conditions list (either measurement_conditions or measurement_conditions_multiple)
  void _processConditions(
    List<dynamic>? conditions,
    num triggerMeasurementId,
    num selectedOptionId, {
    bool isInitialization = false,
  }) {
    if (conditions == null || conditions.isEmpty) return;

    for (final condition in conditions) {
      // Check if this condition matches the current trigger
      if (condition.measurementId == triggerMeasurementId &&
          condition.measurementOptionId == selectedOptionId) {
        final actionMeasurementId = condition.actionMeasurementId;
        final action = condition.action;

        if (actionMeasurementId != null && action != null) {
          logger(
              'FlipPage: Applying conditional action: $action to measurement $actionMeasurementId across all question parts');

          // Apply the action to this measurement across ALL question parts
          _applyConditionalActionToAllQuestionParts(
            actionMeasurementId,
            action,
            isInitialization: isInitialization,
          );
        }
      }
    }
  }

  /// Apply conditional action to a measurement across all question parts
  void _applyConditionalActionToAllQuestionParts(
    num targetMeasurementId,
    String action, {
    bool isInitialization = false,
  }) {
    for (final questionPart in _questionParts) {
      if (questionPart.questionpartId == null) continue;

      final questionPartId = questionPart.questionpartId!;

      // Check if this question part has this measurement
      if (_widgetVisibility[questionPartId]?.containsKey(targetMeasurementId) ==
          true) {
        final previousVisibility =
            _widgetVisibility[questionPartId]![targetMeasurementId] ?? true;

        // Update widget visibility based on action
        if (action.toLowerCase() == 'appear') {
          _widgetVisibility[questionPartId]![targetMeasurementId] = true;
        } else if (action.toLowerCase() == 'disappear') {
          _widgetVisibility[questionPartId]![targetMeasurementId] = false;

          // Clear validation errors when widget becomes hidden
          _validationErrors[questionPartId]?[targetMeasurementId] = null;
          _photoValidationErrors[questionPartId]?[targetMeasurementId] = null;

          // Only clear widget values during user interactions, not during initialization
          if (!isInitialization) {
            _clearWidgetValue(questionPartId, targetMeasurementId);
          }
        }

        // Clear validation errors if visibility changed
        final newVisibility =
            _widgetVisibility[questionPartId]![targetMeasurementId] ?? true;
        if (previousVisibility != newVisibility) {
          _validationErrors[questionPartId]?[targetMeasurementId] = null;
          _photoValidationErrors[questionPartId]?[targetMeasurementId] = null;
        }
      }
    }
  }

  /// Clear widget value based on measurement type
  void _clearWidgetValue(num questionPartId, num measurementId) {
    final measurement = _findMeasurementById(measurementId);
    if (measurement == null) {
      _measurementValues[questionPartId]?[measurementId] = null;
      return;
    }

    final defaultValue = _getDefaultValue(measurement);
    _measurementValues[questionPartId]?[measurementId] = defaultValue;
  }

  /// Handle save button press
  void _handleSave() async {
    logger('FlipPage: Handling save...');

    // Perform validation for all visible measurements
    bool hasValidationErrors = false;
    final allQuestionAnswers = <QuestionAnswer>[];

    for (final questionPart in _questionParts) {
      if (questionPart.questionpartId == null) continue;

      final questionPartId = questionPart.questionpartId!;

      for (final measurement in _question?.measurements ?? <Measurement>[]) {
        if (measurement.measurementId == null) continue;

        final measurementId = measurement.measurementId!;

        // Check if widget is visible
        final isVisible =
            _widgetVisibility[questionPartId]?[measurementId] ?? true;
        if (!isVisible) continue;

        // Get current value
        final currentValue = _measurementValues[questionPartId]?[measurementId];

        // Validate measurement
        final validationError = _validateMeasurement(measurement, currentValue);
        final photoError = _validatePhotos(questionPartId, measurement);

        // Update state with validation errors
        setState(() {
          _validationErrors[questionPartId]?[measurementId] = validationError;
          _photoValidationErrors[questionPartId]?[measurementId] = photoError;
        });

        if (validationError != null || photoError != null) {
          hasValidationErrors = true;
        }

        // Generate question answer for non-empty values
        if (!_isValueEmpty(currentValue, measurement.measurementTypeId)) {
          final questionAnswer = _generateSingleQuestionAnswer(
              questionPartId, measurement, currentValue);
          if (questionAnswer != null) {
            allQuestionAnswers.add(questionAnswer);
          }
        }
      }
    }

    if (hasValidationErrors) {
      // Show error message and don't save
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Please fix validation errors before saving',
        );
      }
      logger('FlipPage: Validation errors found, not saving');
      return;
    }

    // Save all question answers to database
    if (allQuestionAnswers.isNotEmpty) {
      final saveSuccess =
          await _saveQuestionAnswersToDatabase(allQuestionAnswers);

      if (mounted) {
        if (saveSuccess) {
          SnackBarService.success(
            context: context,
            message: 'Form saved successfully',
          );
          logger(
              'FlipPage: Successfully saved ${allQuestionAnswers.length} question answers');
        } else {
          SnackBarService.error(
            context: context,
            message: 'Error saving form data',
          );
          logger('FlipPage: Failed to save question answers');
        }
      }
    } else {
      if (mounted) {
        SnackBarService.info(
          context: context,
          message: 'No data to save',
        );
      }
      logger('FlipPage: No data to save');
    }
  }

  /// Auto-save all valid data when navigating back
  /// This method performs silent validation and saves only valid fields
  Future<void> _autoSaveOnNavigation() async {
    final measurements = _question?.measurements ?? [];
    final validQuestionAnswers = <QuestionAnswer>[];

    for (final questionPart in _questionParts) {
      if (questionPart.questionpartId == null) continue;

      final questionPartId = questionPart.questionpartId!;

      for (final measurement in measurements) {
        if (measurement.measurementId == null) continue;

        final measurementId = measurement.measurementId!;

        // Only process visible measurements
        final isVisible =
            _widgetVisibility[questionPartId]?[measurementId] ?? true;
        if (!isVisible) continue;

        final value = _measurementValues[questionPartId]?[measurementId];

        // Skip empty values
        if (_isValueEmpty(value, measurement.measurementTypeId)) continue;

        // Perform silent validation (no error display)
        final validationError = _validateMeasurement(measurement, value);
        final photoError = _validatePhotos(questionPartId, measurement);

        // Only include if all validations pass
        if (validationError == null && photoError == null) {
          final questionAnswer =
              _generateSingleQuestionAnswer(questionPartId, measurement, value);
          if (questionAnswer != null) {
            validQuestionAnswers.add(questionAnswer);
          }
        }
      }
    }

    // Save all valid answers if any exist
    if (validQuestionAnswers.isNotEmpty) {
      await _saveQuestionAnswersToDatabase(validQuestionAnswers);
      logger(
          'FlipPage: Auto-saved ${validQuestionAnswers.length} valid answers on navigation');
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop && !_isLoading) {
          // Auto-save valid data after navigation is confirmed
          await _autoSaveOnNavigation();
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.lightGrey2,
        appBar: CustomAppBar(
          title: _pageTitle,
          actions: [
            if (!_isLoading)
              IconButton(
                icon: const Icon(
                  Icons.save_rounded,
                  color: AppColors.primaryBlue,
                ),
                onPressed: _handleSave,
              ),
          ],
        ),
        body: _isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  color: AppColors.primaryBlue,
                ),
              )
            : _errorMessage != null
                ? _buildErrorState(textTheme)
                : _maxMeasurementCount == 0
                    ? const EmptyState(
                        message: 'No measurements available for this question')
                    : Column(
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                              child: Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Gap(12),
                                    ListView.separated(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16),
                                      itemCount: _maxMeasurementCount,
                                      itemBuilder: (context, index) =>
                                          _FlipCard(
                                        measurementIndex: index,
                                        measurement:
                                            _question!.measurements![index],
                                        questionParts: _questionParts,
                                        isExpanded: _expandedCardIndex == index,
                                        onToggleExpanded: () =>
                                            _toggleCardExpansion(index),
                                        measurementValues: _measurementValues,
                                        validationErrors: _validationErrors,
                                        photoValidationErrors:
                                            _photoValidationErrors,
                                        widgetVisibility: _widgetVisibility,
                                        measurementPhotos: _measurementPhotos,
                                        onMeasurementChanged:
                                            _updateMeasurementValue,
                                        onCameraTap: _handleAddPhotosTap,
                                      ),
                                      separatorBuilder: (context, index) =>
                                          const Gap(8),
                                    ),
                                    const Gap(24),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // Bottom progress bar
                          if (!_isLoading && _errorMessage == null)
                            _buildBottomProgressBar(),
                        ],
                      ),
      ),
    );
  }

  /// Build error state UI matching QPMDPage style
  Widget _buildErrorState(TextTheme textTheme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const Gap(16),
            Text(
              'Error loading data',
              style: textTheme.titleLarge?.copyWith(
                color: Colors.red.shade700,
              ),
            ),
            const Gap(8),
            Text(
              _errorMessage!,
              style: textTheme.bodyMedium?.copyWith(
                color: AppColors.blackTint1,
              ),
              textAlign: TextAlign.center,
            ),
            const Gap(24),
            ElevatedButton(
              onPressed: _loadQuestionDataFromDatabase,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build bottom progress bar matching QPMDPage style
  Widget _buildBottomProgressBar() {
    final progressInfo = _calculateProgress();
    final completedCards = progressInfo['completedCards'] as int;
    final totalCards = progressInfo['totalCards'] as int;
    final isCompleted = completedCards == totalCards && totalCards > 0;
    final progress = totalCards > 0 ? completedCards / totalCards : 0.0;

    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        color: isCompleted ? AppColors.green15 : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: isCompleted
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Completed',
                    style: AppTypography.montserratTitleExtraSmall,
                  ),
                  Icon(
                    Icons.check,
                    color: AppColors.black,
                    size: 20,
                  ),
                ],
              )
            : Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: progress,
                      backgroundColor: AppColors.green15,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        AppColors.loginGreen,
                      ),
                      minHeight: 4,
                    ),
                  ),
                  const Gap(32),
                  Text(
                    '$completedCards of $totalCards',
                    style: AppTypography.montserratTableSmall,
                  ),
                ],
              ),
      ),
    );
  }

  /// Toggle card expansion (accordion behavior - only one card expanded at a time)
  void _toggleCardExpansion(int index) {
    setState(() {
      if (_expandedCardIndex == index) {
        _expandedCardIndex = null; // Collapse if already expanded
      } else {
        _expandedCardIndex = index; // Expand this card, collapse others
      }
    });
  }

  /// Update measurement value and handle conditional logic
  void _updateMeasurementValue(
      num questionPartId, num measurementId, dynamic value) {
    setState(() {
      // Update the measurement value
      if (_measurementValues[questionPartId] != null) {
        _measurementValues[questionPartId]![measurementId] = value;
      }

      // Clear validation errors when value changes
      _validationErrors[questionPartId]?[measurementId] = null;
      _photoValidationErrors[questionPartId]?[measurementId] = null;
    });

    logger(
        'FlipPage: Updated measurement value: questionPart=$questionPartId, measurement=$measurementId, value=$value');

    // Check for conditional logic when widget values change
    final measurement = _findMeasurementById(measurementId);
    if (measurement != null && _canTriggerConditionalLogic(measurement)) {
      // Reset all conditional dependencies before processing new logic
      _resetConditionalDependencies(measurement);

      // Process conditional logic for this measurement change
      _processConditionalLogic(
        questionPartId,
        measurementId,
        value,
        isInitialization: false,
      );
    }

    // Auto-save the field if it's valid
    _autoSaveField(questionPartId, measurementId);
  }

  /// Reset all conditional dependencies for a measurement that's about to change
  void _resetConditionalDependencies(Measurement measurement) {
    if (measurement.measurementId == null) return;

    // Get all target measurement IDs that this measurement can affect
    final targetMeasurementIds = <num>{};

    // Check measurement_conditions array
    if (_question?.isMll == false &&
        measurement.measurementConditions != null) {
      for (final condition in measurement.measurementConditions!) {
        if (condition.actionMeasurementId != null) {
          targetMeasurementIds.add(condition.actionMeasurementId!);
        }
      }
    }

    // Check measurement_conditions_multiple array
    if (_question?.isMll == true &&
        measurement.measurementConditionsMultiple != null) {
      for (final condition in measurement.measurementConditionsMultiple!) {
        if (condition.actionMeasurementId != null) {
          targetMeasurementIds.add(condition.actionMeasurementId!);
        }
      }
    }

    // Hide all target widgets and clear their values across all question parts
    for (final targetId in targetMeasurementIds) {
      _hideWidgetAndCascadeAcrossAllQuestionParts(targetId);
    }
  }

  /// Hide a widget and cascade the hiding across all question parts
  void _hideWidgetAndCascadeAcrossAllQuestionParts(num measurementId) {
    for (final questionPart in _questionParts) {
      if (questionPart.questionpartId == null) continue;

      final questionPartId = questionPart.questionpartId!;

      // Hide the widget and clear its value
      if (_widgetVisibility[questionPartId]?.containsKey(measurementId) ==
          true) {
        setState(() {
          _widgetVisibility[questionPartId]![measurementId] = false;
          _validationErrors[questionPartId]?[measurementId] = null;
          _photoValidationErrors[questionPartId]?[measurementId] = null;

          // Clear the widget's value
          _clearWidgetValue(questionPartId, measurementId);
        });

        // Recursively hide dependent widgets
        final measurement = _findMeasurementById(measurementId);
        if (measurement != null) {
          _cascadeHideForMeasurement(measurement);
        }
      }
    }
  }

  /// Cascade hide for dependent measurements
  void _cascadeHideForMeasurement(Measurement measurement) {
    // Get all measurements that this measurement can affect through conditional logic
    final dependentMeasurementIds = <num>{};

    // Check measurement_conditions array
    if (_question?.isMll == false &&
        measurement.measurementConditions != null) {
      for (final condition in measurement.measurementConditions!) {
        if (condition.actionMeasurementId != null) {
          dependentMeasurementIds.add(condition.actionMeasurementId!);
        }
      }
    }

    // Check measurement_conditions_multiple array
    if (_question?.isMll == true &&
        measurement.measurementConditionsMultiple != null) {
      for (final condition in measurement.measurementConditionsMultiple!) {
        if (condition.actionMeasurementId != null) {
          dependentMeasurementIds.add(condition.actionMeasurementId!);
        }
      }
    }

    // Recursively hide dependent widgets
    for (final dependentId in dependentMeasurementIds) {
      _hideWidgetAndCascadeAcrossAllQuestionParts(dependentId);
    }
  }

  /// Auto-save a single field if it passes validation
  Future<void> _autoSaveField(num questionPartId, num measurementId) async {
    final measurement = _findMeasurementById(measurementId);
    if (measurement == null) return;

    final value = _measurementValues[questionPartId]?[measurementId];

    // Skip auto-save if value is empty
    if (_isValueEmpty(value, measurement.measurementTypeId)) return;

    // Perform silent validation (no error display)
    final validationError = _validateMeasurement(measurement, value);
    final photoError = _validatePhotos(questionPartId, measurement);

    // Only auto-save if all validations pass
    if (validationError == null && photoError == null) {
      // Create a single QuestionAnswer for this measurement
      final questionAnswer =
          _generateSingleQuestionAnswer(questionPartId, measurement, value);
      if (questionAnswer != null) {
        logger(
            'FlipPage: Auto-saving answer for questionPart $questionPartId, measurement $measurementId: $value');
        await _saveQuestionAnswersToDatabase([questionAnswer]);
      }
    }
  }

  /// Check if a value is empty based on measurement type
  bool _isValueEmpty(dynamic value, num? measurementTypeId) {
    if (value == null) return true;

    switch (measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        return (value as String).trim().isEmpty;
      case 3: // Checkbox
        return false; // Checkbox always has a value (true/false)
      case 4: // Dropdown
      case 5: // Dropdown
      case 8: // Radio button
      case 9: // Date picker
        return value == null;
      case 6: // Multi-select
        return (value as List).isEmpty;
      case 7: // Counter
        return value == 0;
      default:
        return value == null;
    }
  }

  /// Validate a single measurement value
  String? _validateMeasurement(Measurement measurement, dynamic value) {
    if (measurement.measurementValidations == null) return null;

    for (final validation in measurement.measurementValidations!) {
      // Validation Type 1: Required validation
      if (validation.validationTypeId == 1 && validation.required == true) {
        if (_isValueEmpty(value, measurement.measurementTypeId)) {
          return validation.errorMessage ?? 'This field is required';
        }
      }

      // Validation Type 2: Range validation
      if (validation.validationTypeId == 2 &&
          validation.rangeValidation != null &&
          validation.rangeValidation!.isNotEmpty) {
        if (!_isValueEmpty(value, measurement.measurementTypeId)) {
          final numValue = int.tryParse(value.toString());
          if (numValue != null &&
              !_validateRange(numValue, validation.rangeValidation!)) {
            return validation.errorMessage ??
                'Value is not within the valid range';
          }
        }
      }

      // Validation Type 3: Expression validation (regex)
      if (validation.validationTypeId == 3 &&
          validation.expressionValidation != null &&
          validation.expressionValidation!.isNotEmpty) {
        if (!_isValueEmpty(value, measurement.measurementTypeId)) {
          if (!_validateExpression(value, validation.expressionValidation!)) {
            return validation.errorMessage ??
                'Value does not match the required format';
          }
        }
      }
    }

    return null;
  }

  /// Validate range for numeric values
  bool _validateRange(int value, String rangeValidation) {
    try {
      final parts = rangeValidation.split('-');
      if (parts.length == 2) {
        final min = int.tryParse(parts[0].trim());
        final max = int.tryParse(parts[1].trim());
        if (min != null && max != null) {
          return value >= min && value <= max;
        }
      }
    } catch (e) {
      logger('FlipPage: Error validating range: $e');
    }
    return false;
  }

  /// Validate expression (regex)
  bool _validateExpression(dynamic value, String expression) {
    try {
      final regex = RegExp(expression);
      return regex.hasMatch(value.toString());
    } catch (e) {
      logger('FlipPage: Error validating expression: $e');
      return false;
    }
  }

  /// Validate photo requirements for a measurement
  String? _validatePhotos(num questionPartId, Measurement measurement) {
    // Photo validation will be implemented when needed
    return null;
  }

  /// Generate a single QuestionAnswer for a specific measurement
  QuestionAnswer? _generateSingleQuestionAnswer(
      num questionPartId, Measurement measurement, dynamic value) {
    if (measurement.measurementId == null) return null;

    // Find the question part to get questionpartMultiId if needed
    final questionPart = _questionParts
        .where((qp) => qp.questionpartId == questionPartId)
        .firstOrNull;

    final questionAnswer = QuestionAnswer(
      taskId: widget.taskId,
      flip: _question?.flip,
      formId: widget.formId,
      questionId: _question?.questionId,
      isComment: false,
      commentTypeId: null,
      questionpartId: questionPartId,
      questionPartMultiId: questionPart?.questionpartMultiId,
      measurementId: measurement.measurementId!,
      measurementTypeId: measurement.measurementTypeId,
    );

    // Set values based on measurement type
    switch (measurement.measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        questionAnswer.measurementTextResult = value?.toString();
        break;
      case 3: // Checkbox
        questionAnswer.measurementTextResult = value == true ? 'true' : 'false';
        break;
      case 4: // Dropdown
      case 5: // Dropdown
      case 8: // Radio button
        questionAnswer.measurementTextResult = value?.toString();
        break;
      case 6: // Multi-select
        if (value is List) {
          questionAnswer.measurementTextResult =
              (value as List<String>).join(',');
        }
        break;
      case 7: // Counter
        questionAnswer.measurementTextResult = value?.toString() ?? '0';
        break;
      case 9: // Date picker
        questionAnswer.measurementTextResult = value?.toString();
        break;
      default:
        questionAnswer.measurementTextResult = value?.toString();
    }

    return questionAnswer;
  }

  /// Save question answers to database
  Future<bool> _saveQuestionAnswersToDatabase(
      List<QuestionAnswer> questionAnswers) async {
    if (widget.taskId == null || widget.formId == null) {
      logger(
          'FlipPage: TaskId or FormId is null, cannot save question answers');
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        logger('FlipPage: Task not found in database');
        return false;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        logger('FlipPage: Form not found in task');
        return false;
      }

      // Convert QuestionAnswer entities to models
      final questionAnswerModels = questionAnswers
          .map((qa) => QuestionAnswerModel(
                taskId: qa.taskId?.toInt(),
                formId: qa.formId?.toInt(),
                questionId: qa.questionId?.toInt(),
                questionpartId: qa.questionpartId?.toInt(),
                measurementId: qa.measurementId?.toInt(),
                measurementTypeId: qa.measurementTypeId?.toInt(),
                measurementOptionId: qa.measurementOptionId?.toInt(),
                measurementTextResult: qa.measurementTextResult,
                isComment: qa.isComment,
                commentTypeId: qa.commentTypeId?.toInt(),
                questionPartMultiId: qa.questionPartMultiId,
              ))
          .toList();

      // Save to database in a write transaction
      realm.write(() {
        for (final newAnswer in questionAnswerModels) {
          // Remove existing answer for this specific measurement and question part
          final existingAnswer = formModel.questionAnswers
              .where((qa) =>
                  qa.questionId == newAnswer.questionId &&
                  qa.questionpartId == newAnswer.questionpartId &&
                  qa.measurementId == newAnswer.measurementId)
              .firstOrNull;

          if (existingAnswer != null) {
            formModel.questionAnswers.remove(existingAnswer);
          }

          // Add new answer
          formModel.questionAnswers.add(newAnswer);
        }
      });

      logger(
          'FlipPage: Successfully saved ${questionAnswerModels.length} question answers to database');
      return true;
    } catch (e) {
      logger('FlipPage: Error saving question answers to database: $e');
      return false;
    }
  }

  /// Handle photo tap for a measurement
  void _handleAddPhotosTap(num questionPartId, Measurement measurement) {
    if (measurement.measurementId == null) {
      logger('FlipPage: Measurement ID is null, cannot handle photo tap');
      return;
    }

    final measurementId = measurement.measurementId!;
    logger(
        'FlipPage: Handling photo tap: questionPart=$questionPartId, measurement=$measurementId');

    // For now, show a simple message
    // In a full implementation, this would open the camera/gallery and handle photo capture
    SnackBarService.info(
      context: context,
      message: 'Photo feature will be implemented later',
    );
  }

  /// Calculate progress for cards (completed cards / total cards)
  Map<String, dynamic> _calculateProgress() {
    if (_question?.measurements == null || _questionParts.isEmpty) {
      return {
        'completedCards': 0,
        'totalCards': 0,
        'progressPercentage': 0.0,
      };
    }

    final totalCards = _maxMeasurementCount;
    int completedCards = 0;

    // Check each measurement (card) for completion
    for (int measurementIndex = 0;
        measurementIndex < _maxMeasurementCount;
        measurementIndex++) {
      if (_isCardCompleted(measurementIndex)) {
        completedCards++;
      }
    }

    final progressPercentage =
        totalCards > 0 ? completedCards / totalCards : 0.0;

    return {
      'completedCards': completedCards,
      'totalCards': totalCards,
      'progressPercentage': progressPercentage,
    };
  }

  /// Check if a card (measurement across all question parts) is completed
  /// A card is completed when all mandatory question parts for that measurement are filled
  bool _isCardCompleted(int measurementIndex) {
    if (_question?.measurements == null ||
        measurementIndex >= _question!.measurements!.length) {
      return false;
    }

    final measurement = _question!.measurements![measurementIndex];
    if (measurement.measurementId == null) return false;

    final measurementId = measurement.measurementId!;

    // Check if measurement is required
    final isRequired = _isMeasurementRequired(measurement);

    // For non-required measurements, consider them completed if any question part has a value
    // For required measurements, all question parts must have valid values
    bool hasAnyValue = false;
    bool allRequiredPartsCompleted = true;

    for (final questionPart in _questionParts) {
      if (questionPart.questionpartId == null) continue;

      final questionPartId = questionPart.questionpartId!;

      // Check if this measurement is visible for this question part
      final isVisible =
          _widgetVisibility[questionPartId]?[measurementId] ?? true;
      if (!isVisible) continue;

      // Get the current value
      final currentValue = _measurementValues[questionPartId]?[measurementId];

      // Check if the value is valid (not empty)
      final hasValue =
          !_isValueEmpty(currentValue, measurement.measurementTypeId);

      if (hasValue) {
        hasAnyValue = true;

        // Perform validation to ensure the value is actually valid
        final validationError = _validateMeasurement(measurement, currentValue);
        final photoError = _validatePhotos(questionPartId, measurement);

        if (validationError != null || photoError != null) {
          allRequiredPartsCompleted = false;
        }
      } else if (isRequired) {
        // If this is a required measurement and this question part has no value,
        // the card is not completed
        allRequiredPartsCompleted = false;
      }
    }

    // Card is completed if:
    // - For required measurements: all question parts have valid values
    // - For non-required measurements: at least one question part has a valid value
    if (isRequired) {
      return allRequiredPartsCompleted && hasAnyValue;
    } else {
      return hasAnyValue;
    }
  }

  /// Check if measurement is required (extracted to separate method for reuse)
  bool _isMeasurementRequired(Measurement measurement) {
    if (measurement.measurementValidations == null) return false;

    for (final validation in measurement.measurementValidations!) {
      if (validation.validationTypeId == 1 && validation.required == true) {
        return true;
      }
    }
    return false;
  }
}

/// Individual expandable card widget for each measurement
class _FlipCard extends StatelessWidget {
  final int measurementIndex;
  final Measurement measurement;
  final List<QuestionPart> questionParts;
  final bool isExpanded;
  final VoidCallback onToggleExpanded;
  final Map<num, Map<num, dynamic>> measurementValues;
  final Map<num, Map<num, String?>> validationErrors;
  final Map<num, Map<num, String?>> photoValidationErrors;
  final Map<num, Map<num, bool>> widgetVisibility;
  final Map<num, Map<num, List<String>>> measurementPhotos;
  final Function(num questionPartId, num measurementId, dynamic value)
      onMeasurementChanged;
  final Function(num questionPartId, Measurement measurement) onCameraTap;

  const _FlipCard({
    required this.measurementIndex,
    required this.measurement,
    required this.questionParts,
    required this.isExpanded,
    required this.onToggleExpanded,
    required this.measurementValues,
    required this.validationErrors,
    required this.photoValidationErrors,
    required this.widgetVisibility,
    required this.measurementPhotos,
    required this.onMeasurementChanged,
    required this.onCameraTap,
  });

  @override
  Widget build(BuildContext context) {
    if (questionParts.isEmpty) return const SizedBox.shrink();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.borderColor,
          width: 1.0,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 8,
            offset: const Offset(0, 3),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 1. Measurement Title (shown once at the top) with enhanced background
          // Container(
          //   decoration: BoxDecoration(
          //     color: AppColors.lightGrey2.withValues(alpha: 0.3),
          //     borderRadius: const BorderRadius.only(
          //       topLeft: Radius.circular(12),
          //       topRight: Radius.circular(12),
          //     ),
          //   ),
          //   child: Padding(
          //     padding: const EdgeInsets.fromLTRB(16, 18, 16, 16),
          //     child: Text(
          //       measurement.measurementDescription ?? 'Measurement',
          //       style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
          //         fontWeight: FontWeight.w600,
          //         color: AppColors.black,
          //       ),
          //     ),
          //   ),
          // ),

          // Enhanced divider below the main title
          Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.lightGrey1.withValues(alpha: 0.3),
                  AppColors.lightGrey1,
                  AppColors.lightGrey1.withValues(alpha: 0.3),
                ],
              ),
            ),
          ),

          // 2. First Question Part (always visible)
          _buildQuestionPartWidget(context, questionParts.first,
              isFirstPart: true),

          // 3. Expandable section for remaining question parts
          if (isExpanded && questionParts.length > 1)
            _buildRemainingQuestionPartWidgets(context),

          // 4. Expand/Collapse button
          if (questionParts.length > 1) _buildExpandCollapseButton(),
        ],
      ),
    );
  }

  /// Build the list of remaining question parts for the expandable section.
  Widget _buildRemainingQuestionPartWidgets(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        color: AppColors.lightGrey2.withValues(alpha: 0.15),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Column(
        children: questionParts.skip(1).map((questionPart) {
          return _buildQuestionPartWidget(context, questionPart,
              isFirstPart: false);
        }).toList(),
      ),
    );
  }

  /// Build the UI for a single question part.
  Widget _buildQuestionPartWidget(
      BuildContext context, QuestionPart questionPart,
      {required bool isFirstPart}) {
    if (questionPart.questionpartId == null ||
        measurement.measurementId == null) {
      return const SizedBox.shrink();
    }

    final questionPartId = questionPart.questionpartId!;
    final measurementId = measurement.measurementId!;

    // Check visibility
    final isVisible = widgetVisibility[questionPartId]?[measurementId] ?? true;
    if (!isVisible) return const SizedBox.shrink();

    // Get current value and error states
    final currentValue = measurementValues[questionPartId]?[measurementId];
    final errorText = validationErrors[questionPartId]?[measurementId];
    final photoErrorText =
        photoValidationErrors[questionPartId]?[measurementId];
    final selectedImages =
        measurementPhotos[questionPartId]?[measurementId] ?? [];

    return Column(
      children: [
        if (!isFirstPart)
          Container(
            height: 1,
            margin: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.lightGrey1.withValues(alpha: 0.2),
                  AppColors.lightGrey1.withValues(alpha: 0.6),
                  AppColors.lightGrey1.withValues(alpha: 0.2),
                ],
              ),
            ),
          ),
        Padding(
          padding: EdgeInsets.all(isFirstPart ? 16 : 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Question part title with enhanced styling - only show for non-first items
              if (!isFirstPart) ...[
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: AppColors.lightGrey2.withValues(alpha: 0.4),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.borderColor.withValues(alpha: 0.5),
                      width: 0.5,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 16,
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue.withValues(alpha: 0.6),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const Gap(8),
                      Expanded(
                        child: Text(
                          questionPart.questionpartDescription ??
                              'Question Part',
                          style:
                              AppTypography.montserratTitleExtraSmall.copyWith(
                            color: AppColors.black,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const Gap(16),
              ],
              // Build actual measurement input widget
              _buildActualInputWidget(
                context,
                measurement,
                questionPartId,
                currentValue,
                errorText,
                photoErrorText,
                selectedImages,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Validate and cast current value to expected type based on measurement type
  T? _validateAndCastValue<T>(
      dynamic currentValue, num? measurementTypeId, T defaultValue) {
    if (currentValue == null) return defaultValue;

    try {
      switch (measurementTypeId) {
        case 1: // Text field
        case 2: // Text field
          if (T == String) {
            return (currentValue is String
                ? currentValue
                : currentValue.toString()) as T;
          }
          break;
        case 3: // Checkbox
          if (T == bool) {
            if (currentValue is bool) return currentValue as T;
            if (currentValue is String) {
              final lowerValue = currentValue.toLowerCase();
              return (lowerValue == 'true' || lowerValue == '1') as T;
            }
          }
          break;
        case 4: // Dropdown
        case 5: // Dropdown
        case 8: // Radio button
          if (T == String) {
            return (currentValue is String
                ? currentValue
                : currentValue.toString()) as T;
          }
          break;
        case 6: // Multi-select
          if (T == List<String>) {
            if (currentValue is List<String>) return currentValue as T;
            if (currentValue is List) {
              return currentValue.map((e) => e.toString()).toList() as T;
            }
            if (currentValue is String) {
              return currentValue
                  .split(',')
                  .map((e) => e.trim())
                  .where((e) => e.isNotEmpty)
                  .toList() as T;
            }
          }
          break;
        case 7: // Counter
          if (T == int) {
            if (currentValue is int) return currentValue as T;
            if (currentValue is num) return currentValue.toInt() as T;
            if (currentValue is String) {
              final parsed = int.tryParse(currentValue);
              if (parsed != null) return parsed as T;
            }
          }
          break;
        case 9: // Date picker
          if (T == String) {
            return (currentValue is String
                ? currentValue
                : currentValue.toString()) as T;
          }
          break;
        case null:
        default:
          // Unknown measurement type, fall through to default value
          break;
      }
    } catch (e) {
      logger(
          'FlipPage: Error validating value type for measurement type $measurementTypeId: $e');
    }

    logger(
        'FlipPage: Type mismatch - expected ${T.toString()} for measurement type $measurementTypeId, got ${currentValue.runtimeType}, using default');
    return defaultValue;
  }

  /// Get string value with type safety
  String _getStringValue(dynamic currentValue, num? measurementTypeId) {
    return _validateAndCastValue<String>(currentValue, measurementTypeId, '') ??
        '';
  }

  /// Get boolean value with type safety
  bool _getBoolValue(dynamic currentValue, num? measurementTypeId) {
    return _validateAndCastValue<bool>(
            currentValue, measurementTypeId, false) ??
        false;
  }

  /// Get integer value with type safety
  int _getIntValue(dynamic currentValue, num? measurementTypeId) {
    return _validateAndCastValue<int>(currentValue, measurementTypeId, 0) ?? 0;
  }

  /// Get list value with type safety
  List<String> _getListValue(dynamic currentValue, num? measurementTypeId) {
    return _validateAndCastValue<List<String>>(
            currentValue, measurementTypeId, <String>[]) ??
        <String>[];
  }

  /// Build the actual input widget (e.g., TextFormField, Dropdown) without the outer container.
  Widget _buildActualInputWidget(
    BuildContext context,
    Measurement measurement,
    num questionPartId,
    dynamic currentValue,
    String? errorText,
    String? photoErrorText,
    List<String> selectedImages,
  ) {
    final measurementId = measurement.measurementId;
    if (measurementId == null) return const SizedBox.shrink();

    // Get camera info for photos
    final cameraInfo = _getCameraIconInfo(measurement);
    final photoTag = _getPhotoTagForMeasurement(measurement);
    final textTheme = Theme.of(context).textTheme;

    // Build measurement widget based on type with type validation
    switch (measurement.measurementTypeId) {
      case 9: // Date picker
        final validValue =
            _getStringValue(currentValue, measurement.measurementTypeId);
        return DatePickerWidget(
          measurement: measurement,
          value: validValue.isEmpty ? null : validValue,
          onChanged: (value) =>
              onMeasurementChanged(questionPartId, measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: _isMeasurementRequired(measurement),
          onCameraTap: () => onCameraTap(questionPartId, measurement),
          errorText: errorText,
          selectedImages: selectedImages,
          photoErrorText: photoErrorText,
          photoTag: photoTag,
        );

      case 1: // Text field
      case 2: // Text field
        final validValue =
            _getStringValue(currentValue, measurement.measurementTypeId);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: TextEditingController(text: validValue),
              onChanged: (value) =>
                  onMeasurementChanged(questionPartId, measurementId, value),
              keyboardType: measurement.measurementTypeId?.keyboardType,
              decoration: InputDecoration(
                hintText: 'Enter your text here...',
                hintStyle: textTheme.montserratTitleExtraSmall.copyWith(
                  color: AppColors.blackTint1,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                    color:
                        errorText != null ? Colors.red : AppColors.blackTint2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                    color:
                        errorText != null ? Colors.red : AppColors.blackTint2,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                    color:
                        errorText != null ? Colors.red : AppColors.primaryBlue,
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 16.0,
                ),
                errorText: errorText,
                errorStyle: textTheme.montserratTableSmall.copyWith(
                  color: Colors.red,
                ),
              ),
              style: textTheme.montserratTitleExtraSmall.copyWith(
                color: AppColors.black,
              ),
            ),
            if (cameraInfo['show'] as bool) ...[
              const Gap(16),
              // Assuming PhotoUploadWidget doesn't have its own outer container
              // If it does, it will need to be refactored as well.
            ],
          ],
        );

      case 7: // Counter
        final validValue =
            _getIntValue(currentValue, measurement.measurementTypeId);
        return CounterWidget(
          measurement: measurement,
          value: validValue,
          onChanged: (value) =>
              onMeasurementChanged(questionPartId, measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: _isMeasurementRequired(measurement),
          onCameraTap: () => onCameraTap(questionPartId, measurement),
          errorText: errorText,
          selectedImages: selectedImages,
          photoErrorText: photoErrorText,
          photoTag: photoTag,
        );

      case 4: // Dropdown
      case 5: // Dropdown
        final validValue =
            _getStringValue(currentValue, measurement.measurementTypeId);
        return DropdownWidget(
          measurement: measurement,
          value: validValue.isEmpty ? null : validValue,
          onChanged: (value) =>
              onMeasurementChanged(questionPartId, measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: _isMeasurementRequired(measurement),
          onCameraTap: () => onCameraTap(questionPartId, measurement),
          errorText: errorText,
          selectedImages: selectedImages,
          photoErrorText: photoErrorText,
          photoTag: photoTag,
        );

      case 3: // Checkbox
        final validValue =
            _getBoolValue(currentValue, measurement.measurementTypeId);
        return CheckboxWidget(
          measurement: measurement,
          value: validValue,
          onChanged: (value) =>
              onMeasurementChanged(questionPartId, measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: _isMeasurementRequired(measurement),
          onCameraTap: () => onCameraTap(questionPartId, measurement),
          errorText: errorText,
          selectedImages: selectedImages,
          photoErrorText: photoErrorText,
          photoTag: photoTag,
        );

      case 6: // Multi-select
        final validValue =
            _getListValue(currentValue, measurement.measurementTypeId);
        return MultiSelectWidget(
          measurement: measurement,
          value: validValue,
          onChanged: (value) =>
              onMeasurementChanged(questionPartId, measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: _isMeasurementRequired(measurement),
          onCameraTap: () => onCameraTap(questionPartId, measurement),
          errorText: errorText,
          selectedImages: selectedImages,
          photoErrorText: photoErrorText,
          photoTag: photoTag,
        );

      case 8: // Radio button
        final validValue =
            _getStringValue(currentValue, measurement.measurementTypeId);
        return RadioButtonWidget(
          measurement: measurement,
          value: validValue.isEmpty ? null : validValue,
          onChanged: (value) =>
              onMeasurementChanged(questionPartId, measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: _isMeasurementRequired(measurement),
          onCameraTap: () => onCameraTap(questionPartId, measurement),
          selectedImages: selectedImages,
          photoErrorText: photoErrorText,
          photoTag: photoTag,
        );

      default:
        return Text(
          'Unsupported measurement type: ${measurement.measurementTypeId}',
          style: AppTypography.montserratTableSmall.copyWith(
            color: AppColors.blackTint1,
          ),
        );
    }
  }

  /// Check if measurement is required
  bool _isMeasurementRequired(Measurement measurement) {
    if (measurement.measurementValidations == null) return false;

    for (final validation in measurement.measurementValidations!) {
      if (validation.validationTypeId == 1 && validation.required == true) {
        return true;
      }
    }
    return false;
  }

  /// Get camera icon info for a measurement based on photo_tags_three array
  Map<String, dynamic> _getCameraIconInfo(Measurement measurement) {
    final result = {'show': false, 'isMandatory': false};

    // This will need to be implemented based on the question's photoTagsThree
    // For now, return false values
    return result;
  }

  /// Get photo tag for a measurement
  PhotoTagsT? _getPhotoTagForMeasurement(Measurement measurement) {
    // This will need to be implemented based on the question's photoTagsThree
    // For now, return null
    return null;
  }

  /// Build expand/collapse button
  Widget _buildExpandCollapseButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onToggleExpanded,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.lightGrey1.withValues(alpha: 0.1),
                AppColors.lightGrey1.withValues(alpha: 0.3),
              ],
            ),
            border: Border(
              top: BorderSide(
                color: AppColors.lightGrey1.withValues(alpha: 0.5),
                width: 0.5,
              ),
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: AppColors.primaryBlue,
                  size: 20,
                ),
              ),
              const Gap(12),
              Text(
                isExpanded
                    ? 'Show less'
                    : 'Show more (${questionParts.length - 1})',
                style: AppTypography.montserratTitleExtraSmall.copyWith(
                  color: AppColors.primaryBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
