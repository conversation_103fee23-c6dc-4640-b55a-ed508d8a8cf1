import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart' hide Form;
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/task_utils.dart';
import 'package:storetrack_app/core/utils/time_access_validator.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';

import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/blocs/schedule/schedule_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/schedule/schedule_state.dart';

import '../../../../core/storage/data_manager.dart';
import '../../domain/entities/tasks_response_entity.dart';
import '../../../home/<USER>/constants/action_types.dart';
import '../../../home/<USER>/widgets/calendar_bottom_sheet.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/services/location_service.dart';
import 'package:gap/gap.dart';
import '../widgets/all_tasks_list.dart';
import '../widgets/week_tasks_list.dart';
import '../widgets/overdue_tasks_list.dart';
import '../widgets/reschedule_floating_buttons.dart';
import '../../../../config/routes/app_router.gr.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';

@RoutePage()
class SchedulePage extends StatefulWidget {
  const SchedulePage({super.key});

  @override
  State<SchedulePage> createState() => _SchedulePageState();
}

class _SchedulePageState extends State<SchedulePage>
    with SingleTickerProviderStateMixin {
  _SchedulePageState(); // Add an unnamed constructor
  late TabController _tabController;
  bool isWeekView = true;
  late DateTime selectedDate; // Using late to initialize in initState
  DateTime?
      selectedCalendarDate; // This will track which date is selected in the calendar
  List<DateTime> weekDays = [];
  final DateTime _currentMonth = DateTime.now();
  List<DateTime> monthDays = [];

  // Track which button is currently selected
  String _selectedButton =
      'all'; // Options: 'all', 'this_week', 'next_week', 'overdue'

  // Task data
  String actualDeviceUid = '';
  late String actualUserId;
  final String actualAppVersion = AppConstants.appVersion;
  final List<String> actualTasksToSchedule = [];
  late String actualUserToken;

  List<TaskDetail> scheduledTasks = [];
  bool _isCheckboxMode = false;
  List<TaskDetail> selectedItems = [];
  bool _areAllItemsSelected = false;

  // Store all tasks from the API response
  List<schedule.TaskDetail> allApiTasks = [];

  // Map to track which dates have tasks
  Map<String, bool> datesWithTasks = {};

  // Convert schedule.Datum to unscheduled Datum
  TaskDetail convertScheduleToDatum(schedule.TaskDetail scheduleTask) {
    // Create a Datum with all necessary properties to prevent null values
    return TaskDetail(
      taskId: scheduleTask.taskId,
      taskStatus: scheduleTask.taskStatus,
      isOpen: scheduleTask.isOpen,
      storeName: scheduleTask.storeName,
      location: scheduleTask.location,
      scheduledTimeStamp: scheduleTask.scheduledTimeStamp,
      taskNote: scheduleTask.taskNote,
      posRequired: scheduleTask.posRequired,
      taskCount: scheduleTask.taskCount,
      teamlead: scheduleTask.teamlead,
      storeId: scheduleTask.storeId,
      client: scheduleTask.client,
      clientId: scheduleTask.clientId,
      clientLogoUrl: scheduleTask.clientLogoUrl,
      minutes: scheduleTask.minutes,
      budget: scheduleTask.budget,
      comment: scheduleTask.comment,
      suburb: scheduleTask.suburb,
      latitude: scheduleTask.latitude,
      longitude: scheduleTask.longitude,
      taskLatitude: scheduleTask.taskLatitude,
      taskLongitude: scheduleTask.taskLongitude,
      cycle: scheduleTask.cycle,
      cycleId: scheduleTask.cycleId,
      canDelete: scheduleTask.canDelete,
      submissionTimeStamp: scheduleTask.submissionTimeStamp,
      expires: scheduleTask.expires,
      onTask: scheduleTask.onTask,
      phone: scheduleTask.phone,
      rangeStart: scheduleTask.rangeStart,
      rangeEnd: scheduleTask.rangeEnd,
      reOpened: scheduleTask.reOpened,
      reOpenedReason: scheduleTask.reOpenedReason,
      warehousejobId: scheduleTask.warehousejobId,
      connoteUrl: scheduleTask.connoteUrl,
      isPosMandatory: scheduleTask.isPosMandatory,
      posReceived: scheduleTask.posReceived,
      photoFolder: scheduleTask.photoFolder,
      signatureFolder: scheduleTask.signatureFolder,
      forms: scheduleTask.forms,
      posItems: scheduleTask.posItems,
      documents: scheduleTask.documents,
      taskalerts: scheduleTask.taskalerts,
      taskmembers: scheduleTask.taskmembers,
      modifiedTimeStampDocuments: scheduleTask.modifiedTimeStampDocuments,
      modifiedTimeStampForms: scheduleTask.modifiedTimeStampForms,
      modifiedTimeStampMembers: scheduleTask.modifiedTimeStampMembers,
      modifiedTimeStampTask: scheduleTask.modifiedTimeStampTask,
      modifiedTimeStampPhotos: scheduleTask.modifiedTimeStampPhotos,
      modifiedTimeStampSignatures: scheduleTask.modifiedTimeStampSignatures,
      modifiedTimeStampSignaturetypes:
          scheduleTask.modifiedTimeStampSignaturetypes,
      posSentTo: scheduleTask.posSentTo,
      posSentToEmail: scheduleTask.posSentToEmail,
      modifiedTimeStampPhototypes: scheduleTask.modifiedTimeStampPhototypes,
      taskCommencementTimeStamp: scheduleTask.taskCommencementTimeStamp,
      taskStoppedTimeStamp: scheduleTask.taskStoppedTimeStamp,
      followupTasks: scheduleTask.followupTasks,
      stocktake: scheduleTask.stocktake,
      disallowReschedule: scheduleTask.disallowReschedule,
      photoResPerc: scheduleTask.photoResPerc,
      liveImagesOnly: scheduleTask.liveImagesOnly,
      timeSchedule: scheduleTask.timeSchedule,
      scheduleTypeId: scheduleTask.scheduleTypeId,
      showFollowupIconMulti: scheduleTask.showFollowupIconMulti,
      followupSelectedMulti: scheduleTask.followupSelectedMulti,
      regionId: scheduleTask.regionId,
      ctFormsTotalCnt: scheduleTask.ctFormsTotalCnt,
      ctFormsCompletedCnt: scheduleTask.ctFormsCompletedCnt,
      preftime: scheduleTask.preftime,
      sendTo: scheduleTask.sendTo,
    );
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);

    // Set initial selected button based on tab
    _selectedButton = _tabController.index == 0
        ? 'all'
        : _tabController.index == 1
            ? 'this_week'
            : _tabController.index == 2
                ? 'next_week'
                : 'overdue';

    // Initialize selectedDate but don't select current date
    selectedDate = DateTime
        .now(); // We need to initialize it, but won't mark it as selected in the calendar

    // Initialize the week and month days
    _generateWeekDays();
    _generateMonthDays();

    // Initialize data
    _initializeData();
  }

  void _generateWeekDays() {
    // Get the start of the week (Monday)
    final DateTime now = selectedDate;
    final int day = now.weekday;
    final DateTime firstDayOfWeek = now.subtract(Duration(days: day - 1));

    weekDays = List.generate(7, (index) {
      return firstDayOfWeek.add(Duration(days: index));
    });

    // Check if today is in this week, if so select it
    final today = DateTime.now();
    final bool isTodayInWeek = weekDays.any((day) =>
        day.day == today.day &&
        day.month == today.month &&
        day.year == today.year);

    if (isTodayInWeek) {
      // Select today
      selectedDate = today;
    }
  }

  void _generateMonthDays() {
    // Get the first day of the month
    final DateTime firstDayOfMonth =
        DateTime(_currentMonth.year, _currentMonth.month, 1);

    // Get the weekday of the first day (0 = Monday, 6 = Sunday)
    final int firstWeekday = firstDayOfMonth.weekday - 1;

    // Calculate days from previous month to show
    final DateTime firstDayToShow =
        firstDayOfMonth.subtract(Duration(days: firstWeekday));

    // Calculate total days to show (6 weeks = 42 days)
    monthDays = List.generate(42, (index) {
      return firstDayToShow.add(Duration(days: index));
    });
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        // Update the selected button based on tab index
        if (_tabController.index == 0) {
          _selectedButton = 'all';
          isWeekView = true;

          // When switching to all view, we don't need to filter by date
          // as we'll show all scheduled tasks
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
        } else if (_tabController.index == 1) {
          _selectedButton = 'this_week';
          isWeekView = true;

          // When switching to this week view, use today's date for filtering
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 2) {
          _selectedButton = 'next_week';
          isWeekView = true;

          // When switching to next week view, use next week's date for filtering
          final DateTime nextWeekDate =
              DateTime.now().add(const Duration(days: 7));
          selectedDate = nextWeekDate;
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 3) {
          _selectedButton = 'overdue';
          isWeekView = false;
        }

        // For tabs other than "All", filter tasks for the selected date
        // For "All" tab, we'll show all scheduled tasks in _buildAllTasksListContent
        _updateScheduledTasksForView();
      });
    }
  }

  void _updateScheduledTasksForView() {
    List<schedule.TaskDetail> tasksToDisplay = [];

    final DateTime today =
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);

    if (_selectedButton == 'all') {
      tasksToDisplay = allApiTasks
          .where((task) =>
              task.taskStatus == "Confirmed" &&
              task.isOpen == false &&
              task.scheduledTimeStamp != null)
          .toList();
    } else if (_selectedButton == 'this_week') {
      final weekDates =
          weekDays.map((d) => DateTime(d.year, d.month, d.day)).toSet();
      tasksToDisplay = allApiTasks.where((task) {
        if (task.taskStatus != "Confirmed" ||
            task.isOpen == true ||
            task.scheduledTimeStamp == null) {
          return false;
        }
        final taskDate = DateTime(task.scheduledTimeStamp!.year,
            task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);
        return weekDates.contains(taskDate);
      }).toList();
    } else if (_selectedButton == 'next_week') {
      final weekDates =
          weekDays.map((d) => DateTime(d.year, d.month, d.day)).toSet();
      tasksToDisplay = allApiTasks.where((task) {
        if (task.taskStatus != "Confirmed" ||
            task.isOpen == true ||
            task.scheduledTimeStamp == null) {
          return false;
        }
        final taskDate = DateTime(task.scheduledTimeStamp!.year,
            task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);
        return weekDates.contains(taskDate);
      }).toList();
    } else if (_selectedButton == 'overdue') {
      tasksToDisplay = allApiTasks
          .where((task) =>
              task.taskStatus == "Confirmed" &&
              task.isOpen == false &&
              task.scheduledTimeStamp != null &&
              task.scheduledTimeStamp!.isBefore(today))
          .toList();
    }
    setState(() {
      scheduledTasks =
          tasksToDisplay.map((task) => convertScheduleToDatum(task)).toList();
    });
  }

  Future<void> _initializeData() async {
    _refreshData();
  }

  Future<void> _refreshData() async {
    try {
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();

      if (mounted) {
        context.read<ScheduleTaskCubit>().getData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize: ${e.toString()}',
        );
      }
    }
  }

  // Open route map using StoreTrack web service
  Future<void> _openRouteMap() async {
    try {
      // Get current location
      final locationService = sl<LocationService>();
      final currentLocation = await locationService.getCurrentPosition();

      if (!mounted) return;

      if (currentLocation == null) {
        SnackBarService.warning(
          context: context,
          message:
              'Unable to get current location. Please enable location services.',
        );
        return;
      }

      // Get user ID
      final userID = await sl<DataManager>().getUserId() ?? "0";

      // Construct current location string
      final currentLatLng =
          "${currentLocation.latitude},${currentLocation.longitude}";

      // Construct the StoreTrack route URL
      final routeUrl =
          "https://webservice.storetrack.com.au/standalone/route.aspx?pid=$userID&latlong=$currentLatLng";

      // Navigate to web browser with the route URL
      context.router.push(WebBrowserRoute(url: routeUrl, title: 'Route Map'));
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening route map: ${e.toString()}',
        );
      }
    }
  }

  PreferredSizeWidget _buildViewToggleButtons() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(48),
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Tab bar with All, This Week, Next Week, and Overdue buttons
            Expanded(
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    // All button
                    _buildToggleButton(
                      'All',
                      'all',
                      const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                    ),
                    // This Week button
                    _buildToggleButton(
                      'This weeks',
                      'this_week',
                      BorderRadius.zero,
                    ),
                    // Next Week button
                    _buildToggleButton(
                      'Next weeks',
                      'next_week',
                      BorderRadius.zero,
                    ),
                    // Overdue button
                    _buildToggleButton(
                      'Overdue',
                      'overdue',
                      const BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleButton(
    String text,
    String buttonKey,
    BorderRadius borderRadius,
  ) {
    final bool isSelected = _selectedButton == buttonKey;

    return Expanded(
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedButton = buttonKey;
            if (buttonKey == 'all') {
              _tabController.animateTo(0);
            } else if (buttonKey == 'this_week') {
              _tabController.animateTo(1);
            } else if (buttonKey == 'next_week') {
              _tabController.animateTo(2);
            } else if (buttonKey == 'overdue') {
              _tabController.animateTo(3);
            }
          });
        },
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor:
              isSelected ? AppColors.primaryBlue : const Color(0xFFF5F5F5),
          foregroundColor: isSelected ? Colors.white : AppColors.blackTint1,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
            side: const BorderSide(
              color: Colors.grey,
              width: 0,
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          text,
          style: Theme.of(context)
              .textTheme
              .montserratNavigationPrimaryMedium
              .copyWith(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
        ),
      ),
    );
  }

  // Helper method to check if a task is scheduled for a specific date
  bool _isTaskScheduledForDate(schedule.TaskDetail task, DateTime date) {
    // Check if the task has a scheduled timestamp
    if (task.scheduledTimeStamp == null) return false;

    // Check if the task is confirmed and not open
    if (task.taskStatus != "Confirmed" || task.isOpen == true) return false;

    // Compare only the date part (year, month, day) ignoring time
    final taskDate = task.scheduledTimeStamp!;
    final taskDateOnly = DateTime(taskDate.year, taskDate.month, taskDate.day);
    final selectedDateOnly = DateTime(date.year, date.month, date.day);

    // Check if the dates match
    return taskDateOnly.isAtSameMomentAs(selectedDateOnly);
  }

  // Update the map of dates that have tasks
  void _updateDatesWithTasks(List<schedule.TaskDetail> tasks) {
    datesWithTasks.clear();

    for (var task in tasks) {
      if (task.scheduledTimeStamp != null &&
          task.taskStatus == "Confirmed" &&
          task.isOpen == false) {
        final taskDate = task.scheduledTimeStamp!;
        final dateKey = "${taskDate.year}-${taskDate.month}-${taskDate.day}";

        datesWithTasks[dateKey] = true;
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ScheduleTaskCubit, ScheduleTaskState>(
      listener: (context, state) {
        if (state is ScheduleTaskSuccess) {
          var response = state.response;

          setState(() {
            // Store all tasks from the API response
            allApiTasks = response.addTasks ?? [];

            // Update the map of dates with tasks
            _updateDatesWithTasks(allApiTasks);
          });

          _updateScheduledTasksForView();
        } else if (state is ScheduleTaskError) {
          // Show error message
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.lightGrey2,
          appBar: CustomAppBar(
            title: 'Scheduled',
            bottom: _buildViewToggleButtons(),
            actions: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isCheckboxMode = false;
                  });
                },
                child: Image.asset(
                  AppAssets.appbarCalendar,
                  scale: 4,
                  color: !_isCheckboxMode
                      ? AppColors.primaryBlue
                      : AppColors.black,
                ),
              ),
              const Gap(8),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isCheckboxMode = !_isCheckboxMode;
                    if (!_isCheckboxMode) {
                      // Clear selection when exiting checkbox mode
                      selectedItems.clear();
                      _areAllItemsSelected = false;
                    }
                  });
                },
                child: Image.asset(
                  AppAssets.appbarCalendarEdit,
                  color:
                      _isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
                  scale: 4,
                ),
              ),
              const Gap(8),
              GestureDetector(
                onTap: _openRouteMap,
                child: Image.asset(
                  AppAssets.appbarMap,
                  scale: 4,
                ),
              ),
              const Gap(16)
            ],
          ),
          body: BlocListener<SyncCubit, SyncState>(
            listener: (context, state) {
              if (state is SyncSuccess) {
                _refreshData();
              }
            },
            child: RefreshIndicator(
              onRefresh: _initializeData,
              child: _buildScrollableContent(context),
            ),
          ),
          floatingActionButton: _isCheckboxMode
              ? RescheduleFloatingButtons(
                  isCheckboxMode: _isCheckboxMode,
                  selectedItems: selectedItems,
                  areAllItemsSelected: _areAllItemsSelected,
                  onClose: () {
                    setState(() {
                      _isCheckboxMode = false;
                      selectedItems.clear();
                      _areAllItemsSelected = false;
                    });
                  },
                  onSelectAll: _selectAllItems,
                  onReschedule: () {
                    // Show calendar bottom sheet
                    _showCalendarBottomSheet(context);
                  },
                )
              : null,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
        );
      },
    );
  }

  void _handleParentActionTap(String actionType, TaskDetail task) {
    switch (actionType) {
      case ActionTypes.contactInfo:
        SnackBarService.info(context: context, message: 'Contact info');
        break;
      case ActionTypes.map:
        _openGoogleMaps(task);
        break;
      case ActionTypes.chatAssistant:
        SnackBarService.info(context: context, message: 'Chat with assistant');
        break;
      default:
        SnackBarService.info(context: context, message: 'Unknown action');
    }
  }

  // Open Google Maps with task coordinates
  Future<void> _openGoogleMaps(TaskDetail task) async {
    try {
      // Get coordinates from task
      double? latitude;
      double? longitude;

      // Prefer taskLatitude/taskLongitude, fallback to latitude/longitude
      if (task.taskLatitude != null && task.taskLongitude != null) {
        latitude = task.taskLatitude!.toDouble();
        longitude = task.taskLongitude!.toDouble();
      } else if (task.latitude != null && task.longitude != null) {
        latitude = task.latitude!.toDouble();
        longitude = task.longitude!.toDouble();
      }

      if (latitude == null || longitude == null) {
        if (mounted) {
          SnackBarService.warning(
            context: context,
            message: 'Location coordinates not available for this task.',
          );
        }
        return;
      }

      // Construct Google Maps URL
      final googleMapsUrl =
          'https://www.google.com/maps?q=$latitude,$longitude';

      // Navigate to web browser with Google Maps URL
      context.router
          .push(WebBrowserRoute(url: googleMapsUrl, title: 'Google Maps'));
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening map: ${e.toString()}',
        );
      }
    }
  }

  // Implementation of select all that works directly with the data
  void _selectAllItems() {
    setState(() {
      _areAllItemsSelected = !_areAllItemsSelected;

      if (_areAllItemsSelected) {
        // We're selecting all items - create a fresh list with all scheduled tasks
        selectedItems = List.from(scheduledTasks);
      } else {
        // We're deselecting all - clear the list
        selectedItems.clear();
      }
    });
  }

  void _handleSelectionChanged(
      List<TaskDetail> itemsInList, List<TaskDetail> selectedItemsInList) {
    setState(() {
      // Get a set of task IDs for the items managed by the component that fired the event
      final itemsInListIds = itemsInList.map((t) => t.taskId).toSet();

      // Remove all tasks from the global selection that were managed by this component
      selectedItems.removeWhere((task) => itemsInListIds.contains(task.taskId));

      // Add the newly selected items from the component
      selectedItems.addAll(selectedItemsInList);

      // Update _areAllItemsSelected based on whether all items are selected
      _areAllItemsSelected = selectedItems.length == scheduledTasks.length &&
          scheduledTasks.isNotEmpty;
    });
  }

  // Build the scrollable content that includes task list
  Widget _buildScrollableContent(BuildContext context) {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content section based on selected button
          if (_selectedButton == 'all')
            // For "All" tab, show all scheduled tasks
            AllTasksList(
              allApiTasks: allApiTasks,
              isCheckboxMode: _isCheckboxMode,
              areAllItemsSelected: _areAllItemsSelected,
              // onParentActionTap: _handleParentActionTap,
              // onSubtaskActionTap: _handleSubtaskActionTap,
              onSelectionChanged: _handleSelectionChanged,
              convertScheduleToDatum: convertScheduleToDatum,
              onTaskTap: _handleTaskTap,
            )
          else if (_selectedButton == 'this_week' ||
              _selectedButton == 'next_week')
            // For week tabs, show tasks for the selected week
            WeekTasksList(
              allApiTasks: allApiTasks,
              weekDays: weekDays,
              isCheckboxMode: _isCheckboxMode,
              areAllItemsSelected: _areAllItemsSelected,
              // onParentActionTap: _handleParentActionTap,
              // onSubtaskActionTap: _handleSubtaskActionTap,
              onSelectionChanged: _handleSelectionChanged,
              convertScheduleToDatum: convertScheduleToDatum,
              calculateTotalHours: _calculateTotalHours,
              onTaskTap: _handleTaskTap,
            )
          else if (_selectedButton == 'overdue')
            // For overdue tab, show overdue tasks
            OverdueTasksList(
              allApiTasks: allApiTasks,
              isCheckboxMode: _isCheckboxMode,
              areAllItemsSelected: _areAllItemsSelected,
              // onParentActionTap: _handleParentActionTap,
              // onSubtaskActionTap: _handleSubtaskActionTap,
              onSelectionChanged: _handleSelectionChanged,
              convertScheduleToDatum: convertScheduleToDatum,
              onTaskTap: _handleTaskTap,
            )
          else
            // Fallback for any other state
            Container(
              color: AppColors.lightGrey2,
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'Select a view option',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Calculate total hours for a list of tasks
  String _calculateTotalHours(List<TaskDetail> tasks) {
    logger('tasks: ${tasks.length}');
    if (tasks.isEmpty) {
      return "0 hrs";
    }

    double totalMinutes = 0;
    for (var task in tasks) {
      if (task.minutes != null) {
        // Ensure we're properly converting the minutes value to a double
        // Handle different numeric types (int, double, num)
        try {
          logger(
              'Task minutes: ${task.budget}, type: ${task.budget.runtimeType}');
          totalMinutes += task.budget!.toDouble();
        } catch (e) {
          logger("Error converting minutes to double: ${e.toString()}");
          // If conversion fails, try parsing as string
          try {
            totalMinutes += double.parse(task.budget.toString());
          } catch (e) {
            logger("Error parsing minutes as string: ${e.toString()}");
          }
        }
      }
    }

    // Convert minutes to hours with proper decimal representation
    double hours = totalMinutes / 60;

    // Format hours to show proper fractions (e.g., 0.75 instead of 0.8 for 45 minutes)
    // For 45 minutes, we want to show 0.75 hrs instead of 0.8 hrs
    String formattedHours;
    if (totalMinutes % 60 == 45) {
      formattedHours =
          "${(totalMinutes ~/ 60)}.75"; // Use exact fraction for 45 minutes
    } else if (totalMinutes % 60 == 30) {
      formattedHours =
          "${(totalMinutes ~/ 60)}.5"; // Use exact fraction for 30 minutes
    } else if (totalMinutes % 60 == 15) {
      formattedHours =
          "${(totalMinutes ~/ 60)}.25"; // Use exact fraction for 15 minutes
    } else {
      formattedHours = hours.toStringAsFixed(2);
      // Remove trailing zeros
      if (formattedHours.endsWith('0')) {
        formattedHours = formattedHours.substring(0, formattedHours.length - 1);
      }
    }

    logger(
        'Total minutes: $totalMinutes, hours: $hours, formatted: $formattedHours');
    return "$formattedHours hrs";
  }

  // Add calendar bottom sheet functionality
  void _showCalendarBottomSheet(BuildContext context) {
    // Create a local reference to the cubit to avoid context usage in async gap
    final scheduleTaskCubit = context.read<ScheduleTaskCubit>();

    // Create a list of dates that have tasks
    List<DateTime> taskDates = [];
    for (var task in allApiTasks) {
      if (task.scheduledTimeStamp != null &&
          task.taskStatus == "Confirmed" &&
          task.isOpen != true) {
        taskDates.add(task.scheduledTimeStamp!);
      }
    }

    // Show calendar bottom sheet
    CalendarBottomSheet.show(
      context: context,
      calendarResponse: scheduleTaskCubit
          .calendarResponse, // Pass the calendar response from the cubit
      taskDates: taskDates, // Pass the task dates to show grey circles
    ).then((selectedDate) {
      // Check if the widget is still mounted and a date was selected
      if (selectedDate != null && mounted && selectedItems.isNotEmpty) {
        // Show rescheduling message immediately
        SnackBarService.info(
          context: context,
          message:
              'Rescheduling ${selectedItems.length} tasks for ${DateFormat('MMM dd, yyyy').format(selectedDate)}',
        );

        // Perform async operations
        _performRescheduling(selectedDate);
      }
    });
  }

  /// Performs the actual rescheduling operations asynchronously
  Future<void> _performRescheduling(DateTime selectedDate) async {
    try {
      // submission timestamp is current time in timezone "Australia/NSW", locale "en", "AU"
      final submissionTimeStamp = DateTime.now();

      // Update scheduling data for each selected task in the database
      for (var task in selectedItems) {
        await TaskUtils.updateTaskSchedulingData(
          taskId: task.taskId.toString(),
          scheduledTimeStamp: selectedDate,
          submissionTimeStamp: submissionTimeStamp,
          taskStatus: "Confirmed",
          submissionState: 1,
        );
      }

      // Sync the changes to the server
      await SyncService().sync();

      // Clear selection after rescheduling
      if (mounted) {
        setState(() {
          _isCheckboxMode = false;
          selectedItems.clear();
          _areAllItemsSelected = false;
        });
      }
    } catch (e) {
      logger('Error during rescheduling: $e');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to reschedule tasks. Please try again.',
        );
      }
    }
  }

  /// Handle task tap with time validation
  Future<void> _handleTaskTap(TaskDetail task) async {
    if ((await TimeAccessValidator.canAccessTask(context, task,
            skipDialog: task.reOpened == true)) ||
        task.reOpened == true) {
      if (mounted) {
        context.router.push(TaskDetailsRoute(
          storeId: task.storeId ?? 0,
          taskId: task.taskId!.toInt(),
        ));
      }
    }
  }
}
