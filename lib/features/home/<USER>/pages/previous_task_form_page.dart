import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class PreviousTaskFormPage extends StatelessWidget {
  final entities.Form form;

  const PreviousTaskFormPage({
    super.key,
    required this.form,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Previous Task Form',
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    // Process form data to organize by headers
    final organizedData = _organizeFormData();

    if (organizedData.isEmpty) {
      return const Center(
        child: Text(
          'No form data available',
          style: AppTypography.montserratParagraphSmall,
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: organizedData.length,
      itemBuilder: (context, index) {
        final headerData = organizedData[index];
        return _buildHeaderSection(headerData);
      },
    );
  }

  Widget _buildHeaderSection(HeaderData headerData) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: AppColors.blackTint2,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Question',
                    style: AppTypography.montserratTitleExtraSmall.copyWith(
                      color: AppColors.black,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  'Response',
                  style: AppTypography.montserratTitleExtraSmall.copyWith(
                    color: AppColors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Header title (section header like "CONGO BRANDS Range")
          if (headerData.headerTitle.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: AppColors.blackTint1,
              child: Text(
                headerData.headerTitle.toUpperCase(),
                style: AppTypography.montserratTitleExtraSmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                ),
              ),
            ),

          // Question parts
          ...headerData.questionParts.map((questionPartData) =>
              _buildQuestionPartSection(questionPartData)),
        ],
      ),
    );
  }

  Widget _buildQuestionPartSection(QuestionPartData questionPartData) {
    return Column(
      children: [
        // Question part description (product name)
        if (questionPartData.description.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: AppColors.lightGrey1,
            child: Text(
              questionPartData.description,
              style: AppTypography.montserratTitleExtraSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
          ),

        // Measurements (individual measurement rows)
        ...questionPartData.measurements
            .map((measurementData) => _buildMeasurementRow(measurementData)),
      ],
    );
  }

  Widget _buildMeasurementRow(MeasurementData measurementData) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              measurementData.description,
              style: AppTypography.montserratParagraphSmall.copyWith(
                color: AppColors.black,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Text(
            measurementData.response,
            style: AppTypography.montserratParagraphSmall.copyWith(
              color: AppColors.blackTint1,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  List<HeaderData> _organizeFormData() {
    final List<HeaderData> organizedData = [];

    if (form.questions == null || form.questions!.isEmpty) {
      return organizedData;
    }

    // Process each question as a section
    for (final question in form.questions!) {
      final headerData = HeaderData(
        headerTitle: question.questionDescription ?? 'Unknown Section',
        questionParts: [],
      );

      // Process question parts (these are the individual items)
      if (question.questionParts != null &&
          question.questionParts!.isNotEmpty) {
        for (final questionPart in question.questionParts!) {
          final questionPartData = QuestionPartData(
            description: questionPart.questionpartDescription ?? 'Unknown Item',
            measurements: [],
          );

          // Process measurements for this question part
          if (question.measurements != null) {
            for (final measurement in question.measurements!) {
              // Find corresponding answer for this specific combination
              final answer = _findAnswerForMeasurement(
                measurement.measurementId,
                questionPart.questionpartId,
              );

              questionPartData.measurements.add(MeasurementData(
                description:
                    measurement.measurementDescription ?? 'Unknown Measurement',
                response: answer ?? 'Not Answered',
              ));
            }
          }

          headerData.questionParts.add(questionPartData);
        }
      } else {
        // If no question parts, create measurements directly under the question
        if (question.measurements != null) {
          final questionPartData = QuestionPartData(
            description: '', // Empty description for direct measurements
            measurements: [],
          );

          for (final measurement in question.measurements!) {
            final answer =
                _findAnswerForMeasurement(measurement.measurementId, null);

            questionPartData.measurements.add(MeasurementData(
              description:
                  measurement.measurementDescription ?? 'Unknown Measurement',
              response: answer ?? 'Not Answered',
            ));
          }

          headerData.questionParts.add(questionPartData);
        }
      }

      if (headerData.questionParts.isNotEmpty) {
        organizedData.add(headerData);
      }
    }

    return organizedData;
  }

  String? _findAnswerForMeasurement(num? measurementId, num? questionPartId) {
    if (measurementId == null || form.questionAnswers == null) {
      return null;
    }

    for (final answer in form.questionAnswers!) {
      if (answer.measurementId == measurementId) {
        // If questionPartId is provided, match it as well for more precise matching
        if (questionPartId != null && answer.questionpartId != questionPartId) {
          continue;
        }
        return answer.measurementTextResult ?? 'Not Answered';
      }
    }

    return null;
  }
}

// Data classes for organizing form information
class HeaderData {
  final String headerTitle;
  final List<QuestionPartData> questionParts;

  HeaderData({
    required this.headerTitle,
    required this.questionParts,
  });
}

class QuestionPartData {
  final String description;
  final List<MeasurementData> measurements;

  QuestionPartData({
    required this.description,
    required this.measurements,
  });
}

class MeasurementData {
  final String description;
  final String response;

  MeasurementData({
    required this.description,
    required this.response,
  });
}
