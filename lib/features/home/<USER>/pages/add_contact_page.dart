import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_contact_model.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_contacts/store_contacts_cubit.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/di/service_locator.dart';

@RoutePage()
class AddContactPage extends StatefulWidget {
  final StoreContactData? contact;
  final bool isEditMode;
  final String storeId;
  const AddContactPage({
    super.key,
    this.contact,
    this.isEditMode = false,
    required this.storeId,
  });

  @override
  State<AddContactPage> createState() => _AddContactPageState();
}

class _AddContactPageState extends State<AddContactPage> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _jobTitleController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  String? _selectedContactTypeId;
  List<ContactType> _contactTypes = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _populateFields();
  }

  void _populateFields() {
    if (widget.contact != null) {
      _firstNameController.text = widget.contact!.firstName;
      _lastNameController.text = widget.contact!.lastName;
      _jobTitleController.text = widget.contact!.jobTitle;
      _emailController.text = widget.contact!.email;
      _phoneController.text = widget.contact!.phone;
      _selectedContactTypeId = widget.contact!.contactTypeId;
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _jobTitleController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _saveContact() async {
    print('🟡 _saveContact: Starting save operation');

    if (!_formKey.currentState!.validate()) {
      print('🔴 _saveContact: Form validation failed');
      return;
    }

    print('🟢 _saveContact: Form validation passed');
    setState(() {
      _isLoading = true;
    });

    try {
      // Find the contact type name for the selected ID
      String? contactTypeName;
      if (_selectedContactTypeId != null) {
        final selectedType = _contactTypes.firstWhere(
          (type) => type.id == _selectedContactTypeId,
          orElse: () => ContactType(id: '', name: ''),
        );
        contactTypeName =
            selectedType.name.isNotEmpty ? selectedType.name : null;
      }

      final contact = StoreContactData(
        employeeId: widget.contact?.employeeId,
        contactTypeId: _selectedContactTypeId,
        contactTypeName: contactTypeName,
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        jobTitle: _jobTitleController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
      );

      print('🟢 _saveContact: Contact object created');
      print('Contact data: ${contact.toJson()}');

      // Create a separate cubit instance for saving to avoid state conflicts
      final saveCubit = sl<StoreContactsCubit>();
      print('🟢 _saveContact: Cubit instance created');

      // Call the API to save the contact using separate cubit instance
      print('🟡 _saveContact: About to call saveStoreContact API');
      await saveCubit.saveStoreContact(
        storeId: widget.storeId,
        contact: contact,
      );
      print('🟢 _saveContact: API call completed successfully');

      // Handle success
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Contact saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.router.maybePop(true);
      }
    } catch (e) {
      print('🔴 _saveContact: Error occurred: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving contact: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteContact() async {
    if (!widget.isEditMode || widget.contact?.employeeId == null) return;

    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Contact'),
        content: const Text('Are you sure you want to delete this contact?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (shouldDelete == true && mounted) {
      context.router.maybePop('deleted');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => sl<StoreContactsCubit>()
            ..fetchStoreContactTypes(storeId: widget.storeId),
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<StoreContactsCubit, StoreContactsState>(
            listenWhen: (previous, current) =>
                current is StoreContactTypesLoaded ||
                current is StoreContactTypesError,
            listener: (context, state) {
              if (state is StoreContactTypesLoaded) {
                setState(() {
                  _contactTypes = state.response.data;
                  // If editing and have a contact type ID, make sure it's still selected
                  if (widget.contact?.contactTypeId != null) {
                    _selectedContactTypeId = widget.contact!.contactTypeId;
                  }
                });
              } else if (state is StoreContactTypesError) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          'Failed to load contact types: ${state.message}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
        ],
        child: BlocBuilder<StoreContactsCubit, StoreContactsState>(
          builder: (context, state) {
            // Debug: Print the current state and contact types
            print('AddContactPage State: $state');
            print('Contact Types Count: ${_contactTypes.length}');

            return Scaffold(
              backgroundColor: Colors.white,
              appBar: CustomAppBar(
                title: widget.isEditMode ? 'Edit Contact' : 'Add Contact',
                actions: [
                  IconButton(
                    onPressed: _isLoading ? null : _saveContact,
                    icon: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: AppColors.primaryBlue,
                            ),
                          )
                        : const Icon(
                            Icons.check,
                            color: AppColors.primaryBlue,
                          ),
                  ),
                ],
              ),
              body: Column(
                children: [
                  // Show loading indicator while fetching contact types
                  if (state is StoreContactTypesLoading)
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: const Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text('Loading contact types...'),
                        ],
                      ),
                    ),

                  // Show error if contact types failed to load
                  if (state is StoreContactTypesError)
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Icon(Icons.error, color: Colors.red, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Failed to load contact types: ${state.message}',
                              style: const TextStyle(
                                  color: Colors.red, fontSize: 12),
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              context
                                  .read<StoreContactsCubit>()
                                  .fetchStoreContactTypes(
                                      storeId: widget.storeId);
                            },
                            child: const Text('Retry',
                                style: TextStyle(fontSize: 12)),
                          ),
                        ],
                      ),
                    ),

                  // Main form content
                  Expanded(
                    child: Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Contact Type Spinner
                            _buildContactTypeDropdown(state),

                            // Separator Line
                            Container(
                              height: 1,
                              color: AppColors.lightGrey2,
                              margin: const EdgeInsets.symmetric(vertical: 8),
                            ),

                            // First Name Field
                            _buildFormField(
                              label: 'First Name',
                              controller: _firstNameController,
                              hint: 'Enter first name',
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Please enter first name';
                                }
                                return null;
                              },
                            ),

                            // Last Name Field
                            _buildFormField(
                              label: 'Last Name',
                              controller: _lastNameController,
                              hint: 'Enter last name',
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Please enter last name';
                                }
                                return null;
                              },
                            ),

                            // Job Title Field
                            _buildFormField(
                              label: 'Job Title',
                              controller: _jobTitleController,
                              hint: 'Enter job title',
                            ),

                            // Email Field
                            _buildFormField(
                              label: 'Email',
                              controller: _emailController,
                              hint: 'Enter email',
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  if (!RegExp(
                                          r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                      .hasMatch(value)) {
                                    return 'Please enter a valid email';
                                  }
                                }
                                return null;
                              },
                            ),

                            // Phone Field
                            _buildFormField(
                              label: 'Phone',
                              controller: _phoneController,
                              hint: 'Enter phone',
                              keyboardType: TextInputType.phone,
                            ),

                            const SizedBox(height: 24),

                            // Delete Contact Button (only in edit mode)
                            if (widget.isEditMode) ...[
                              AppButton(
                                text: 'Delete Contact',
                                color: AppColors.primaryBlue,
                                onPressed: _deleteContact,
                                height: 48,
                                textColor: Colors.white,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildContactTypeDropdown(StoreContactsState state) {
    // Determine which contact types to show
    List<ContactType> displayTypes = [];
    String hintText = 'Select Contact Type';
    bool isDropdownEnabled = true;

    // Handle different states
    if (state is StoreContactTypesLoading) {
      hintText = 'Loading contact types...';
      isDropdownEnabled = false;
      displayTypes = []; // Empty while loading
    } else if (state is StoreContactTypesLoaded) {
      displayTypes = state.response.data;
      hintText = 'Select Contact Type (${displayTypes.length} available)';
      isDropdownEnabled = true;
    } else if (state is StoreContactTypesError) {
      hintText = 'Error loading contact types';
      isDropdownEnabled = false;
      displayTypes = []; // Empty on error
    } else {
      // Use current contact types if available
      displayTypes = _contactTypes;
      isDropdownEnabled = displayTypes.isNotEmpty;
      if (displayTypes.isEmpty) {
        hintText = 'No contact types available';
      }
    }

    // Debug logging
    print('Dropdown - Display Types Count: ${displayTypes.length}');
    print('Dropdown - State: $state');
    print('Dropdown - Enabled: $isDropdownEnabled');
    for (var type in displayTypes) {
      print('Type: ${type.id} - ${type.name}');
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contact Type',
            style: Theme.of(context)
                .textTheme
                .montserratNavigationPrimaryMedium
                .copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
          ),
          const SizedBox(height: 6),
          DropdownButtonFormField<String>(
            value: _selectedContactTypeId,
            decoration: InputDecoration(
              hintText: hintText,
              filled: true,
              fillColor: AppColors.lightGrey1,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 14,
              ),
              hintStyle: Theme.of(context)
                  .textTheme
                  .montserratNavigationPrimaryMedium
                  .copyWith(
                    color: AppColors.blackTint1,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.borderColor,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.borderColor,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.primaryBlue,
                  width: 1.5,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 1.5,
                ),
              ),
            ),
            hint: Text(
              hintText,
              style: Theme.of(context)
                  .textTheme
                  .montserratNavigationPrimaryMedium
                  .copyWith(
                    color: AppColors.blackTint1,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
            ),
            style: Theme.of(context).textTheme.montserratRegular.copyWith(
                  fontSize: 16,
                  color: AppColors.black,
                ),
            items: isDropdownEnabled && displayTypes.isNotEmpty
                ? displayTypes.map((contactType) {
                    return DropdownMenuItem<String>(
                      value: contactType.id,
                      child: Text(
                        contactType.name,
                        style: Theme.of(context)
                            .textTheme
                            .montserratRegular
                            .copyWith(
                              fontSize: 16,
                              color: AppColors.black,
                            ),
                      ),
                    );
                  }).toList()
                : null,
            onChanged: isDropdownEnabled
                ? (value) {
                    setState(() {
                      _selectedContactTypeId = value;
                    });
                    print('Selected contact type: $value');
                  }
                : null,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select a contact type';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hint,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context)
                .textTheme
                .montserratNavigationPrimaryMedium
                .copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
          ),
          const SizedBox(height: 6),
          TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            textCapitalization: TextCapitalization.sentences,
            decoration: InputDecoration(
              hintText: hint,
              filled: true,
              fillColor: AppColors.lightGrey1,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 14,
              ),
              hintStyle: Theme.of(context)
                  .textTheme
                  .montserratNavigationPrimaryMedium
                  .copyWith(
                    color: AppColors.blackTint1,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.borderColor,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.borderColor,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.primaryBlue,
                  width: 1.5,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 1.5,
                ),
              ),
            ),
            style: Theme.of(context).textTheme.montserratRegular.copyWith(
                  fontSize: 16,
                  color: AppColors.black,
                ),
            validator: validator,
          ),
        ],
      ),
    );
  }
}
