import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/shared/widgets/tutorial_dialog.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';

import '../../../../config/routes/app_router.gr.dart';
import '../../../../config/themes/app_colors.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/utils/logger.dart';

late TabsRouter tabsRouter;

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool _hasCheckedTutorial = false;

  @override
  void initState() {
    super.initState();
    // Check tutorial setting after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowTutorial();
    });
  }

  Future<void> _checkAndShowTutorial() async {
    if (_hasCheckedTutorial) return;

    try {
      final dataManager = sl<DataManager>();
      final tutorialEnabled = await dataManager.getTutorialEnabled();

      if (tutorialEnabled && mounted) {
        _hasCheckedTutorial = true;
        await TutorialDialog.show(context);
      }
    } catch (e) {
      // Handle error silently
    }
  }

  // Check if the current route is the DashboardRoute
  bool _isDashboardRoute() {
    // First check if we're on the first tab (Dashboard tab)
    return context.router.currentPath == '/home-route';
  }

  @override
  Widget build(BuildContext context) {
    return AutoTabsRouter(
      routes: const [
        DashboardHolderRoute(),
        // AssistantRoute(),
        ProfileHolderRoute(),
        MoreHolderRoute()
      ],
      builder: (context, child) {
        logger('Current route: ${context.router.currentPath}');
        logger(
            'Current route name: ${(ModalRoute.of(context)?.settings.name)}');
        final textTheme = Theme.of(context).textTheme;
        tabsRouter = AutoTabsRouter.of(context);
        return Scaffold(
          body: child,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            toolbarHeight: 0,
            forceMaterialTransparency: true,
            systemOverlayStyle: SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness:
                  _isDashboardRoute() ? Brightness.light : Brightness.dark,
              systemNavigationBarColor: Colors.transparent,
              systemNavigationBarIconBrightness: Brightness.light,
              statusBarBrightness:
                  _isDashboardRoute() ? Brightness.dark : Brightness.light,
            ),
          ),
          extendBodyBehindAppBar: true,
          bottomNavigationBar: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: BottomNavigationBar(
                type: BottomNavigationBarType.fixed,
                backgroundColor: Colors.white,
                currentIndex: tabsRouter.activeIndex,
                onTap: tabsRouter.setActiveIndex,
                selectedFontSize: 12,
                unselectedItemColor: AppColors.black.withOpacity(.4),
                selectedItemColor: AppColors.primaryBlue,
                selectedLabelStyle: textTheme.montserratBottomNavigation,
                unselectedLabelStyle: textTheme.montserratBottomNavigation
                    .copyWith(color: AppColors.black.withOpacity(.6)),
                items: [
                  BottomNavigationBarItem(
                    icon: Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: tabsRouter.activeIndex == 0
                          ? Image.asset(
                              color: AppColors.primaryBlue,
                              AppAssets.homeDashboard,
                              width: 18,
                            )
                          : Image.asset(
                              color: AppColors.black.withOpacity(.6),
                              AppAssets.homeDashboard,
                              width: 18,
                            ),
                    ),
                    label: 'Dashboard',
                  ),
                  // BottomNavigationBarItem(
                  //   icon: Padding(
                  //     padding: const EdgeInsets.only(bottom: 4),
                  //     child: tabsRouter.activeIndex == 1
                  //         ? Image.asset(
                  //             color: AppColors.primaryBlue,
                  //             AppAssets.homeAssistant,
                  //             width: 18,
                  //           )
                  //         : Image.asset(
                  //             color: AppColors.black.withOpacity(.6),
                  //             AppAssets.homeAssistant,
                  //             width: 18,
                  //           ),
                  //   ),
                  //   label: 'Assistant',
                  // ),
                  BottomNavigationBarItem(
                    icon: Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: tabsRouter.activeIndex == 1
                          ? Image.asset(
                              color: AppColors.primaryBlue,
                              AppAssets.homeProfile,
                              width: 18,
                            )
                          : Image.asset(
                              color: AppColors.black.withOpacity(.6),
                              AppAssets.homeProfile,
                              width: 18,
                            ),
                    ),
                    label: 'Profile',
                  ),
                  BottomNavigationBarItem(
                    icon: Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: tabsRouter.activeIndex == 2
                          ? Image.asset(
                              color: AppColors.primaryBlue,
                              AppAssets.homeMore,
                              width: 18,
                            )
                          : Image.asset(
                              color: AppColors.black.withOpacity(.6),
                              AppAssets.homeMore,
                              width: 18,
                            ),
                    ),
                    label: 'More',
                  ),
                ]
                // BottomNavigationBarItem(
                //   icon: Icon(Icons.more_horiz),
                //   label: 'More',
                // ),
                // ],
                ),
          ),
        );
      },
    );
  }
}
