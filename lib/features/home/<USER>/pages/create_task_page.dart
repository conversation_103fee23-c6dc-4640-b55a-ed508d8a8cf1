import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/create_task_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/create_task/create_task_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/create_task/create_task_state.dart';
import 'package:storetrack_app/features/home/<USER>/models/coverage_store_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/coverage_client_model.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class CreateTaskPage extends StatefulWidget {
  const CreateTaskPage({super.key});

  @override
  State<CreateTaskPage> createState() => _CreateTaskPageState();
}

class _CreateTaskPageState extends State<CreateTaskPage> {
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _durationController = TextEditingController();
  final TextEditingController _storesController = TextEditingController();
  final TextEditingController _clientController = TextEditingController();

  DateTime? _selectedDate;
  int? _selectedStoreId;
  int? _selectedClientId;

  // Real data from API
  List<CoverageStore> _availableStores = [];
  List<CoverageClient> _availableClients = [];
  bool _isLoadingStores = true;
  bool _isLoadingClients = true;

  // Store the Cubit instance to ensure consistency
  late CreateTaskCubit _createTaskCubit;

  @override
  void initState() {
    super.initState();
    _createTaskCubit = sl<CreateTaskCubit>();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    try {
      print('🚀 Starting _loadInitialData...');
      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId() ?? "";
      final token = await dataManager.getAuthToken() ?? "";

      print('👤 User ID: $userId, Token length: ${token.length}');
      if (mounted && userId.isNotEmpty && token.isNotEmpty) {
        // Fetch stores and clients using the same Cubit instance
        print('📦 Calling fetchCoverageStores...');
        _createTaskCubit.fetchCoverageStores(
          userId: userId,
          token: token,
        );

        // Give a small delay before fetching clients
        await Future.delayed(const Duration(milliseconds: 100));

        if (mounted) {
          print('👥 Calling fetchCoverageClients...');
          _createTaskCubit.fetchCoverageClients(
            userId: userId,
            token: token,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error loading initial data: ${e.toString()}',
        );
      }
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    _durationController.dispose();
    _storesController.dispose();
    _clientController.dispose();
    _createTaskCubit.close();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primaryBlue,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  Future<void> _selectStores() async {
    // Check if stores are still loading
    if (_isLoadingStores) {
      SnackBarService.warning(
        context: context,
        message: 'Please wait, stores are still loading...',
      );
      return;
    }

    // Check if no stores available
    if (_availableStores.isEmpty) {
      SnackBarService.warning(
        context: context,
        message:
            'No stores available. Please check your internet connection and reload.',
      );
      return;
    }

    // Use bottom sheet for store selection
    final int? selected = await _showStoreBottomSheet();

    if (selected != null) {
      setState(() {
        _selectedStoreId = selected;
        _storesController.text = _availableStores
            .firstWhere((store) => store.storeId == selected)
            .displayName;
      });
    }
  }

  Future<void> _selectClient() async {
    // Check if clients are still loading
    if (_isLoadingClients) {
      SnackBarService.warning(
        context: context,
        message: 'Please wait, clients are still loading...',
      );
      return;
    }

    // Check if no clients available
    if (_availableClients.isEmpty) {
      SnackBarService.warning(
        context: context,
        message:
            'No clients available. Please check your internet connection and reload.',
      );
      return;
    }

    // Use bottom sheet for client selection
    final int? selected = await _showClientBottomSheet();

    if (selected != null) {
      setState(() {
        _selectedClientId = selected;
        _clientController.text = _availableClients
            .firstWhere((client) => client.clientId == selected)
            .displayName;
      });
    }
  }

  Future<void> _refreshStoresData() async {
    try {
      setState(() {
        _isLoadingStores = true;
      });

      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId() ?? "";
      final token = await dataManager.getAuthToken() ?? "";

      if (mounted && userId.isNotEmpty && token.isNotEmpty) {
        _createTaskCubit.fetchCoverageStores(
          userId: userId,
          token: token,
        );
      }
    } catch (e) {
      setState(() {
        _isLoadingStores = false;
      });
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error refreshing stores: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _refreshClientsData() async {
    try {
      setState(() {
        _isLoadingClients = true;
      });

      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId() ?? "";
      final token = await dataManager.getAuthToken() ?? "";

      if (mounted && userId.isNotEmpty && token.isNotEmpty) {
        _createTaskCubit.fetchCoverageClients(
          userId: userId,
          token: token,
        );
      }
    } catch (e) {
      setState(() {
        _isLoadingClients = false;
      });
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error refreshing clients: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _createTaskCubit,
      child: Scaffold(
        backgroundColor: AppColors.lightGrey1,
        appBar: const CustomAppBar(
          title: 'Create Tasks',
        ),
        body: BlocConsumer<CreateTaskCubit, CreateTaskState>(
          listener: (context, state) {
            print('🔄 BlocListener received state: ${state.runtimeType}');
            if (state is CreateTaskSuccess) {
              SnackBarService.success(
                context: context,
                message:
                    'Tasks created successfully! ${state.response.totalTasksCreated} tasks created.',
              );

              // Trigger sync after successful task creation
              _triggerSyncAfterTaskCreation();

              context.router.maybePop();
            } else if (state is CreateTaskError) {
              // Reset loading states on error
              setState(() {
                _isLoadingStores = false;
                _isLoadingClients = false;
              });
              SnackBarService.error(
                context: context,
                message: state.message,
              );
            } else if (state is CoverageStoresLoaded) {
              print(
                  '📦 Processing CoverageStoresLoaded with ${state.response.data?.coverageStores.length ?? 0} stores');
              setState(() {
                _availableStores = state.response.data?.coverageStores ?? [];
                _isLoadingStores = false;
              });
            } else if (state is CoverageClientsLoaded) {
              print(
                  '👥 Processing CoverageClientsLoaded with ${state.response.data?.coverageClients.length ?? 0} clients');
              setState(() {
                _availableClients = state.response.data?.coverageClients ?? [];
                _isLoadingClients = false;
              });
            }
          },
          builder: (context, state) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Form fields
                  Column(
                    children: [
                      // Date picker
                      _buildInputField(
                        controller: _dateController,
                        label: 'Task Date',
                        hint: 'Select task date',
                        readOnly: true,
                        onTap: _selectDate,
                        suffixIcon: const Icon(
                          Icons.calendar_today_outlined,
                          color: AppColors.primaryBlue,
                          size: 20,
                        ),
                      ),
                      const Gap(24),
                      // Task duration
                      _buildInputField(
                        controller: _durationController,
                        label: 'Duration (minutes)',
                        hint: 'Enter task duration',
                        keyboardType: TextInputType.number,
                        suffixIcon: const Icon(
                          Icons.access_time_outlined,
                          color: AppColors.primaryBlue,
                          size: 20,
                        ),
                      ),
                      const Gap(24),
                      // Store selection
                      _buildInputField(
                        controller: _storesController,
                        label: 'Stores',
                        hint: _isLoadingStores
                            ? 'Loading stores...'
                            : _availableStores.isEmpty
                                ? 'No stores available'
                                : 'Select stores',
                        readOnly: true,
                        maxLines: 1,
                        onTap: _selectStores,
                        suffixIcon: _isLoadingStores
                            ? const Padding(
                                padding: EdgeInsets.all(16),
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.primaryBlue,
                                  ),
                                ),
                              )
                            : const Icon(
                                Icons.store_outlined,
                                color: AppColors.primaryBlue,
                                size: 20,
                              ),
                      ),
                      const Gap(24),
                      // Client selection
                      _buildInputField(
                        controller: _clientController,
                        label: 'Client',
                        hint: _isLoadingClients
                            ? 'Loading clients...'
                            : _availableClients.isEmpty
                                ? 'No clients available'
                                : 'Select client',
                        readOnly: true,
                        maxLines: 1,
                        onTap: _selectClient,
                        suffixIcon: _isLoadingClients
                            ? const Padding(
                                padding: EdgeInsets.all(16),
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.primaryBlue,
                                  ),
                                ),
                              )
                            : const Icon(
                                Icons.business_outlined,
                                color: AppColors.primaryBlue,
                                size: 20,
                              ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
        floatingActionButton: BlocBuilder<CreateTaskCubit, CreateTaskState>(
          builder: (context, state) {
            return Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 24),
              child: AppButton(
                text: state is CreateTaskLoading
                    ? 'Creating Tasks...'
                    : 'Create Tasks',
                onPressed: state is CreateTaskLoading ? () {} : _submitTasks,
                color: state is CreateTaskLoading
                    ? AppColors.midGrey
                    : AppColors.primaryBlue,
                height: 56,
              ),
            );
          },
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String hint,
    String? label,
    bool readOnly = false,
    int maxLines = 1,
    TextInputType? keyboardType,
    VoidCallback? onTap,
    Widget? suffixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Text(
            label,
            style: AppTypography.montserratTitleExtraSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          const Gap(8),
        ],
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.black10,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            readOnly: readOnly,
            maxLines: maxLines,
            keyboardType: keyboardType,
            onTap: onTap,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: AppTypography.montserratParagraphSmall.copyWith(
                color: AppColors.secondaryTextColor,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: AppColors.primaryBlue,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              filled: true,
              fillColor: Colors.white,
              suffixIcon: suffixIcon,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _submitTasks() async {
    if (_selectedDate == null) {
      SnackBarService.warning(
        context: context,
        message: 'Please select a date',
      );
      return;
    }

    if (_durationController.text.isEmpty) {
      SnackBarService.warning(
        context: context,
        message: 'Please enter task duration',
      );
      return;
    }

    if (_selectedStoreId == null) {
      SnackBarService.warning(
        context: context,
        message: 'Please select a store',
      );
      return;
    }

    if (_selectedClientId == null) {
      SnackBarService.warning(
        context: context,
        message: 'Please select a client',
      );
      return;
    }

    try {
      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId() ?? "";
      final token = await dataManager.getAuthToken() ?? "";

      final request = CreateTaskRequestEntity(
        taskDuration: int.parse(_durationController.text),
        userId: userId,
        token: token,
        scheduledDate: DateFormat('yyyy-MM-dd').format(_selectedDate!),
        coverageStores: _selectedStoreId != null
            ? [
                _availableStores
                    .firstWhere((store) => store.storeId == _selectedStoreId)
              ]
            : [],
        clientId: _selectedClientId!,
      );

      if (mounted) {
        _createTaskCubit.createTasks(request);
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error creating tasks: ${e.toString()}',
        );
      }
    }
  }

  Future<int?> _showStoreBottomSheet() async {
    return await showModalBottomSheet<int>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableStores.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select Stores',
                  style: AppTypography.montserratTitleExtraSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Stores list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableStores.length,
                  itemBuilder: (context, index) {
                    final store = _availableStores[index];
                    final isSelected = _selectedStoreId == store.storeId;

                    return InkWell(
                      onTap: () {
                        Navigator.pop(context, store.storeId);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                store.displayName,
                                style: AppTypography.montserratParagraphSmall
                                    .copyWith(
                                  color: AppColors.black,
                                ),
                              ),
                            ),
                            if (isSelected)
                              const Icon(
                                Icons.radio_button_checked,
                                color: AppColors.primaryBlue,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _triggerSyncAfterTaskCreation() async {
    try {
      await SyncService().sync();
    } catch (e) {
      // Log sync error but don't block user flow
      print('Sync failed after task creation: $e');
    }
  }

  Future<int?> _showClientBottomSheet() async {
    return await showModalBottomSheet<int>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) {
        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            (_availableClients.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Container(
          height: finalHeight,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12),
                decoration: BoxDecoration(
                  color: AppColors.blackTint2,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Select Client',
                  style: AppTypography.montserratTitleExtraSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Clients list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: _availableClients.length,
                  itemBuilder: (context, index) {
                    final client = _availableClients[index];
                    final isSelected = _selectedClientId == client.clientId;

                    return InkWell(
                      onTap: () {
                        Navigator.pop(context, client.clientId);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12.0),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: AppColors.blackTint2,
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                client.displayName,
                                style: AppTypography.montserratParagraphSmall
                                    .copyWith(
                                  color: AppColors.black,
                                ),
                              ),
                            ),
                            if (isSelected)
                              const Icon(
                                Icons.radio_button_checked,
                                color: AppColors.primaryBlue,
                                size: 20,
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
