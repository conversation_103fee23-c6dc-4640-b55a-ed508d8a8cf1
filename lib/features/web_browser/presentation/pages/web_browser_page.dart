import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../config/themes/app_colors.dart';
import '../../../../core/utils/snackbar_service.dart';

@RoutePage()
class WebBrowserPage extends StatefulWidget {
  final String url;
  final String? title;

  const WebBrowserPage({
    super.key,
    required this.url,
    this.title,
  });

  @override
  State<WebBrowserPage> createState() => _WebBrowserPageState();
}

class _WebBrowserPageState extends State<WebBrowserPage> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            return _handleNavigationRequest(request);
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _isLoading = false;
            });
            if (error.description.contains('ERR_UNKNOWN_URL_SCHEME')) {
              _showUrlSchemeError();
            }
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2.withValues(alpha: 0.9),
      appBar: CustomAppBar(
        title: widget.title ?? 'Web browser',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppColors.blackTint1),
            onPressed: () => _controller.reload(),
          ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryBlue,
              ),
            ),
        ],
      ),
    );
  }

  /// Handles navigation requests to detect and launch external URLs
  NavigationDecision _handleNavigationRequest(NavigationRequest request) {
    final url = request.url.toLowerCase();

    // Check for intent URLs (Android app links)
    if (url.startsWith('intent://')) {
      _launchExternalUrl(request.url);
      return NavigationDecision.prevent;
    }

    // Check for other external schemes
    if (url.startsWith('mailto:') ||
        url.startsWith('tel:') ||
        url.startsWith('sms:') ||
        url.startsWith('market://') ||
        url.startsWith('play.google.com')) {
      _launchExternalUrl(request.url);
      return NavigationDecision.prevent;
    }

    // Check for Google Maps URLs that should open in the Maps app
    if (url.contains('maps.google.com') ||
        url.contains('maps.app.goo.gl') ||
        url.contains('goo.gl/maps')) {
      _launchExternalUrl(request.url);
      return NavigationDecision.prevent;
    }

    // Allow normal web navigation
    return NavigationDecision.navigate;
  }

  /// Launches external URLs using url_launcher
  Future<void> _launchExternalUrl(String url) async {
    try {
      final uri = Uri.parse(url);

      // For intent URLs, try to extract the actual URL
      if (url.startsWith('intent://')) {
        final extractedUrl = _extractUrlFromIntent(url);
        if (extractedUrl != null) {
          final extractedUri = Uri.parse(extractedUrl);
          if (await canLaunchUrl(extractedUri)) {
            await launchUrl(extractedUri, mode: LaunchMode.externalApplication);
            return;
          }
        }
      }

      // Try to launch the original URL
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showLaunchError(url);
      }
    } catch (e) {
      _showLaunchError(url);
    }
  }

  /// Extracts the actual URL from an Android intent URL
  String? _extractUrlFromIntent(String intentUrl) {
    try {
      // Intent URLs often contain the actual URL as a parameter
      final uri = Uri.parse(intentUrl);

      // Look for common URL parameters in intent URLs
      final fragment = uri.fragment;
      if (fragment.isNotEmpty) {
        // Try to find URL in fragment
        final urlMatch = RegExp(r'https?://[^\s;]+').firstMatch(fragment);
        if (urlMatch != null) {
          return urlMatch.group(0);
        }
      }

      // Look in query parameters
      final linkParam = uri.queryParameters['link'];
      if (linkParam != null && linkParam.startsWith('http')) {
        return linkParam;
      }

      // For Google Maps intent URLs, construct a maps URL
      if (intentUrl.contains('maps.google.com') ||
          intentUrl.contains('maps.app.goo.gl')) {
        return 'https://maps.google.com/';
      }
    } catch (e) {
      // If parsing fails, return null
    }
    return null;
  }

  /// Shows error when URL scheme is not supported
  void _showUrlSchemeError() {
    SnackBarService.warning(
      context: context,
      message:
          'This link requires an external app to open. Please install the required app.',
    );
  }

  /// Shows error when URL cannot be launched
  void _showLaunchError(String url) {
    SnackBarService.error(
      context: context,
      message:
          'Could not open external link. Please check if the required app is installed.',
    );
  }
}
