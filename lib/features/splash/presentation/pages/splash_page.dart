import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';

import '../../../../config/routes/app_router.gr.dart';

@RoutePage()
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    _checkAuthAndNavigate();
  }

  Future<void> _checkAuthAndNavigate() async {
    await Future.delayed(const Duration(seconds: 4));
    if (!mounted) return;

    // Check if location is available
    // final isLocationAvailable = await _checkLocationAvailability();
    // if (!isLocationAvailable) {
    //   if (mounted) {
    //     await _showLocationAlert();
    //   }
    //   return;
    // }

    final authToken = await sl<DataManager>().getAuthToken();
    if (authToken != null && authToken.isNotEmpty) {
      if (mounted) context.router.replace(const HomeRoute());
    } else {
      if (mounted) context.router.replace(const LoginRoute());
    }
  }

  // Future<bool> _checkLocationAvailability() async {
  //   try {
  //     final locationService = sl<LocationService>();
  //     return await locationService.isLocationAvailable();
  //   } catch (e) {
  //     debugPrint('Error checking location availability: $e');
  //     return false;
  //   }
  // }

  // Future<void> _showLocationAlert() async {
  //   if (!mounted) return;

  //   await ConfirmDialog.show(
  //     context: context,
  //     title: 'Location Access Required',
  //     message:
  //         'This app requires location access to function properly. Please enable location services and grant permission to continue.',
  //     confirmText: 'Retry',
  //     cancelText: 'Exit',
  //     onConfirm: () {
  //       _checkAuthAndNavigate();
  //     },
  //     onCancel: () {
  //       SystemNavigator.pop();
  //     },
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        forceMaterialTransparency: true,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF0077CC),
              Color(0xFF005799),
              Color(0xFF003366),
            ],
          ),
          image: DecorationImage(
            image: AssetImage(AppAssets.imgBg),
            fit: BoxFit.fill,
            opacity: 0.2,
          ),
        ),
        child: Stack(
          children: [
            Center(
              child: Image.asset(
                AppAssets.banner,
                width: size.width * 0.8,
              ),
            ),
            const Positioned(
              bottom: 40,
              left: 0,
              right: 0,
              child: Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              ),
            ),
            Positioned(
              bottom: 16,
              right: 16,
              child: Image.asset(
                AppAssets.poweredBy,
                height: 25,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
