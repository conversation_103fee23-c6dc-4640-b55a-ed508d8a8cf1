D/InputMethodManager( 9758): showSoftInput() view=io.flutter.embedding.android.FlutterView{5b2bc59 VFE...... .F...... 0,0-1080,2352 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor( 9758): The input method toggled cursor monitoring on
W/OnBackInvokedCallback( 9758): OnBackInvokedCallback is not enabled for the application.
W/OnBackInvokedCallback( 9758): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
D/InsetsController( 9758): show(ime(), fromIme=true)
I/Quality ( 9758): Skipped: true 1 cost 31.120716 refreshRate 16673364 bit true processName com.example.storetrack
[log] Auto-saving answer for measurement 1807461: 2
[log] Removing 0 existing answers (regular mode)
[log] Successfully saved 1 question answers to database
[log] Auto-saving answer for measurement 1807461: 22
[log] Removing 1 existing answers (regular mode)
[log] Successfully saved 1 question answers to database
D/OplusSystemUINavigationGesture( 9758): [GESTURE_BUTTON] swipe from 0
D/OplusSystemUINavigationGesture( 9758): [GESTURE_BUTTON] Hit Gesture Region !
D/InputConnectionAdaptor( 9758): The input method toggled cursor monitoring off
I/Quality ( 9758): Skipped: true 1 cost 24.206892 refreshRate 16674715 bit true processName com.example.storetrack
D/OplusSystemUINavigationGesture( 9758): [GESTURE_BUTTON] swipe from 0
D/OplusSystemUINavigationGesture( 9758): [GESTURE_BUTTON] Hit Gesture Region !
D/Activity( 9758): dispatchKeyEvent to com.example.storetrack.MainActivity@cb170c0 will call onBackPressed
[log] Removing 1 existing answers (regular mode)
[log] Successfully saved 1 question answers to database
[log] Removing 1 existing answers (regular mode)
[log] Successfully saved 1 question answers to database
[log] Auto-saved 1 valid answers on navigation
[log] Auto-saved 1 valid answers on navigation
[log] Found 11 photos from task PhotoFolders
[log] Found 11 photos from task PhotoFolders
[log] Current route: /home-route/sub-header-route
[log] Current route name: HomeRoute
W/OnBackInvokedCallback( 9758): OnBackInvokedCallback is not enabled for the application.
W/OnBackInvokedCallback( 9758): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
W/OnBackInvokedCallback( 9758): OnBackInvokedCallback is not enabled for the application.
W/OnBackInvokedCallback( 9758): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
W/OnBackInvokedCallback( 9758): OnBackInvokedCallback is not enabled for the application.
W/OnBackInvokedCallback( 9758): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
[log] Current route: /home-route/more-holder-route
[log] Current route name: HomeRoute
W/OnBackInvokedCallback( 9758): OnBackInvokedCallback is not enabled for the application.
W/OnBackInvokedCallback( 9758): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
[log] Retrieved 16 tasks from local database
[log] 🚀 Starting photo upload and sync workflow
I/Quality ( 9758): Skipped: false 24 cost 410.1188 refreshRate 16677560 bit true processName com.example.storetrack
[log] 🚀 Starting comprehensive photo upload and sync workflow
[log] 📤 Starting batch photo upload
[log] Found 0 photos to upload
[log] No photos to upload
[log] 🔄 Starting photo metadata sync
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Request ║ POST
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/sync_pic_info_mpt
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Headers
I/flutter ( 9758): ╟ Content-Type: application/json
I/flutter ( 9758): ╟ contentType: application/json
I/flutter ( 9758): ╟ responseType: ResponseType.json
I/flutter ( 9758): ╟ followRedirects: true
I/flutter ( 9758): ╟ connectTimeout: 0:00:20.000000
I/flutter ( 9758): ╟ receiveTimeout: 0:00:20.000000
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Body
I/flutter ( 9758): ╟ user_id: 64366
I/flutter ( 9758): ╟ token:
I/flutter ( 9758): ║ eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjcm9zc21hcmsuY29tLmF1IiwibGlua0NzcyI6IiIsI
I/flutter ( 9758): ║ mV4cCI6MTc1MjgyMTk0MiwiaWF0IjoxNzUyNzM1NTQyLCJjb250ZXh0Ijp7IndlZWtTdGFydCI6MH0sInVzZXIiOns
I/flutter ( 9758): ║ idXNlcklkIjoidGVzdC51c2VyQHN0b3JldHJhY2suY29tIn19.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83
I/flutter ( 9758): ║ PPc
I/flutter ( 9758): ╟ tasks: []
I/flutter ( 9758): ╟ deviceuid: TP1A.220905.001
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ║ {user_id: 64366, token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjcm9zc21hcmsuY29tL
I/flutter ( 9758): ║ mF1IiwibGlua0NzcyI6IiIsImV4cCI6MTc1MjgyMTk0MiwiaWF0IjoxNzUyNzM1NTQyLCJjb250ZXh0Ijp7IndlZWt
I/flutter ( 9758): ║ TdGFydCI6MH0sInVzZXIiOnsidXNlcklkIjoidGVzdC51c2VyQHN0b3JldHJhY2suY29tIn19.UpbXsapw2AYwXB9s
I/flutter ( 9758): ║ q48bKasUGDaXK1btV_hnYW83PPc, tasks: [], deviceuid: TP1A.220905.001}
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Response ║ POST ║ Status: 200 OK  ║ Time: 924 ms
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/sync_pic_info_mpt
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
[log] ✅ POST /sync_pic_info_mpt - Success
[log] Response status: 200
[log] ✅ Photo metadata sync completed successfully
[log] 🧹 Starting cleanup of soft-deleted photos
[log] 🧹 Cleanup completed: 0 deleted, 0 errors
[log] ✅ Photo cleanup completed: 0 deleted, 0 errors
[log] ✅ Photo upload and sync workflow completed successfully
[log] ✅ Photo upload and sync completed successfully
[log] 🚀 Starting signature upload and sync workflow
[log] 🚀 Starting comprehensive signature upload and sync workflow
[log] 📤 Starting batch signature upload
[log] Found 0 signatures to upload
[log] No signatures to upload
[log] 🔄 Starting signature metadata sync
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Request ║ POST
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/sync_sig_info
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Headers
I/flutter ( 9758): ╟ Content-Type: application/json
I/flutter ( 9758): ╟ contentType: application/json
I/flutter ( 9758): ╟ responseType: ResponseType.json
I/flutter ( 9758): ╟ followRedirects: true
I/flutter ( 9758): ╟ connectTimeout: 0:00:20.000000
I/flutter ( 9758): ╟ receiveTimeout: 0:00:20.000000
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Body
I/flutter ( 9758): ╟ user_id: 64366
I/flutter ( 9758): ╟ token:
I/flutter ( 9758): ║ eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjcm9zc21hcmsuY29tLmF1IiwibGlua0NzcyI6IiIsI
I/flutter ( 9758): ║ mV4cCI6MTc1MjgyMTk0MiwiaWF0IjoxNzUyNzM1NTQyLCJjb250ZXh0Ijp7IndlZWtTdGFydCI6MH0sInVzZXIiOns
I/flutter ( 9758): ║ idXNlcklkIjoidGVzdC51c2VyQHN0b3JldHJhY2suY29tIn19.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83
I/flutter ( 9758): ║ PPc
I/flutter ( 9758): ╟ tasks: []
I/flutter ( 9758): ╟ deviceuid: TP1A.220905.001
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ║ {user_id: 64366, token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjcm9zc21hcmsuY29tL
I/flutter ( 9758): ║ mF1IiwibGlua0NzcyI6IiIsImV4cCI6MTc1MjgyMTk0MiwiaWF0IjoxNzUyNzM1NTQyLCJjb250ZXh0Ijp7IndlZWt
I/flutter ( 9758): ║ TdGFydCI6MH0sInVzZXIiOnsidXNlcklkIjoidGVzdC51c2VyQHN0b3JldHJhY2suY29tIn19.UpbXsapw2AYwXB9s
I/flutter ( 9758): ║ q48bKasUGDaXK1btV_hnYW83PPc, tasks: [], deviceuid: TP1A.220905.001}
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Response ║ POST ║ Status: 200 OK  ║ Time: 284 ms
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/sync_sig_info
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
[log] ✅ POST /sync_sig_info - Success
[log] Response status: 200
[log] ✅ Signature metadata sync completed successfully
[log] 🧹 Starting cleanup of soft-deleted signatures
[log] ✅ Signature cleanup completed. Deleted: 0, Errors: 0
[log] ✅ Signature cleanup completed: 0 deleted, 0 errors
[log] ✅ Signature upload and sync workflow completed successfully
[log] ✅ Signature upload and sync completed successfully
[log] No tasks require report submission
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Request ║ GET
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/calendar_information?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83PPc&user_id=64366
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Headers
I/flutter ( 9758): ╟ Content-Type: application/json
I/flutter ( 9758): ╟ contentType: application/json
I/flutter ( 9758): ╟ responseType: ResponseType.json
I/flutter ( 9758): ╟ followRedirects: true
I/flutter ( 9758): ╟ connectTimeout: 0:00:20.000000
I/flutter ( 9758): ╟ receiveTimeout: 0:00:20.000000
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Request ║ GET
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/profile_postal?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83PPc&user_id=64366
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Query Parameters
I/flutter ( 9758): ╟ token:
I/flutter ( 9758): ║ eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjcm9zc21hcmsuY29tLmF1IiwibGlua0NzcyI6IiIsI
I/flutter ( 9758): ║ mV4cCI6MTc1MjgyMTk0MiwiaWF0IjoxNzUyNzM1NTQyLCJjb250ZXh0Ijp7IndlZWtTdGFydCI6MH0sInVzZXIiOns
I/flutter ( 9758): ║ idXNlcklkIjoidGVzdC51c2VyQHN0b3JldHJhY2suY29tIn19.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83
I/flutter ( 9758): ║ PPc
I/flutter ( 9758): ╟ user_id: 64366
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Headers
I/flutter ( 9758): ╟ Content-Type: application/json
I/flutter ( 9758): ╟ contentType: application/json
I/flutter ( 9758): ╟ responseType: ResponseType.json
I/flutter ( 9758): ╟ followRedirects: true
I/flutter ( 9758): ╟ connectTimeout: 0:00:20.000000
I/flutter ( 9758): ╟ receiveTimeout: 0:00:20.000000
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Request ║ POST
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/tasks_optimize
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Headers
I/flutter ( 9758): ╟ Content-Type: application/json
I/flutter ( 9758): ╟ contentType: application/json
I/flutter ( 9758): ╟ responseType: ResponseType.json
I/flutter ( 9758): ╟ followRedirects: true
I/flutter ( 9758): ╟ connectTimeout: 0:00:20.000000
I/flutter ( 9758): ╟ receiveTimeout: 0:00:20.000000
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Body
I/flutter ( 9758): ╟ device_uid: TP1A.220905.001
I/flutter ( 9758): ╟ user_id: 64366
I/flutter ( 9758): ╟ appversion: 9.9.9
I/flutter ( 9758): ╟ tasks:
I/flutter ( 9758): ║ [Instance of 'Task', Instance of 'Task', Instance of 'Task', Instance of 'Task', Instance
I/flutter ( 9758): ║ of 'Task', Instance of 'Task', Instance of 'Task', Instance of 'Task', Instance of 'Task',
I/flutter ( 9758): ║  Instance of 'Task', Instance of 'Task', Instance of 'Task', Instance of 'Task', Instance
I/flutter ( 9758): ║ of 'Task', Instance of 'Task', Instance of 'Task']
I/flutter ( 9758): ╟ token:
I/flutter ( 9758): ║ eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjcm9zc21hcmsuY29tLmF1IiwibGlua0NzcyI6IiIsI
I/flutter ( 9758): ║ mV4cCI6MTc1MjgyMTk0MiwiaWF0IjoxNzUyNzM1NTQyLCJjb250ZXh0Ijp7IndlZWtTdGFydCI6MH0sInVzZXIiOns
I/flutter ( 9758): ║ idXNlcklkIjoidGVzdC51c2VyQHN0b3JldHJhY2suY29tIn19.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83
I/flutter ( 9758): ║ PPc
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ║ {device_uid: TP1A.220905.001, user_id: 64366, appversion: 9.9.9, tasks: [Instance of 'Task
I/flutter ( 9758): ║ ', Instance of 'Task', Instance of 'Task', Instance of 'Task', Instance of 'Task', Instanc
I/flutter ( 9758): ║ e of 'Task', Instance of 'Task', Instance of 'Task', Instance of 'Task', Instance of 'Task
I/flutter ( 9758): ║ ', Instance of 'Task', Instance of 'Task', Instance of 'Task', Instance of 'Task', Instanc
I/flutter ( 9758): ║ e of 'Task', Instance of 'Task'], token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJj
I/flutter ( 9758): ║ cm9zc21hcmsuY29tLmF1IiwibGlua0NzcyI6IiIsImV4cCI6MTc1MjgyMTk0MiwiaWF0IjoxNzUyNzM1NTQyLCJjb2
I/flutter ( 9758): ║ 50ZXh0Ijp7IndlZWtTdGFydCI6MH0sInVzZXIiOnsidXNlcklkIjoidGVzdC51c2VyQHN0b3JldHJhY2suY29tIn19
I/flutter ( 9758): ║ .UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83PPc}
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Response ║ GET ║ Status: 200 OK  ║ Time: 357 ms
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/calendar_information?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83PPc&user_id=64366
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Response ║ GET ║ Status: 200 OK  ║ Time: 826 ms
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/profile_postal?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83PPc&user_id=64366
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Response ║ POST ║ Status: 200 OK  ║ Time: 4318 ms
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/tasks_optimize
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
[log] 📄 Processing 7 document updates
[log] 📄 Updated documents for task: 14061508
[log] 📄 Updated documents for task: 13840926
[log] 📄 Updated documents for task: 14540516
[log] 📄 Updated documents for task: 14497371
[log] 📄 Updated documents for task: 14155793
[log] 📄 Updated documents for task: 16172481
[log] 📄 Updated documents for task: 14023849
[log] SYNC_FORMS_002: [2025-07-19T13:11:50.990118] Starting form updates processing for 14 tasks
[log] SYNC_FORMS_003: [0/14] Processing task update for taskId: 19439362
[log] SYNC_FORMS_006: Found existing task 19439362 with 0 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 19439362:
[log] QA_DATA_004: [BEFORE_UPDATE] Task 19439362 has TOTAL 0 question answers across 0 forms
[log] SYNC_FORMS_009: Processing 0 server forms for task 19439362
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 0 server forms from TaskDetailMapper
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 19439362: 2025-07-02 09:23:05.000Z -> 2025-07-02 09:23:05.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 19439362:
[log] QA_DATA_004: [AFTER_UPDATE] Task 19439362 has TOTAL 0 question answers across 0 forms
[log] SYNC_FORMS_037: Completed forms update for task: 19439362
[log] SYNC_FORMS_003: [1/14] Processing task update for taskId: 14061508
[log] SYNC_FORMS_006: Found existing task 14061508 with 1 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 14061508:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 14061508 has TOTAL 0 question answers across 1 forms
[log] SYNC_FORMS_009: Processing 1 server forms for task 14061508
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 1 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 58813, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_015: Processing server form [0] - formId: 58813
[log] SYNC_FORMS_016: Found matching local form 58813 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 58813
[log] 📝 Updating form fields for formId: 58813
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 58813
[log] 📝 Removing form questions from realm for formId: 58813
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 58813
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 58813 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 58813
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 14061508: 2025-07-02 09:23:05.000Z -> 2025-07-02 09:23:05.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 14061508:
[log] QA_DATA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 14061508 has TOTAL 0 question answers across 1 forms
[log] SYNC_FORMS_037: Completed forms update for task: 14061508
[log] SYNC_FORMS_003: [2/14] Processing task update for taskId: 14497371
[log] SYNC_FORMS_006: Found existing task 14497371 with 3 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 14497371:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 13284 has 0 question answers
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 19439362, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 19439362, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] QA_DATA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 59973 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 14497371 has TOTAL 0 question answers across 3 forms
[log] SYNC_FORMS_009: Processing 3 server forms for task 14497371
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 14061508, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_011: Mapped 3 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 13284, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [1] - formId: 58813, questionAnswers count: 0, questions count: 3
[log] SYNC_FORMS_012: Server form [2] - formId: 59973, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_015: Processing server form [0] - formId: 13284
[log] SYNC_FORMS_016: Found matching local form 13284 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 13284
[log] 📝 Updating form fields for formId: 13284
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 13284
[log] 📝 Removing form questions from realm for formId: 13284
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 13284
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 13284 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 13284
[log] SYNC_FORMS_015: Processing server form [1] - formId: 58813
[log] SYNC_FORMS_016: Found matching local form 58813 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 58813
[log] 📝 Updating form fields for formId: 58813
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 58813
[log] 📝 Removing form questions from realm for formId: 58813
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 3 server questions to local form 58813
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 14061508, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 14497371, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 14497371, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 58813 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 58813
[log] SYNC_FORMS_015: Processing server form [2] - formId: 59973
[log] SYNC_FORMS_016: Found matching local form 59973 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 59973
[log] 📝 Updating form fields for formId: 59973
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 59973
[log] 📝 Removing form questions from realm for formId: 59973
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 59973
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 59973 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 59973
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 14497371: 2025-07-04 07:57:00.000Z -> 2025-07-04 07:57:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 14497371:
[log] QA_DATA_002: [AFTER_UPDATE] Form 13284 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 59973 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 14497371 has TOTAL 0 question answers across 3 forms
[log] SYNC_FORMS_037: Completed forms update for task: 14497371
[log] SYNC_FORMS_003: [3/14] Processing task update for taskId: 14155793
[log] SYNC_FORMS_006: Found existing task 14155793 with 1 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 14155793:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 14155793 has TOTAL 0 question answers across 1 forms
[log] SYNC_FORMS_009: Processing 1 server forms for task 14155793
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 1 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 58813, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_015: Processing server form [0] - formId: 58813
[log] SYNC_FORMS_016: Found matching local form 58813 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 58813
[log] 📝 Updating form fields for formId: 58813
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 58813
[log] 📝 Removing form questions from realm for formId: 58813
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 58813
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 14155793, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 14155793, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 58813 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 58813
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 14155793: 2025-07-03 00:38:00.000Z -> 2025-07-03 00:38:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 14155793:
[log] QA_DATA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 14155793 has TOTAL 0 question answers across 1 forms
[log] SYNC_FORMS_037: Completed forms update for task: 14155793
[log] SYNC_FORMS_003: [4/14] Processing task update for taskId: 16172481
[log] SYNC_FORMS_006: Found existing task 16172481 with 1 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 16172481:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 16172481 has TOTAL 0 question answers across 1 forms
[log] SYNC_FORMS_009: Processing 1 server forms for task 16172481
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 1 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 58813, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_015: Processing server form [0] - formId: 58813
[log] SYNC_FORMS_016: Found matching local form 58813 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 58813
[log] 📝 Updating form fields for formId: 58813
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 58813
[log] 📝 Removing form questions from realm for formId: 58813
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 58813
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 58813 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 58813
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 16172481: 2025-07-03 10:58:00.000Z -> 2025-07-03 10:58:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 16172481:
[log] QA_DATA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 16172481 has TOTAL 0 question answers across 1 forms
[log] SYNC_FORMS_037: Completed forms update for task: 16172481
[log] SYNC_FORMS_003: [5/14] Processing task update for taskId: 19308875
[log] SYNC_FORMS_006: Found existing task 19308875 with 11 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 19308875:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 19308875 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_009: Processing 11 server forms for task 19308875
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 11 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 64137, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [1] - formId: 63894, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [2] - formId: 64350, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [3] - formId: 63896, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [4] - formId: 63897, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [5] - formId: 63892, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [6] - formId: 63893, questionAnswers count: 0, questions count: 12
[log] SYNC_FORMS_012: Server form [7] - formId: 63899, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [8] - formId: 63895, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [9] - formId: 63891, questionAnswers count: 0, questions count: 8
[log] SYNC_FORMS_012: Server form [10] - formId: 63898, questionAnswers count: 0, questions count: 0
[log] SYNC_FORMS_015: Processing server form [0] - formId: 64137
[log] SYNC_FORMS_016: Found matching local form 64137 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64137
[log] 📝 Updating form fields for formId: 64137
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64137
[log] 📝 Removing form questions from realm for formId: 64137
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64137
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64137 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64137
[log] SYNC_FORMS_015: Processing server form [1] - formId: 63894
[log] SYNC_FORMS_016: Found matching local form 63894 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63894
[log] 📝 Updating form fields for formId: 63894
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63894
[log] 📝 Removing form questions from realm for formId: 63894
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63894
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63894 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63894
[log] SYNC_FORMS_015: Processing server form [2] - formId: 64350
[log] SYNC_FORMS_016: Found matching local form 64350 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64350
[log] 📝 Updating form fields for formId: 64350
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64350
[log] 📝 Removing form questions from realm for formId: 64350
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64350
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64350 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64350
[log] SYNC_FORMS_015: Processing server form [3] - formId: 63896
[log] SYNC_FORMS_016: Found matching local form 63896 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63896
[log] 📝 Updating form fields for formId: 63896
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63896
[log] 📝 Removing form questions from realm for formId: 63896
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63896
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63896 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63896
[log] SYNC_FORMS_015: Processing server form [4] - formId: 63897
[log] SYNC_FORMS_016: Found matching local form 63897 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63897
[log] 📝 Updating form fields for formId: 63897
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63897
[log] 📝 Removing form questions from realm for formId: 63897
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63897
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63897 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63897
[log] SYNC_FORMS_015: Processing server form [5] - formId: 63892
[log] SYNC_FORMS_016: Found matching local form 63892 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63892
[log] 📝 Updating form fields for formId: 63892
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63892
[log] 📝 Removing form questions from realm for formId: 63892
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63892
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63892 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63892
[log] SYNC_FORMS_015: Processing server form [6] - formId: 63893
[log] SYNC_FORMS_016: Found matching local form 63893 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63893
[log] 📝 Updating form fields for formId: 63893
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63893
[log] 📝 Removing form questions from realm for formId: 63893
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 12 server questions to local form 63893
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63893 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63893
[log] SYNC_FORMS_015: Processing server form [7] - formId: 63899
[log] SYNC_FORMS_016: Found matching local form 63899 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63899
[log] 📝 Updating form fields for formId: 63899
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63899
[log] 📝 Removing form questions from realm for formId: 63899
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63899
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63899 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63899
[log] SYNC_FORMS_015: Processing server form [8] - formId: 63895
[log] SYNC_FORMS_016: Found matching local form 63895 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63895
[log] 📝 Updating form fields for formId: 63895
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63895
[log] 📝 Removing form questions from realm for formId: 63895
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63895
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63895 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63895
[log] SYNC_FORMS_015: Processing server form [9] - formId: 63891
[log] SYNC_FORMS_016: Found matching local form 63891 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63891
[log] 📝 Updating form fields for formId: 63891
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63891
[log] 📝 Removing form questions from realm for formId: 63891
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 8 server questions to local form 63891
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63891 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63891
[log] SYNC_FORMS_015: Processing server form [10] - formId: 63898
[log] SYNC_FORMS_016: Found matching local form 63898 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63898
[log] 📝 Updating form fields for formId: 63898
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63898
[log] 📝 Removing form questions from realm for formId: 63898
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 0 server questions to local form 63898
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63898 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63898
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 19308875: 2025-07-18 11:45:00.000Z -> 2025-07-18 11:45:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 19308875:
[log] QA_DATA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 19308875 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_037: Completed forms update for task: 19308875
[log] SYNC_FORMS_003: [6/14] Processing task update for taskId: 19308876
[log] SYNC_FORMS_006: Found existing task 19308876 with 11 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 19308876:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 19308876 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_009: Processing 11 server forms for task 19308876
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 11 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 64137, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [1] - formId: 63894, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [2] - formId: 64350, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [3] - formId: 63896, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [4] - formId: 63897, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [5] - formId: 63892, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [6] - formId: 63893, questionAnswers count: 0, questions count: 12
[log] SYNC_FORMS_012: Server form [7] - formId: 63899, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [8] - formId: 63895, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [9] - formId: 63891, questionAnswers count: 0, questions count: 8
[log] SYNC_FORMS_012: Server form [10] - formId: 63898, questionAnswers count: 0, questions count: 0
[log] SYNC_FORMS_015: Processing server form [0] - formId: 64137
[log] SYNC_FORMS_016: Found matching local form 64137 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64137
[log] 📝 Updating form fields for formId: 64137
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64137
[log] 📝 Removing form questions from realm for formId: 64137
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64137
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64137 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64137
[log] SYNC_FORMS_015: Processing server form [1] - formId: 63894
[log] SYNC_FORMS_016: Found matching local form 63894 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63894
[log] 📝 Updating form fields for formId: 63894
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63894
[log] 📝 Removing form questions from realm for formId: 63894
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63894
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63894 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63894
[log] SYNC_FORMS_015: Processing server form [2] - formId: 64350
[log] SYNC_FORMS_016: Found matching local form 64350 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64350
[log] 📝 Updating form fields for formId: 64350
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64350
[log] 📝 Removing form questions from realm for formId: 64350
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64350
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64350 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64350
[log] SYNC_FORMS_015: Processing server form [3] - formId: 63896
[log] SYNC_FORMS_016: Found matching local form 63896 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63896
[log] 📝 Updating form fields for formId: 63896
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63896
[log] 📝 Removing form questions from realm for formId: 63896
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63896
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63896 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63896
[log] SYNC_FORMS_015: Processing server form [4] - formId: 63897
[log] SYNC_FORMS_016: Found matching local form 63897 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63897
[log] 📝 Updating form fields for formId: 63897
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63897
[log] 📝 Removing form questions from realm for formId: 63897
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63897
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63897 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63897
[log] SYNC_FORMS_015: Processing server form [5] - formId: 63892
[log] SYNC_FORMS_016: Found matching local form 63892 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63892
[log] 📝 Updating form fields for formId: 63892
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63892
[log] 📝 Removing form questions from realm for formId: 63892
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63892
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63892 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63892
[log] SYNC_FORMS_015: Processing server form [6] - formId: 63893
[log] SYNC_FORMS_016: Found matching local form 63893 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63893
[log] 📝 Updating form fields for formId: 63893
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63893
[log] 📝 Removing form questions from realm for formId: 63893
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 12 server questions to local form 63893
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63893 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63893
[log] SYNC_FORMS_015: Processing server form [7] - formId: 63899
[log] SYNC_FORMS_016: Found matching local form 63899 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63899
[log] 📝 Updating form fields for formId: 63899
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63899
[log] 📝 Removing form questions from realm for formId: 63899
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63899
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63899 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63899
[log] SYNC_FORMS_015: Processing server form [8] - formId: 63895
[log] SYNC_FORMS_016: Found matching local form 63895 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63895
[log] 📝 Updating form fields for formId: 63895
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63895
[log] 📝 Removing form questions from realm for formId: 63895
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63895
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63895 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63895
[log] SYNC_FORMS_015: Processing server form [9] - formId: 63891
[log] SYNC_FORMS_016: Found matching local form 63891 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63891
[log] 📝 Updating form fields for formId: 63891
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63891
[log] 📝 Removing form questions from realm for formId: 63891
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 8 server questions to local form 63891
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63891 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63891
[log] SYNC_FORMS_015: Processing server form [10] - formId: 63898
[log] SYNC_FORMS_016: Found matching local form 63898 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63898
[log] 📝 Updating form fields for formId: 63898
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63898
[log] 📝 Removing form questions from realm for formId: 63898
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 0 server questions to local form 63898
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63898 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63898
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 19308876: 2025-07-18 11:45:00.000Z -> 2025-07-18 11:45:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 19308876:
[log] QA_DATA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 19308876 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_037: Completed forms update for task: 19308876
[log] SYNC_FORMS_003: [7/14] Processing task update for taskId: 19308871
[log] SYNC_FORMS_006: Found existing task 19308871 with 11 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 19308871:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 19308871 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_009: Processing 11 server forms for task 19308871
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 11 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 64137, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [1] - formId: 63894, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [2] - formId: 64350, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [3] - formId: 63896, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [4] - formId: 63897, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [5] - formId: 63892, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [6] - formId: 63893, questionAnswers count: 0, questions count: 12
[log] SYNC_FORMS_012: Server form [7] - formId: 63899, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [8] - formId: 63895, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [9] - formId: 63891, questionAnswers count: 0, questions count: 8
[log] SYNC_FORMS_012: Server form [10] - formId: 63898, questionAnswers count: 0, questions count: 0
[log] SYNC_FORMS_015: Processing server form [0] - formId: 64137
[log] SYNC_FORMS_016: Found matching local form 64137 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64137
[log] 📝 Updating form fields for formId: 64137
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64137
[log] 📝 Removing form questions from realm for formId: 64137
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64137
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64137 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64137
[log] SYNC_FORMS_015: Processing server form [1] - formId: 63894
[log] SYNC_FORMS_016: Found matching local form 63894 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63894
[log] 📝 Updating form fields for formId: 63894
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63894
[log] 📝 Removing form questions from realm for formId: 63894
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63894
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63894 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63894
[log] SYNC_FORMS_015: Processing server form [2] - formId: 64350
[log] SYNC_FORMS_016: Found matching local form 64350 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64350
[log] 📝 Updating form fields for formId: 64350
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64350
[log] 📝 Removing form questions from realm for formId: 64350
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64350
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64350 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64350
[log] SYNC_FORMS_015: Processing server form [3] - formId: 63896
[log] SYNC_FORMS_016: Found matching local form 63896 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63896
[log] 📝 Updating form fields for formId: 63896
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63896
[log] 📝 Removing form questions from realm for formId: 63896
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63896
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63896 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63896
[log] SYNC_FORMS_015: Processing server form [4] - formId: 63897
[log] SYNC_FORMS_016: Found matching local form 63897 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63897
[log] 📝 Updating form fields for formId: 63897
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63897
[log] 📝 Removing form questions from realm for formId: 63897
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63897
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63897 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63897
[log] SYNC_FORMS_015: Processing server form [5] - formId: 63892
[log] SYNC_FORMS_016: Found matching local form 63892 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63892
[log] 📝 Updating form fields for formId: 63892
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63892
[log] 📝 Removing form questions from realm for formId: 63892
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63892
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63892 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63892
[log] SYNC_FORMS_015: Processing server form [6] - formId: 63893
[log] SYNC_FORMS_016: Found matching local form 63893 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63893
[log] 📝 Updating form fields for formId: 63893
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63893
[log] 📝 Removing form questions from realm for formId: 63893
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 12 server questions to local form 63893
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63893 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63893
[log] SYNC_FORMS_015: Processing server form [7] - formId: 63899
[log] SYNC_FORMS_016: Found matching local form 63899 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63899
[log] 📝 Updating form fields for formId: 63899
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63899
[log] 📝 Removing form questions from realm for formId: 63899
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63899
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63899 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63899
[log] SYNC_FORMS_015: Processing server form [8] - formId: 63895
[log] SYNC_FORMS_016: Found matching local form 63895 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63895
[log] 📝 Updating form fields for formId: 63895
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63895
[log] 📝 Removing form questions from realm for formId: 63895
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63895
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63895 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63895
[log] SYNC_FORMS_015: Processing server form [9] - formId: 63891
[log] SYNC_FORMS_016: Found matching local form 63891 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63891
[log] 📝 Updating form fields for formId: 63891
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63891
[log] 📝 Removing form questions from realm for formId: 63891
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 8 server questions to local form 63891
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63891 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63891
[log] SYNC_FORMS_015: Processing server form [10] - formId: 63898
[log] SYNC_FORMS_016: Found matching local form 63898 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63898
[log] 📝 Updating form fields for formId: 63898
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63898
[log] 📝 Removing form questions from realm for formId: 63898
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 0 server questions to local form 63898
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63898 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63898
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 19308871: 2025-07-18 11:45:00.000Z -> 2025-07-18 11:45:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 19308871:
[log] QA_DATA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 19308871 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_037: Completed forms update for task: 19308871
[log] SYNC_FORMS_003: [8/14] Processing task update for taskId: 19308872
[log] SYNC_FORMS_006: Found existing task 19308872 with 11 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 19308872:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 19308872 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_009: Processing 11 server forms for task 19308872
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 11 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 64137, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [1] - formId: 63894, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [2] - formId: 64350, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [3] - formId: 63896, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [4] - formId: 63897, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [5] - formId: 63892, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [6] - formId: 63893, questionAnswers count: 0, questions count: 12
[log] SYNC_FORMS_012: Server form [7] - formId: 63899, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [8] - formId: 63895, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [9] - formId: 63891, questionAnswers count: 0, questions count: 8
[log] SYNC_FORMS_012: Server form [10] - formId: 63898, questionAnswers count: 0, questions count: 0
[log] SYNC_FORMS_015: Processing server form [0] - formId: 64137
[log] SYNC_FORMS_016: Found matching local form 64137 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64137
[log] 📝 Updating form fields for formId: 64137
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64137
[log] 📝 Removing form questions from realm for formId: 64137
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64137
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64137 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64137
[log] SYNC_FORMS_015: Processing server form [1] - formId: 63894
[log] SYNC_FORMS_016: Found matching local form 63894 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63894
[log] 📝 Updating form fields for formId: 63894
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63894
[log] 📝 Removing form questions from realm for formId: 63894
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63894
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63894 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63894
[log] SYNC_FORMS_015: Processing server form [2] - formId: 64350
[log] SYNC_FORMS_016: Found matching local form 64350 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64350
[log] 📝 Updating form fields for formId: 64350
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64350
[log] 📝 Removing form questions from realm for formId: 64350
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64350
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64350 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64350
[log] SYNC_FORMS_015: Processing server form [3] - formId: 63896
[log] SYNC_FORMS_016: Found matching local form 63896 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63896
[log] 📝 Updating form fields for formId: 63896
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63896
[log] 📝 Removing form questions from realm for formId: 63896
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63896
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63896 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63896
[log] SYNC_FORMS_015: Processing server form [4] - formId: 63897
[log] SYNC_FORMS_016: Found matching local form 63897 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63897
[log] 📝 Updating form fields for formId: 63897
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63897
[log] 📝 Removing form questions from realm for formId: 63897
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63897
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63897 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63897
[log] SYNC_FORMS_015: Processing server form [5] - formId: 63892
[log] SYNC_FORMS_016: Found matching local form 63892 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63892
[log] 📝 Updating form fields for formId: 63892
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63892
[log] 📝 Removing form questions from realm for formId: 63892
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63892
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63892 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63892
[log] SYNC_FORMS_015: Processing server form [6] - formId: 63893
[log] SYNC_FORMS_016: Found matching local form 63893 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63893
[log] 📝 Updating form fields for formId: 63893
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63893
[log] 📝 Removing form questions from realm for formId: 63893
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 12 server questions to local form 63893
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63893 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63893
[log] SYNC_FORMS_015: Processing server form [7] - formId: 63899
[log] SYNC_FORMS_016: Found matching local form 63899 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63899
[log] 📝 Updating form fields for formId: 63899
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63899
[log] 📝 Removing form questions from realm for formId: 63899
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63899
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63899 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63899
[log] SYNC_FORMS_015: Processing server form [8] - formId: 63895
[log] SYNC_FORMS_016: Found matching local form 63895 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63895
[log] 📝 Updating form fields for formId: 63895
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63895
[log] 📝 Removing form questions from realm for formId: 63895
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63895
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63895 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63895
[log] SYNC_FORMS_015: Processing server form [9] - formId: 63891
[log] SYNC_FORMS_016: Found matching local form 63891 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63891
[log] 📝 Updating form fields for formId: 63891
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63891
[log] 📝 Removing form questions from realm for formId: 63891
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 8 server questions to local form 63891
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63891 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63891
[log] SYNC_FORMS_015: Processing server form [10] - formId: 63898
[log] SYNC_FORMS_016: Found matching local form 63898 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63898
[log] 📝 Updating form fields for formId: 63898
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63898
[log] 📝 Removing form questions from realm for formId: 63898
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 0 server questions to local form 63898
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63898 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63898
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 19308872: 2025-07-18 11:45:00.000Z -> 2025-07-18 11:45:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 19308872:
[log] QA_DATA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 19308872 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_037: Completed forms update for task: 19308872
[log] SYNC_FORMS_003: [9/14] Processing task update for taskId: 19308873
[log] SYNC_FORMS_006: Found existing task 19308873 with 11 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 19308873:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63891 has 1 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 19308873 has TOTAL 1 question answers across 11 forms
[log] SYNC_FORMS_009: Processing 11 server forms for task 19308873
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 11 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 64137, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [1] - formId: 63894, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [2] - formId: 64350, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [3] - formId: 63896, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [4] - formId: 63897, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [5] - formId: 63892, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [6] - formId: 63893, questionAnswers count: 0, questions count: 12
[log] SYNC_FORMS_012: Server form [7] - formId: 63899, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [8] - formId: 63895, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [9] - formId: 63891, questionAnswers count: 0, questions count: 8
[log] SYNC_FORMS_012: Server form [10] - formId: 63898, questionAnswers count: 0, questions count: 0
[log] SYNC_FORMS_015: Processing server form [0] - formId: 64137
[log] SYNC_FORMS_016: Found matching local form 64137 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64137
[log] 📝 Updating form fields for formId: 64137
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64137
[log] 📝 Removing form questions from realm for formId: 64137
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64137
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64137 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64137
[log] SYNC_FORMS_015: Processing server form [1] - formId: 63894
[log] SYNC_FORMS_016: Found matching local form 63894 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63894
[log] 📝 Updating form fields for formId: 63894
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63894
[log] 📝 Removing form questions from realm for formId: 63894
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63894
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63894 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63894
[log] SYNC_FORMS_015: Processing server form [2] - formId: 64350
[log] SYNC_FORMS_016: Found matching local form 64350 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64350
[log] 📝 Updating form fields for formId: 64350
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64350
[log] 📝 Removing form questions from realm for formId: 64350
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64350
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64350 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64350
[log] SYNC_FORMS_015: Processing server form [3] - formId: 63896
[log] SYNC_FORMS_016: Found matching local form 63896 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63896
[log] 📝 Updating form fields for formId: 63896
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63896
[log] 📝 Removing form questions from realm for formId: 63896
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63896
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63896 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63896
[log] SYNC_FORMS_015: Processing server form [4] - formId: 63897
[log] SYNC_FORMS_016: Found matching local form 63897 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63897
[log] 📝 Updating form fields for formId: 63897
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63897
[log] 📝 Removing form questions from realm for formId: 63897
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63897
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63897 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63897
[log] SYNC_FORMS_015: Processing server form [5] - formId: 63892
[log] SYNC_FORMS_016: Found matching local form 63892 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63892
[log] 📝 Updating form fields for formId: 63892
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63892
[log] 📝 Removing form questions from realm for formId: 63892
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63892
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63892 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63892
[log] SYNC_FORMS_015: Processing server form [6] - formId: 63893
[log] SYNC_FORMS_016: Found matching local form 63893 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63893
[log] 📝 Updating form fields for formId: 63893
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63893
[log] 📝 Removing form questions from realm for formId: 63893
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 12 server questions to local form 63893
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63893 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63893
[log] SYNC_FORMS_015: Processing server form [7] - formId: 63899
[log] SYNC_FORMS_016: Found matching local form 63899 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63899
[log] 📝 Updating form fields for formId: 63899
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63899
[log] 📝 Removing form questions from realm for formId: 63899
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63899
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63899 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63899
[log] SYNC_FORMS_015: Processing server form [8] - formId: 63895
[log] SYNC_FORMS_016: Found matching local form 63895 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63895
[log] 📝 Updating form fields for formId: 63895
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63895
[log] 📝 Removing form questions from realm for formId: 63895
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63895
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63895 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63895
[log] SYNC_FORMS_015: Processing server form [9] - formId: 63891
[log] SYNC_FORMS_016: Found matching local form 63891 with 1 existing question answers
[log] SYNC_FORMS_017: Local form 63891 EXISTING question answers BEFORE update:
[log] SYNC_FORMS_019: Updating individual fields for existing form 63891
[log] 📝 Updating form fields for formId: 63891
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 1
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63891
[log] 📝 Removing form questions from realm for formId: 63891
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 1 (was: 1)
[log] SYNC_FORMS_023: Adding 8 server questions to local form 63891
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 1
[log] SYNC_FORMS_025: Local form 63891 FINAL question answers AFTER update:
[log] SYNC_FORMS_028: Updated existing form: 63891
[log] SYNC_FORMS_015: Processing server form [10] - formId: 63898
[log] SYNC_FORMS_016: Found matching local form 63898 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63898
[log] 📝 Updating form fields for formId: 63898
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63898
[log] 📝 Removing form questions from realm for formId: 63898
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 0 server questions to local form 63898
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63898 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63898
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 19308873: 2025-07-18 11:45:00.000Z -> 2025-07-18 11:45:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 19308873:
[log] QA_DATA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63891 has 1 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 19308873 has TOTAL 1 question answers across 11 forms
[log] SYNC_FORMS_037: Completed forms update for task: 19308873
[log] SYNC_FORMS_003: [10/14] Processing task update for taskId: 19308874
[log] SYNC_FORMS_006: Found existing task 19308874 with 11 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 19308874:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 19308874 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_009: Processing 11 server forms for task 19308874
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 11 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 64137, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [1] - formId: 63894, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [2] - formId: 64350, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [3] - formId: 63896, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [4] - formId: 63897, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [5] - formId: 63892, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [6] - formId: 63893, questionAnswers count: 0, questions count: 12
[log] SYNC_FORMS_012: Server form [7] - formId: 63899, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [8] - formId: 63895, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [9] - formId: 63891, questionAnswers count: 0, questions count: 8
[log] SYNC_FORMS_012: Server form [10] - formId: 63898, questionAnswers count: 0, questions count: 0
[log] SYNC_FORMS_015: Processing server form [0] - formId: 64137
[log] SYNC_FORMS_016: Found matching local form 64137 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64137
[log] 📝 Updating form fields for formId: 64137
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64137
[log] 📝 Removing form questions from realm for formId: 64137
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64137
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64137 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64137
[log] SYNC_FORMS_015: Processing server form [1] - formId: 63894
[log] SYNC_FORMS_016: Found matching local form 63894 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63894
[log] 📝 Updating form fields for formId: 63894
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63894
[log] 📝 Removing form questions from realm for formId: 63894
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63894
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63894 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63894
[log] SYNC_FORMS_015: Processing server form [2] - formId: 64350
[log] SYNC_FORMS_016: Found matching local form 64350 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64350
[log] 📝 Updating form fields for formId: 64350
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64350
[log] 📝 Removing form questions from realm for formId: 64350
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64350
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64350 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64350
[log] SYNC_FORMS_015: Processing server form [3] - formId: 63896
[log] SYNC_FORMS_016: Found matching local form 63896 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63896
[log] 📝 Updating form fields for formId: 63896
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63896
[log] 📝 Removing form questions from realm for formId: 63896
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63896
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63896 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63896
[log] SYNC_FORMS_015: Processing server form [4] - formId: 63897
[log] SYNC_FORMS_016: Found matching local form 63897 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63897
[log] 📝 Updating form fields for formId: 63897
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63897
[log] 📝 Removing form questions from realm for formId: 63897
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63897
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63897 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63897
[log] SYNC_FORMS_015: Processing server form [5] - formId: 63892
[log] SYNC_FORMS_016: Found matching local form 63892 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63892
[log] 📝 Updating form fields for formId: 63892
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63892
[log] 📝 Removing form questions from realm for formId: 63892
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63892
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63892 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63892
[log] SYNC_FORMS_015: Processing server form [6] - formId: 63893
[log] SYNC_FORMS_016: Found matching local form 63893 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63893
[log] 📝 Updating form fields for formId: 63893
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63893
[log] 📝 Removing form questions from realm for formId: 63893
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 12 server questions to local form 63893
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63893 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63893
[log] SYNC_FORMS_015: Processing server form [7] - formId: 63899
[log] SYNC_FORMS_016: Found matching local form 63899 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63899
[log] 📝 Updating form fields for formId: 63899
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63899
[log] 📝 Removing form questions from realm for formId: 63899
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63899
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63899 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63899
[log] SYNC_FORMS_015: Processing server form [8] - formId: 63895
[log] SYNC_FORMS_016: Found matching local form 63895 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63895
[log] 📝 Updating form fields for formId: 63895
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63895
[log] 📝 Removing form questions from realm for formId: 63895
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63895
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63895 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63895
[log] SYNC_FORMS_015: Processing server form [9] - formId: 63891
[log] SYNC_FORMS_016: Found matching local form 63891 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63891
[log] 📝 Updating form fields for formId: 63891
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63891
[log] 📝 Removing form questions from realm for formId: 63891
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 8 server questions to local form 63891
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63891 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63891
[log] SYNC_FORMS_015: Processing server form [10] - formId: 63898
[log] SYNC_FORMS_016: Found matching local form 63898 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63898
[log] 📝 Updating form fields for formId: 63898
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63898
[log] 📝 Removing form questions from realm for formId: 63898
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 0 server questions to local form 63898
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63898 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63898
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 19308874: 2025-07-18 11:45:00.000Z -> 2025-07-18 11:45:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 19308874:
[log] QA_DATA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 19308874 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_037: Completed forms update for task: 19308874
[log] SYNC_FORMS_003: [11/14] Processing task update for taskId: 19308878
[log] SYNC_FORMS_006: Found existing task 19308878 with 11 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 19308878:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 19308878 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_009: Processing 11 server forms for task 19308878
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 11 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 64137, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [1] - formId: 63894, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [2] - formId: 64350, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [3] - formId: 63896, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [4] - formId: 63897, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [5] - formId: 63892, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [6] - formId: 63893, questionAnswers count: 0, questions count: 12
[log] SYNC_FORMS_012: Server form [7] - formId: 63899, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [8] - formId: 63895, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [9] - formId: 63891, questionAnswers count: 0, questions count: 8
[log] SYNC_FORMS_012: Server form [10] - formId: 63898, questionAnswers count: 0, questions count: 0
[log] SYNC_FORMS_015: Processing server form [0] - formId: 64137
[log] SYNC_FORMS_016: Found matching local form 64137 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64137
[log] 📝 Updating form fields for formId: 64137
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64137
[log] 📝 Removing form questions from realm for formId: 64137
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64137
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64137 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64137
[log] SYNC_FORMS_015: Processing server form [1] - formId: 63894
[log] SYNC_FORMS_016: Found matching local form 63894 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63894
[log] 📝 Updating form fields for formId: 63894
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63894
[log] 📝 Removing form questions from realm for formId: 63894
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63894
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63894 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63894
[log] SYNC_FORMS_015: Processing server form [2] - formId: 64350
[log] SYNC_FORMS_016: Found matching local form 64350 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64350
[log] 📝 Updating form fields for formId: 64350
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64350
[log] 📝 Removing form questions from realm for formId: 64350
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64350
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64350 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64350
[log] SYNC_FORMS_015: Processing server form [3] - formId: 63896
[log] SYNC_FORMS_016: Found matching local form 63896 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63896
[log] 📝 Updating form fields for formId: 63896
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63896
[log] 📝 Removing form questions from realm for formId: 63896
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63896
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63896 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63896
[log] SYNC_FORMS_015: Processing server form [4] - formId: 63897
[log] SYNC_FORMS_016: Found matching local form 63897 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63897
[log] 📝 Updating form fields for formId: 63897
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63897
[log] 📝 Removing form questions from realm for formId: 63897
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63897
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63897 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63897
[log] SYNC_FORMS_015: Processing server form [5] - formId: 63892
[log] SYNC_FORMS_016: Found matching local form 63892 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63892
[log] 📝 Updating form fields for formId: 63892
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63892
[log] 📝 Removing form questions from realm for formId: 63892
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63892
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63892 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63892
[log] SYNC_FORMS_015: Processing server form [6] - formId: 63893
[log] SYNC_FORMS_016: Found matching local form 63893 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63893
[log] 📝 Updating form fields for formId: 63893
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63893
[log] 📝 Removing form questions from realm for formId: 63893
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 12 server questions to local form 63893
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63893 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63893
[log] SYNC_FORMS_015: Processing server form [7] - formId: 63899
[log] SYNC_FORMS_016: Found matching local form 63899 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63899
[log] 📝 Updating form fields for formId: 63899
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63899
[log] 📝 Removing form questions from realm for formId: 63899
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63899
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63899 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63899
[log] SYNC_FORMS_015: Processing server form [8] - formId: 63895
[log] SYNC_FORMS_016: Found matching local form 63895 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63895
[log] 📝 Updating form fields for formId: 63895
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63895
[log] 📝 Removing form questions from realm for formId: 63895
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63895
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63895 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63895
[log] SYNC_FORMS_015: Processing server form [9] - formId: 63891
[log] SYNC_FORMS_016: Found matching local form 63891 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63891
[log] 📝 Updating form fields for formId: 63891
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63891
[log] 📝 Removing form questions from realm for formId: 63891
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 8 server questions to local form 63891
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63891 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63891
[log] SYNC_FORMS_015: Processing server form [10] - formId: 63898
[log] SYNC_FORMS_016: Found matching local form 63898 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63898
[log] 📝 Updating form fields for formId: 63898
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63898
[log] 📝 Removing form questions from realm for formId: 63898
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 0 server questions to local form 63898
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63898 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63898
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 19308878: 2025-07-18 11:45:00.000Z -> 2025-07-18 11:45:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 19308878:
[log] QA_DATA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 19308878 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_037: Completed forms update for task: 19308878
[log] SYNC_FORMS_003: [12/14] Processing task update for taskId: 19308879
[log] SYNC_FORMS_006: Found existing task 19308879 with 11 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 19308879:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 19308879 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_009: Processing 11 server forms for task 19308879
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 11 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 64137, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [1] - formId: 63894, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [2] - formId: 64350, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [3] - formId: 63896, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [4] - formId: 63897, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [5] - formId: 63892, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [6] - formId: 63893, questionAnswers count: 0, questions count: 12
[log] SYNC_FORMS_012: Server form [7] - formId: 63899, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [8] - formId: 63895, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_012: Server form [9] - formId: 63891, questionAnswers count: 0, questions count: 8
[log] SYNC_FORMS_012: Server form [10] - formId: 63898, questionAnswers count: 0, questions count: 0
[log] SYNC_FORMS_015: Processing server form [0] - formId: 64137
[log] SYNC_FORMS_016: Found matching local form 64137 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64137
[log] 📝 Updating form fields for formId: 64137
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64137
[log] 📝 Removing form questions from realm for formId: 64137
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64137
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64137 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64137
[log] SYNC_FORMS_015: Processing server form [1] - formId: 63894
[log] SYNC_FORMS_016: Found matching local form 63894 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63894
[log] 📝 Updating form fields for formId: 63894
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63894
[log] 📝 Removing form questions from realm for formId: 63894
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63894
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63894 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63894
[log] SYNC_FORMS_015: Processing server form [2] - formId: 64350
[log] SYNC_FORMS_016: Found matching local form 64350 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 64350
[log] 📝 Updating form fields for formId: 64350
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 64350
[log] 📝 Removing form questions from realm for formId: 64350
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 64350
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 64350 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 64350
[log] SYNC_FORMS_015: Processing server form [3] - formId: 63896
[log] SYNC_FORMS_016: Found matching local form 63896 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63896
[log] 📝 Updating form fields for formId: 63896
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63896
[log] 📝 Removing form questions from realm for formId: 63896
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63896
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63896 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63896
[log] SYNC_FORMS_015: Processing server form [4] - formId: 63897
[log] SYNC_FORMS_016: Found matching local form 63897 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63897
[log] 📝 Updating form fields for formId: 63897
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63897
[log] 📝 Removing form questions from realm for formId: 63897
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 63897
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63897 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63897
[log] SYNC_FORMS_015: Processing server form [5] - formId: 63892
[log] SYNC_FORMS_016: Found matching local form 63892 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63892
[log] 📝 Updating form fields for formId: 63892
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63892
[log] 📝 Removing form questions from realm for formId: 63892
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63892
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63892 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63892
[log] SYNC_FORMS_015: Processing server form [6] - formId: 63893
[log] SYNC_FORMS_016: Found matching local form 63893 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63893
[log] 📝 Updating form fields for formId: 63893
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63893
[log] 📝 Removing form questions from realm for formId: 63893
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 12 server questions to local form 63893
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63893 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63893
[log] SYNC_FORMS_015: Processing server form [7] - formId: 63899
[log] SYNC_FORMS_016: Found matching local form 63899 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63899
[log] 📝 Updating form fields for formId: 63899
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63899
[log] 📝 Removing form questions from realm for formId: 63899
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63899
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63899 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63899
[log] SYNC_FORMS_015: Processing server form [8] - formId: 63895
[log] SYNC_FORMS_016: Found matching local form 63895 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63895
[log] 📝 Updating form fields for formId: 63895
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63895
[log] 📝 Removing form questions from realm for formId: 63895
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 63895
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63895 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63895
[log] SYNC_FORMS_015: Processing server form [9] - formId: 63891
[log] SYNC_FORMS_016: Found matching local form 63891 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63891
[log] 📝 Updating form fields for formId: 63891
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63891
[log] 📝 Removing form questions from realm for formId: 63891
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 8 server questions to local form 63891
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63891 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63891
[log] SYNC_FORMS_015: Processing server form [10] - formId: 63898
[log] SYNC_FORMS_016: Found matching local form 63898 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 63898
[log] 📝 Updating form fields for formId: 63898
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 63898
[log] 📝 Removing form questions from realm for formId: 63898
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 0 server questions to local form 63898
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 63898 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 63898
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 19308879: 2025-07-18 11:45:00.000Z -> 2025-07-18 11:45:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 19308879:
[log] QA_DATA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 19308879 has TOTAL 0 question answers across 11 forms
[log] SYNC_FORMS_037: Completed forms update for task: 19308879
[log] SYNC_FORMS_003: [13/14] Processing task update for taskId: 14023849
[log] SYNC_FORMS_006: Found existing task 14023849 with 4 local forms
[log] QA_DATA_001: [BEFORE_UPDATE] Question answers state for task 14023849:
[log] QA_DATA_002: [BEFORE_UPDATE] Form 13284 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 58889 has 0 question answers
[log] QA_DATA_002: [BEFORE_UPDATE] Form 59973 has 0 question answers
[log] QA_DATA_004: [BEFORE_UPDATE] Task 14023849 has TOTAL 0 question answers across 4 forms
[log] SYNC_FORMS_009: Processing 4 server forms for task 14023849
[log] SYNC_FORMS_010: Creating temporary task model for server form mapping
[log] SYNC_FORMS_011: Mapped 4 server forms from TaskDetailMapper
[log] SYNC_FORMS_012: Server form [0] - formId: 13284, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [1] - formId: 58813, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [2] - formId: 58889, questionAnswers count: 0, questions count: 2
[log] SYNC_FORMS_012: Server form [3] - formId: 59973, questionAnswers count: 0, questions count: 1
[log] SYNC_FORMS_015: Processing server form [0] - formId: 13284
[log] SYNC_FORMS_016: Found matching local form 13284 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 13284
[log] 📝 Updating form fields for formId: 13284
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 13284
[log] 📝 Removing form questions from realm for formId: 13284
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 13284
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 13284 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 13284
[log] SYNC_FORMS_015: Processing server form [1] - formId: 58813
[log] SYNC_FORMS_016: Found matching local form 58813 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 58813
[log] 📝 Updating form fields for formId: 58813
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 58813
[log] 📝 Removing form questions from realm for formId: 58813
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 58813
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 58813 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 58813
[log] SYNC_FORMS_015: Processing server form [2] - formId: 58889
[log] SYNC_FORMS_016: Found matching local form 58889 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 58889
[log] 📝 Updating form fields for formId: 58889
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 58889
[log] 📝 Removing form questions from realm for formId: 58889
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 2 server questions to local form 58889
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 58889 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 58889
[log] SYNC_FORMS_015: Processing server form [3] - formId: 59973
[log] SYNC_FORMS_016: Found matching local form 59973 with 0 existing question answers
[log] SYNC_FORMS_019: Updating individual fields for existing form 59973
[log] 📝 Updating form fields for formId: 59973
[log] ✅ Successfully updated form fields
[log] SYNC_FORMS_020: Question answers count BEFORE cleanup: 0
[log] SYNC_FORMS_021: Removing existing form questions from realm for form 59973
[log] 📝 Removing form questions from realm for formId: 59973
[log] ✅ Successfully removed form questions from realm
[log] SYNC_FORMS_022: Question answers count AFTER cleanup: 0 (was: 0)
[log] SYNC_FORMS_023: Adding 1 server questions to local form 59973
[log] SYNC_FORMS_024: Question answers count AFTER adding server questions: 0
[log] SYNC_FORMS_027: INFO - Local form 59973 has no question answers (expected for new/empty forms).
[log] SYNC_FORMS_028: Updated existing form: 59973
[log] SYNC_FORMS_031: Checking for local forms to delete
[log] SYNC_FORMS_033: Deleting 0 forms not found on server
[log] SYNC_FORMS_036: Updated modifiedTimeStampForms for task 14023849: 2025-07-18 11:45:00.000Z -> 2025-07-18 11:45:00.000Z
[log] QA_DATA_001: [AFTER_UPDATE] Question answers state for task 14023849:
[log] QA_DATA_002: [AFTER_UPDATE] Form 13284 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 58889 has 0 question answers
[log] QA_DATA_002: [AFTER_UPDATE] Form 59973 has 0 question answers
[log] QA_DATA_004: [AFTER_UPDATE] Task 14023849 has TOTAL 0 question answers across 4 forms
[log] SYNC_FORMS_037: Completed forms update for task: 14023849
[log] SYNC_FORMS_038: [2025-07-19T13:11:50.990118] Completed form updates processing for all tasks
[log] 👥 Processing 16 member updates
[log] 👥 Updated members for task: 19439362
[log] 👥 Updated members for task: 14061508
[log] 👥 Updated members for task: 13840926
[log] 👥 Updated members for task: 14540516
[log] 👥 Updated members for task: 14497371
[log] 👥 Updated members for task: 14155793
[log] 👥 Updated members for task: 16172481
[log] 👥 Updated members for task: 19308875
[log] 👥 Updated members for task: 19308876
[log] 👥 Updated members for task: 19308871
[log] 👥 Updated members for task: 19308872
[log] 👥 Updated members for task: 19308873
[log] 👥 Updated members for task: 19308874
[log] 👥 Updated members for task: 19308878
[log] 👥 Updated members for task: 19308879
[log] 👥 Updated members for task: 14023849
[log] 📋 Processing 16 task updates
[log] 📋 Updated task: 19439362
[log] 📋 Updated task: 14061508
[log] 📋 Updated task: 13840926
[log] 📋 Updated task: 14540516
[log] 📋 Updated task: 14497371
[log] 📋 Updated task: 14155793
[log] 📋 Updated task: 16172481
[log] 📋 Updated task: 19308875
[log] 📋 Updated task: 19308876
[log] 📋 Updated task: 19308871
[log] 📋 Updated task: 19308872
[log] 📋 Updated task: 19308873
[log] 📋 Updated task: 19308874
[log] 📋 Updated task: 19308878
[log] 📋 Updated task: 19308879
[log] 📋 Updated task: 14023849
[log] 📸 Processing 2 photo updates
[log] 📸 Updated photos for task: 19308873
[log] 📸 Updated photos for task: 14023849
[log] SUBMISSION_001: Starting _processUpdateTasksSubmission with 10 submission updates
[log] SUBMISSION_002: Processing submission update [0] for taskId: 14061508
[log] SUBMISSION_004: Found existing task 14061508 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 14061508:
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 14061508 has TOTAL 0 question answers across 1 forms
[log] SUBMISSION_FORM_001: Processing 1 server forms for task 14061508
[log] SUBMISSION_FORM_002: Processing server form [0] with formId: 58813
[log] SUBMISSION_FORM_003: Server form 58813 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 58813 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 58813 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 58813
[log] SUBMISSION_FORM_006: Local form 58813 now has 0 question answers AFTER operation
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 14061508:
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 14061508 has TOTAL 0 question answers across 1 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 14061508
[log] SUBMISSION_002: Processing submission update [1] for taskId: 13840926
[log] SUBMISSION_004: Found existing task 13840926 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 13840926:
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 13840926 has TOTAL 0 question answers across 0 forms
[log] SUBMISSION_FORM_001: Processing 0 server forms for task 13840926
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 13840926:
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 13840926 has TOTAL 0 question answers across 0 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 13840926
[log] SUBMISSION_002: Processing submission update [2] for taskId: 14540516
[log] SUBMISSION_004: Found existing task 14540516 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 14540516:
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 14540516 has TOTAL 0 question answers across 0 forms
[log] SUBMISSION_FORM_001: Processing 0 server forms for task 14540516
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 14540516:
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 14540516 has TOTAL 0 question answers across 0 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 14540516
[log] SUBMISSION_002: Processing submission update [3] for taskId: 14497371
[log] SUBMISSION_004: Found existing task 14497371 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 14497371:
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 13284 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 59973 has 0 question answers
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 14497371 has TOTAL 0 question answers across 3 forms
[log] SUBMISSION_FORM_001: Processing 3 server forms for task 14497371
[log] SUBMISSION_FORM_002: Processing server form [0] with formId: 13284
[log] SUBMISSION_FORM_003: Server form 13284 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 13284 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 13284 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 13284
[log] SUBMISSION_FORM_006: Local form 13284 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [1] with formId: 58813
[log] SUBMISSION_FORM_003: Server form 58813 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 58813 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 58813 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 58813
[log] SUBMISSION_FORM_006: Local form 58813 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [2] with formId: 59973
[log] SUBMISSION_FORM_003: Server form 59973 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 59973 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 59973 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 59973
[log] SUBMISSION_FORM_006: Local form 59973 now has 0 question answers AFTER operation
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 14497371:
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 13284 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 59973 has 0 question answers
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 14497371 has TOTAL 0 question answers across 3 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 14497371
[log] SUBMISSION_002: Processing submission update [4] for taskId: 14155793
[log] SUBMISSION_004: Found existing task 14155793 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 14155793:
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 14155793 has TOTAL 0 question answers across 1 forms
[log] SUBMISSION_FORM_001: Processing 1 server forms for task 14155793
[log] SUBMISSION_FORM_002: Processing server form [0] with formId: 58813
[log] SUBMISSION_FORM_003: Server form 58813 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 58813 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 58813 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 58813
[log] SUBMISSION_FORM_006: Local form 58813 now has 0 question answers AFTER operation
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 14155793:
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 14155793 has TOTAL 0 question answers across 1 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 14155793
[log] SUBMISSION_002: Processing submission update [5] for taskId: 16172481
[log] SUBMISSION_004: Found existing task 16172481 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 16172481:
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 16172481 has TOTAL 0 question answers across 1 forms
[log] SUBMISSION_FORM_001: Processing 1 server forms for task 16172481
[log] SUBMISSION_FORM_002: Processing server form [0] with formId: 58813
[log] SUBMISSION_FORM_003: Server form 58813 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 58813 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 58813 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 58813
[log] SUBMISSION_FORM_006: Local form 58813 now has 0 question answers AFTER operation
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 16172481:
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 16172481 has TOTAL 0 question answers across 1 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 16172481
[log] SUBMISSION_002: Processing submission update [6] for taskId: 19308875
[log] SUBMISSION_004: Found existing task 19308875 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 19308875:
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63891 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 19308875 has TOTAL 0 question answers across 11 forms
[log] SUBMISSION_FORM_001: Processing 11 server forms for task 19308875
[log] SUBMISSION_FORM_002: Processing server form [0] with formId: 64137
[log] SUBMISSION_FORM_003: Server form 64137 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 64137 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 64137 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 64137
[log] SUBMISSION_FORM_006: Local form 64137 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [1] with formId: 63894
[log] SUBMISSION_FORM_003: Server form 63894 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63894 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63894 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63894
[log] SUBMISSION_FORM_006: Local form 63894 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [2] with formId: 64350
[log] SUBMISSION_FORM_003: Server form 64350 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 64350 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 64350 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 64350
[log] SUBMISSION_FORM_006: Local form 64350 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [3] with formId: 63896
[log] SUBMISSION_FORM_003: Server form 63896 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63896 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63896 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63896
[log] SUBMISSION_FORM_006: Local form 63896 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [4] with formId: 63897
[log] SUBMISSION_FORM_003: Server form 63897 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63897 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63897 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63897
[log] SUBMISSION_FORM_006: Local form 63897 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [5] with formId: 63892
[log] SUBMISSION_FORM_003: Server form 63892 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63892 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63892 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63892
[log] SUBMISSION_FORM_006: Local form 63892 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [6] with formId: 63893
[log] SUBMISSION_FORM_003: Server form 63893 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63893 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63893 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63893
[log] SUBMISSION_FORM_006: Local form 63893 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [7] with formId: 63899
[log] SUBMISSION_FORM_003: Server form 63899 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63899 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63899 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63899
[log] SUBMISSION_FORM_006: Local form 63899 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [8] with formId: 63895
[log] SUBMISSION_FORM_003: Server form 63895 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63895 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63895 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63895
[log] SUBMISSION_FORM_006: Local form 63895 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [9] with formId: 63891
[log] SUBMISSION_FORM_003: Server form 63891 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63891 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63891 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63891
[log] SUBMISSION_FORM_006: Local form 63891 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [10] with formId: 63898
[log] SUBMISSION_FORM_003: Server form 63898 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63898 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63898 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63898
[log] SUBMISSION_FORM_006: Local form 63898 now has 0 question answers AFTER operation
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 19308875:
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 19308875 has TOTAL 0 question answers across 11 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 19308875
[log] SUBMISSION_002: Processing submission update [7] for taskId: 19308873
[log] SUBMISSION_004: Found existing task 19308873 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 19308873:
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63891 has 1 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 19308873 has TOTAL 1 question answers across 11 forms
[log] SUBMISSION_FORM_001: Processing 11 server forms for task 19308873
[log] SUBMISSION_FORM_002: Processing server form [0] with formId: 64137
[log] SUBMISSION_FORM_003: Server form 64137 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 64137 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 64137 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 64137
[log] SUBMISSION_FORM_006: Local form 64137 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [1] with formId: 63894
[log] SUBMISSION_FORM_003: Server form 63894 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63894 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63894 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63894
[log] SUBMISSION_FORM_006: Local form 63894 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [2] with formId: 64350
[log] SUBMISSION_FORM_003: Server form 64350 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 64350 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 64350 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 64350
[log] SUBMISSION_FORM_006: Local form 64350 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [3] with formId: 63896
[log] SUBMISSION_FORM_003: Server form 63896 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63896 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63896 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63896
[log] SUBMISSION_FORM_006: Local form 63896 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [4] with formId: 63897
[log] SUBMISSION_FORM_003: Server form 63897 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63897 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63897 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63897
[log] SUBMISSION_FORM_006: Local form 63897 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [5] with formId: 63892
[log] SUBMISSION_FORM_003: Server form 63892 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63892 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63892 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63892
[log] SUBMISSION_FORM_006: Local form 63892 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [6] with formId: 63893
[log] SUBMISSION_FORM_003: Server form 63893 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63893 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63893 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63893
[log] SUBMISSION_FORM_006: Local form 63893 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [7] with formId: 63899
[log] SUBMISSION_FORM_003: Server form 63899 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63899 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63899 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63899
[log] SUBMISSION_FORM_006: Local form 63899 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [8] with formId: 63895
[log] SUBMISSION_FORM_003: Server form 63895 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63895 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63895 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63895
[log] SUBMISSION_FORM_006: Local form 63895 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [9] with formId: 63891
[log] SUBMISSION_FORM_003: Server form 63891 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63891 with 1 question answers BEFORE clear operation
[log] SUBMISSION_OP_002: Local form 63891 question answers BEFORE clear:
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63891 (was 1 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63891
[log] SUBMISSION_FORM_006: Local form 63891 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [10] with formId: 63898
[log] SUBMISSION_FORM_003: Server form 63898 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63898 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63898 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63898
[log] SUBMISSION_FORM_006: Local form 63898 now has 0 question answers AFTER operation
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 19308873:
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 19308873 has TOTAL 0 question answers across 11 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 19308873
[log] SUBMISSION_002: Processing submission update [8] for taskId: 19308878
[log] SUBMISSION_004: Found existing task 19308878 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 19308878:
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 64137 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63894 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 64350 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63896 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63897 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63892 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63893 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63899 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63895 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63891 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 63898 has 0 question answers
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 19308878 has TOTAL 0 question answers across 11 forms
[log] SUBMISSION_FORM_001: Processing 11 server forms for task 19308878
[log] SUBMISSION_FORM_002: Processing server form [0] with formId: 64137
[log] SUBMISSION_FORM_003: Server form 64137 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 64137 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 64137 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 64137
[log] SUBMISSION_FORM_006: Local form 64137 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [1] with formId: 63894
[log] SUBMISSION_FORM_003: Server form 63894 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63894 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63894 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63894
[log] SUBMISSION_FORM_006: Local form 63894 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [2] with formId: 64350
[log] SUBMISSION_FORM_003: Server form 64350 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 64350 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 64350 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 64350
[log] SUBMISSION_FORM_006: Local form 64350 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [3] with formId: 63896
[log] SUBMISSION_FORM_003: Server form 63896 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63896 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63896 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63896
[log] SUBMISSION_FORM_006: Local form 63896 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [4] with formId: 63897
[log] SUBMISSION_FORM_003: Server form 63897 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63897 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63897 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63897
[log] SUBMISSION_FORM_006: Local form 63897 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [5] with formId: 63892
[log] SUBMISSION_FORM_003: Server form 63892 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63892 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63892 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63892
[log] SUBMISSION_FORM_006: Local form 63892 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [6] with formId: 63893
[log] SUBMISSION_FORM_003: Server form 63893 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63893 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63893 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63893
[log] SUBMISSION_FORM_006: Local form 63893 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [7] with formId: 63899
[log] SUBMISSION_FORM_003: Server form 63899 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63899 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63899 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63899
[log] SUBMISSION_FORM_006: Local form 63899 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [8] with formId: 63895
[log] SUBMISSION_FORM_003: Server form 63895 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63895 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63895 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63895
[log] SUBMISSION_FORM_006: Local form 63895 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [9] with formId: 63891
[log] SUBMISSION_FORM_003: Server form 63891 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63891 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63891 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63891
[log] SUBMISSION_FORM_006: Local form 63891 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [10] with formId: 63898
[log] SUBMISSION_FORM_003: Server form 63898 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 63898 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 63898 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 63898
[log] SUBMISSION_FORM_006: Local form 63898 now has 0 question answers AFTER operation
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 19308878:
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 64137 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63894 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 64350 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63896 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63897 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63892 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63893 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63899 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63895 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63891 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 63898 has 0 question answers
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 19308878 has TOTAL 0 question answers across 11 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 19308878
[log] SUBMISSION_002: Processing submission update [9] for taskId: 14023849
[log] SUBMISSION_004: Found existing task 14023849 for submission update
[log] SUBMISSION_QA_001: [BEFORE_UPDATE] Question answers state for task 14023849:
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 13284 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 58889 has 0 question answers
[log] SUBMISSION_QA_002: [BEFORE_UPDATE] Form 59973 has 0 question answers
[log] SUBMISSION_QA_004: [BEFORE_UPDATE] Task 14023849 has TOTAL 0 question answers across 4 forms
[log] SUBMISSION_FORM_001: Processing 4 server forms for task 14023849
[log] SUBMISSION_FORM_002: Processing server form [0] with formId: 13284
[log] SUBMISSION_FORM_003: Server form 13284 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 13284 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 13284 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 13284
[log] SUBMISSION_FORM_006: Local form 13284 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [1] with formId: 58813
[log] SUBMISSION_FORM_003: Server form 58813 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 58813 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 58813 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 58813
[log] SUBMISSION_FORM_006: Local form 58813 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [2] with formId: 58889
[log] SUBMISSION_FORM_003: Server form 58889 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 58889 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 58889 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 58889
[log] SUBMISSION_FORM_006: Local form 58889 now has 0 question answers AFTER operation
[log] SUBMISSION_FORM_002: Processing server form [3] with formId: 59973
[log] SUBMISSION_FORM_003: Server form 59973 has 0 question answers
[log] SUBMISSION_OP_001: Found matching local form 59973 with 0 question answers BEFORE clear operation
[log] SUBMISSION_OP_004: CRITICAL - Cleared all question answers from local form 59973 (was 0 answers)
[log] SUBMISSION_FORM_005: Added 0 server question answers to local form 59973
[log] SUBMISSION_FORM_006: Local form 59973 now has 0 question answers AFTER operation
[log] SUBMISSION_QA_001: [AFTER_UPDATE] Question answers state for task 14023849:
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 13284 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 58813 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 58889 has 0 question answers
[log] SUBMISSION_QA_002: [AFTER_UPDATE] Form 59973 has 0 question answers
[log] SUBMISSION_QA_004: [AFTER_UPDATE] Task 14023849 has TOTAL 0 question answers across 4 forms
[log] SUBMISSION_005: Successfully completed submission update for task: 14023849
[log] ✍️ Processing 16 signature updates
[log] ✍️ Updated signatures for task: 19439362
[log] ✍️ Updated signatures for task: 14061508
[log] ✍️ Updated signatures for task: 13840926
[log] ✍️ Updated signatures for task: 14540516
[log] ✍️ Updated signatures for task: 14497371
[log] ✍️ Updated signatures for task: 14155793
[log] ✍️ Updated signatures for task: 16172481
[log] ✍️ Updated signatures for task: 19308875
[log] ✍️ Updated signatures for task: 19308876
[log] ✍️ Updated signatures for task: 19308871
[log] ✍️ Updated signatures for task: 19308872
[log] ✍️ Updated signatures for task: 19308873
[log] ✍️ Updated signatures for task: 19308874
[log] ✍️ Updated signatures for task: 19308878
[log] ✍️ Updated signatures for task: 19308879
[log] ✍️ Updated signatures for task: 14023849
[log] 💰 Processing 10 tasks for budget calculation
[log] budget form completed: false
[log] Budget has not changed: 0 -> 0
[log] 💰 Budget calculated for task: 14061508
[log] Budget has not changed: 0 -> 0
[log] 💰 Budget calculated for task: 13840926
[log] Budget has not changed: 0 -> 0
[log] 💰 Budget calculated for task: 14540516
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] Budget has not changed: 10 -> 10
[log] 💰 Budget calculated for task: 14497371
[log] budget form completed: false
[log] Budget has not changed: 0 -> 0
[log] 💰 Budget calculated for task: 14155793
[log] budget form completed: false
[log] Budget has not changed: 45 -> 45
[log] 💰 Budget calculated for task: 16172481
[log] budget form completed: false
[log] budget offset type 2: 10
[log] budget offset type 1: -5
[log] budget offset type 1: -2
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] Budget has not changed: 5 -> 5
[log] 💰 Budget calculated for task: 19308875
[log] budget form completed: false
[log] budget offset type 2: 10
[log] budget offset type 1: -5
[log] budget offset type 1: -2
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] Budget has not changed: 5 -> 5
[log] 💰 Budget calculated for task: 19308873
[log] budget form completed: false
[log] budget offset type 2: 10
[log] budget offset type 1: -5
[log] budget offset type 1: -2
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] Budget has not changed: 10 -> 10
[log] 💰 Budget calculated for task: 19308878
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] budget form completed: false
[log] Budget has not changed: 0 -> 0
[log] 💰 Budget calculated for task: 14023849
[log] ✅ TasksResponse processing completed successfully
[log] Calendar data available: true
[log] Calendar info count: 183
[log] Calendar info 0: timestamp=2015-04-02 18:30:00.000Z, dollarSymbol=false, budgetAmount=0.0
[log] Calendar info 1: timestamp=2015-04-03 18:30:00.000Z, dollarSymbol=false, budgetAmount=0.0
[log] Calendar info 2: timestamp=2015-04-04 18:30:00.000Z, dollarSymbol=false, budgetAmount=0.0
[log] Calendar info 3: timestamp=2015-04-05 18:30:00.000Z, dollarSymbol=false, budgetAmount=0.0
[log] Calendar info 4: timestamp=2015-04-24 18:30:00.000Z, dollarSymbol=false, budgetAmount=0.0
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 16172481, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 16172481, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 19308875, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 19308875, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 19308876, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 19308876, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 19308871, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 19308871, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 19308872, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 19308872, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] QA_DATA_003: [BEFORE_UPDATE] Form 63891 QA[0] - questionId: 599123, questionpartId: 1271188, measurementId: 1807461, measurementTextResult: "22", measurementOptionId: null, measurementOptionIds: "null", isComment: false
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 19308873, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 19308873, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_018: Local QA BEFORE [0] - questionId: 599123, measurementId: 1807461, measurementTextResult: 22, measurementOptionId: null
[log] SYNC_FORMS_026: Local QA FINAL [0] - questionId: 599123, measurementId: 1807461, measurementTextResult: 22, measurementOptionId: null
[log] QA_DATA_003: [AFTER_UPDATE] Form 63891 QA[0] - questionId: 599123, questionpartId: 1271188, measurementId: 1807461, measurementTextResult: "22", measurementOptionId: null, measurementOptionIds: "null", isComment: false
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 19308874, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 19308874, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 19308878, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 19308878, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 19308879, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 19308879, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] SYNC_FORMS_007: Form counters BEFORE update - taskId: 14023849, ctFormsTotalCnt: 0, ctFormsCompletedCnt: 0, kTotal: null, kCompleted: null
[log] SYNC_FORMS_008: Form counters AFTER update - taskId: 14023849, ctFormsTotalCnt: 0 -> 0, ctFormsCompletedCnt: 0 -> 0, kTotal: null -> null, kCompleted: null -> null
[log] ❌ Error removing deleted photos from Realm: RealmException: The Realm is already in a write transaction. Error code: realm_errno.RLM_ERR_WRONG_TRANSACTION_STATE.
[log] SUBMISSION_QA_003: [BEFORE_UPDATE] Form 63891 QA[0] - questionId: 599123, questionpartId: 1271188, measurementId: 1807461, measurementTextResult: "22", measurementOptionId: null, measurementOptionIds: "null", questionPartMultiId: "null"
[log] SUBMISSION_OP_003: Local QA[0] - questionId: 599123, questionpartId: 1271188, measurementId: 1807461, measurementTextResult: "22"
[log] SUBMISSION_FORM_007: ⚠️ CRITICAL DATA LOSS WARNING - Form 63891 had 1 question answers but now has 0! Server provided no question answers.
[log] ❌ Error removing deleted signatures from Realm: RealmException: The Realm is already in a write transaction. Error code: realm_errno.RLM_ERR_WRONG_TRANSACTION_STATE.
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Request ║ GET
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/ot_counter2?latitude=8.5179116&longitude=77.0275301&user_id=64366&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83PPc
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758): ╔ Headers
I/flutter ( 9758): ╟ Content-Type: application/json
I/flutter ( 9758): ╟ contentType: application/json
I/flutter ( 9758): ╟ responseType: ResponseType.json
I/flutter ( 9758): ╟ followRedirects: true
I/flutter ( 9758): ╟ connectTimeout: 0:00:20.000000
I/flutter ( 9758): ╟ receiveTimeout: 0:00:20.000000
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
I/flutter ( 9758):
I/flutter ( 9758): ╔╣ Response ║ GET ║ Status: 200 OK  ║ Time: 926 ms
I/flutter ( 9758): ║  https://webservice2.storetrack.com.au/api/ot_counter2?latitude=8.5179116&longitude=77.0275301&user_id=64366&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.UpbXsapw2AYwXB9sq48bKasUGDaXK1btV_hnYW83PPc
I/flutter ( 9758): ╚══════════════════════════════════════════════════════════════════════════════════════════╝
