import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/pos_response_item_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

/// Result class for POS status calculations
class PosStatusResult {
  final int posReceivedCount;
  final int totalPosItems;
  final bool isCompleted;
  final bool hasAnyReceived;

  PosStatusResult({
    required this.posReceivedCount,
    required this.totalPosItems,
    required this.isCompleted,
    required this.hasAnyReceived,
  });

  /// Creates a PosStatusResult with zero values (for error cases or no data)
  factory PosStatusResult.empty() {
    return PosStatusResult(
      posReceivedCount: 0,
      totalPosItems: 0,
      isCompleted: false,
      hasAnyReceived: false,
    );
  }
}

/// Utility class for POS status calculations
/// Eliminates code duplication between pos_page.dart and pos_received_view.dart
class PosStatusUtils {
  // Private constructor to prevent instantiation
  PosStatusUtils._();

  /// Calculates POS status for a given task by checking the database
  /// Returns PosStatusResult with counts and status flags
  static PosStatusResult calculatePosStatus(TaskDetail task) {
    try {
      final realm = RealmDatabase.instance.realm;

      // Get POS response items from database for this task
      final posResponseItems = realm.query<PosResponseItemModel>(
        'taskId == ${task.taskId}',
      );

      // Check cached data first, then fall back to task.posReceived
      bool hasAnyReceived = false;
      if (posResponseItems.isNotEmpty) {
        // Use cached data if available (case-insensitive comparison)
        hasAnyReceived = posResponseItems
            .any((item) => item.received?.toLowerCase() == "true");
      } else {
        // Fall back to original task.posReceived field
        final posReceivedStr = task.posReceived?.toString().toLowerCase();
        hasAnyReceived = (posReceivedStr == 'true' || posReceivedStr == '1');
      }

      final totalPosItems = task.posItems?.length ?? 0;
      final posReceivedCount = hasAnyReceived ? totalPosItems : 0;
      final isCompleted =
          totalPosItems == posReceivedCount && totalPosItems > 0;

      return PosStatusResult(
        posReceivedCount: posReceivedCount,
        totalPosItems: totalPosItems,
        isCompleted: isCompleted,
        hasAnyReceived: hasAnyReceived,
      );
    } catch (e) {
      // Fallback to original logic if database read fails
      return _calculatePosStatusFallback(task);
    }
  }

  /// Fallback method using the original task.posReceived field
  /// Used when database query fails
  static PosStatusResult _calculatePosStatusFallback(TaskDetail task) {
    final totalPosItems = task.posItems?.length ?? 0;
    final posReceivedStr = task.posReceived?.toString().toLowerCase();

    bool hasAnyReceived = false;
    if (posReceivedStr == 'True' || posReceivedStr == '1') {
      hasAnyReceived = true;
    }

    final posReceivedCount = hasAnyReceived ? totalPosItems : 0;
    final isCompleted = totalPosItems == posReceivedCount && totalPosItems > 0;

    return PosStatusResult(
      posReceivedCount: posReceivedCount,
      totalPosItems: totalPosItems,
      isCompleted: isCompleted,
      hasAnyReceived: hasAnyReceived,
    );
  }

  /// Formats the POS status for display
  /// Returns a string like "2 of 5 received"
  static String formatPosStatus(PosStatusResult result) {
    return '${result.posReceivedCount} of ${result.totalPosItems} received';
  }

  /// Formats the POS status for display with custom text
  /// Returns a string like "2 of 5 delivered"
  static String formatPosStatusWithText(
      PosStatusResult result, String actionText) {
    return '${result.posReceivedCount} of ${result.totalPosItems} $actionText';
  }
}
