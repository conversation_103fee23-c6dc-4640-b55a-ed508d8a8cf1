import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

/// A [DateTime] implementation that is always in the 'Australia/Sydney' timezone.
///
/// This class can be used as a drop-in replacement for [DateTime] objects,
/// ensuring that all time-related operations are consistently handled in
/// Sydney time, regardless of the device's local timezone.
class DateTimeNSW implements DateTime {
  // The internal, timezone-aware DateTime object.
  final tz.TZDateTime _dateTime;

  // Private constructor for internal use by factories.
  DateTimeNSW._internal(this._dateTime);

  /// Creates a [DateTimeNSW] object representing the current moment in Sydney.
  factory DateTimeNSW.now() {
    _initialize();
    final location = tz.getLocation('Australia/Sydney');
    return DateTimeNSW._internal(tz.TZDateTime.now(location));
  }

  /// Creates a [DateTimeNSW] object with the specified date and time components
  /// in the Sydney timezone.
  factory DateTimeNSW(
    int year, [
    int month = 1,
    int day = 1,
    int hour = 0,
    int minute = 0,
    int second = 0,
    int millisecond = 0,
    int microsecond = 0,
  ]) {
    _initialize();
    final location = tz.getLocation('Australia/Sydney');
    return DateTimeNSW._internal(tz.TZDateTime(
      location,
      year,
      month,
      day,
      hour,
      minute,
      second,
      millisecond,
      microsecond,
    ));
  }

  /// Creates a [DateTimeNSW] object from the given milliseconds since epoch.
  /// The [isUtc] parameter is handled, and the final time is converted to
  /// Sydney time.
  factory DateTimeNSW.fromMillisecondsSinceEpoch(int millisecondsSinceEpoch,
      {bool isUtc = false}) {
    _initialize();
    final location = tz.getLocation('Australia/Sydney');
    final dateTime = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch,
        isUtc: isUtc);
    return DateTimeNSW._internal(tz.TZDateTime.from(dateTime, location));
  }

  /// Creates a [DateTimeNSW] object from the given microseconds since epoch.
  /// The [isUtc] parameter is handled, and the final time is converted to
  /// Sydney time.
  factory DateTimeNSW.fromMicrosecondsSinceEpoch(int microsecondsSinceEpoch,
      {bool isUtc = false}) {
    _initialize();
    final location = tz.getLocation('Australia/Sydney');
    final dateTime = DateTime.fromMicrosecondsSinceEpoch(microsecondsSinceEpoch,
        isUtc: isUtc);
    return DateTimeNSW._internal(tz.TZDateTime.from(dateTime, location));
  }

  /// Parses a formatted string and creates a [DateTimeNSW] object.
  /// The parsed [DateTime] is converted to Sydney time.
  factory DateTimeNSW.parse(String formattedString) {
    final dateTime = DateTime.parse(formattedString);
    _initialize();
    final location = tz.getLocation('Australia/Sydney');
    return DateTimeNSW._internal(tz.TZDateTime.from(dateTime, location));
  }

  /// Tries to parse a formatted string. Returns a [DateTimeNSW] object if
  /// successful, otherwise returns `null`.
  static DateTimeNSW? tryParse(String formattedString) {
    final dateTime = DateTime.tryParse(formattedString);
    if (dateTime == null) return null;
    _initialize();
    final location = tz.getLocation('Australia/Sydney');
    return DateTimeNSW._internal(tz.TZDateTime.from(dateTime, location));
  }

  // --- Timezone Initialization ---
  static bool _isInitialized = false;
  static void _initialize() {
    if (!_isInitialized) {
      tz.initializeTimeZones();
      _isInitialized = true;
    }
  }

  // --- DateTime Interface Implementation ---

  @override
  bool get isUtc => false;

  @override
  String get timeZoneName => _dateTime.timeZoneName;

  @override
  Duration get timeZoneOffset => _dateTime.timeZoneOffset;

  @override
  int get year => _dateTime.year;

  @override
  int get month => _dateTime.month;

  @override
  int get day => _dateTime.day;

  @override
  int get hour => _dateTime.hour;

  @override
  int get minute => _dateTime.minute;

  @override
  int get second => _dateTime.second;

  @override
  int get millisecond => _dateTime.millisecond;

  @override
  int get microsecond => _dateTime.microsecond;

  @override
  int get weekday => _dateTime.weekday;

  @override
  int get millisecondsSinceEpoch => _dateTime.millisecondsSinceEpoch;

  @override
  int get microsecondsSinceEpoch => _dateTime.microsecondsSinceEpoch;

  @override
  bool isAfter(DateTime other) => _dateTime.isAfter(other);

  @override
  bool isBefore(DateTime other) => _dateTime.isBefore(other);

  @override
  bool isAtSameMomentAs(DateTime other) => _dateTime.isAtSameMomentAs(other);

  @override
  int compareTo(DateTime other) => _dateTime.compareTo(other);

  @override
  DateTime add(Duration duration) => _dateTime.add(duration);

  @override
  DateTime subtract(Duration duration) => _dateTime.subtract(duration);

  @override
  Duration difference(DateTime other) {
    if (other is DateTimeNSW) {
      return _dateTime.difference(other._dateTime);
    }
    return _dateTime.difference(other);
  }

  @override
  DateTime toUtc() => _dateTime.toUtc();

  @override
  DateTime toLocal() => _dateTime.toLocal();

  @override
  String toIso8601String() => _dateTime.toIso8601String();

  @override
  String toString() => _dateTime.toString();

  @override
  int get hashCode => _dateTime.hashCode;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! DateTime) return false;
    return isAtSameMomentAs(other);
  }
}
