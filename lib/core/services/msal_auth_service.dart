import 'dart:io';
import 'package:msal_auth/msal_auth.dart';
import '../../core/utils/logger.dart';

abstract class MsalAuthService {
  Future<void> init();
  Future<AuthenticationResult?> signIn({required String loginHint});
  Future<void> signOut();
  bool get isInitialized;
}

class MsalAuthServiceImpl implements MsalAuthService {
  SingleAccountPca? _msalAuth;

  @override
  bool get isInitialized => _msalAuth != null;

  @override
  Future<void> init() async {
    try {
      const clientIdAndroid = "b39f9c3c-adf9-49d4-acee-2323b747f30e";
      const clientIdIos = "b69e600b-8182-4764-8d30-d998b1a7e024";
      const androidRedirectUri = "msauth://com.au.storetrack/callback";

      _msalAuth = await SingleAccountPca.create(
        clientId: Platform.isIOS ? clientIdIos : clientIdAndroid,
        androidConfig: AndroidConfig(
          configFilePath: 'assets/jsons/msal_config.json',
          redirectUri: androidRedirectUri,
        ),
        appleConfig: AppleConfig(
          authorityType: AuthorityType.aad,
          broker: Broker.webView,
        ),
      );
      logger('MSAL initialized successfully.');
    } catch (e) {
      logger('Error initializing MSAL: $e');
      rethrow;
    }
  }

  @override
  Future<AuthenticationResult?> signIn({required String loginHint}) async {
    if (_msalAuth == null) {
      throw Exception('MSAL not initialized. Call init() first.');
    }

    try {
      final AuthenticationResult authResult = await _msalAuth!.acquireToken(
        scopes: <String>['User.Read'],
        prompt: Prompt.selectAccount,
        loginHint: loginHint,
      );

      logger('Authentication successful:');
      logger('Access Token: ${authResult.accessToken}');
      logger('Account Username: ${authResult.account.username}');
      logger('Expires On: ${authResult.expiresOn}');

      return authResult;
    } on MsalException catch (e) {
      logger('MSAL Authentication failed: ${e.message}');
      rethrow;
    } catch (e) {
      logger('An unexpected error occurred during authentication: $e');
      rethrow;
    }
  }

  @override
  Future<void> signOut() async {
    if (_msalAuth == null) {
      logger('MSAL not initialized, skipping sign out.');
      return;
    }

    try {
      await _msalAuth!.signOut();
      logger('MSAL sign out successful.');
    } catch (e) {
      logger('Error during MSAL sign out: $e');
      // Don't rethrow as sign out failure shouldn't block logout process
    }
  }
}
