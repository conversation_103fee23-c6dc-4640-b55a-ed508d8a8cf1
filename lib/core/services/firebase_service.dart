import 'dart:math';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../../firebase_options.dart';
import '../utils/logger.dart';

class FirebaseService {
  static FirebaseMessaging? _messaging;
  static final FlutterLocalNotificationsPlugin _localNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static Future<void> setupApp() async {
    try {
      await Firebase.initializeApp(
        name: 'storetrack',
        options: DefaultFirebaseOptions.currentPlatform,
      );
      logger('✅ Firebase initialized successfully');

      _messaging = _getFirebaseMessaging();

      if (_messaging != null) {
        FirebaseMessaging.onBackgroundMessage(handleBackgroundMessage);
      }
    } catch (e) {
      logger('❌ Firebase initialization failed: $e');
    }
  }

  static FirebaseMessaging? _getFirebaseMessaging() {
    try {
      if (Firebase.apps.isNotEmpty) {
        return FirebaseMessaging.instance;
      }
    } catch (e) {
      logger('Firebase not available for messaging: $e');
    }
    return null;
  }

  static Future<void> initialize(BuildContext context) async {
    if (_messaging == null) {
      logger(
          'Firebase messaging not available, skipping push notification setup');
      return;
    }

    try {
      _printFcmToken();
      await requestPushPermissions();
      await _initLocalNotifications();
      await _initListeners(context);
    } catch (e) {
      logger('Error initializing push notifications: $e');
    }
  }

  static Future<bool> requestPushPermissions() async {
    if (_messaging == null) return false;

    try {
      NotificationSettings settings = await _messaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
      logger('User granted permission: ${settings.authorizationStatus}');
      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      logger('Error requesting push permissions: $e');
      return false;
    }
  }

  static Future<void> _initLocalNotifications() async {
    try {
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
      );
      await _localNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          // Handle notification tap
        },
      );
    } catch (e) {
      logger('Error initializing local notifications: $e');
    }
  }

  static Future<void> _initListeners(BuildContext context) async {
    if (_messaging == null) return;

    try {
      await _messaging!.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      FirebaseMessaging.onMessageOpenedApp.listen((event) {
        // Handle navigation or state update
      });

      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        try {
          if (message.notification != null) {
            createPush(
              title: message.notification?.title ?? "StoreTrack",
              body:
                  message.notification?.body ?? "You have a new notification!",
            );
          }
        } catch (e) {
          logger('Error handling foreground message: $e');
        }
      });
    } catch (e) {
      logger('Error setting up message listeners: $e');
    }
  }

  static Future<String?> getToken() async {
    if (_messaging == null) return null;

    try {
      String? token = await _messaging!.getToken();
      logger('FCM Token: ${token ?? "No token"}');
      return token;
    } catch (e) {
      logger('Error getting FCM token: $e');
      return null;
    }
  }

  static void createPush({required String title, required String body}) {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'storetrack',
        'storetrack',
        channelDescription: 'Default channel for notifications',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
      );
      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(android: androidPlatformChannelSpecifics);
      _localNotificationsPlugin.show(
        Random().nextInt(100000),
        title,
        body,
        platformChannelSpecifics,
      );
    } catch (e) {
      logger('Error showing local notification: $e');
    }
  }

  @pragma('vm:entry-point')
  static Future<void> handleBackgroundMessage(RemoteMessage message) async {
    try {
      logger('🔑 [Background] Received message: ${message.messageId}');
      // Note: flutter_local_notifications cannot show notifications in background isolate directly.
      // Optionally, handle data or schedule a local notification via platform channels if needed.
    } catch (e) {
      logger('Error handling background message: $e');
    }
  }

  static void _printFcmToken() {
    final token = getToken();
    logger('FCM Token: ${token.toString()}');
  }
}
