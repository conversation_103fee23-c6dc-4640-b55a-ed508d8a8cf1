package com.au.storetrack.Fragments.Task;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import com.au.storetrack.Activities.ActivityBase;
import com.au.storetrack.Adapters.SpinnerAdapter;
import com.au.storetrack.Fragments.Base.FragmentBase;
import com.au.storetrack.Fragments.Tab.MainScheduleFragment;
import com.au.storetrack.Fragments.Tab.MainTodayFragment;
import com.au.storetrack.Model.CustomModel.TimeCalculation;
import com.au.storetrack.Model.DataModel.FormModel;
import com.au.storetrack.Model.DataModel.PhotoFolderModel;
import com.au.storetrack.Model.DataModel.PhotoModel;
import com.au.storetrack.Model.DataModel.TaskDetailModel;
import com.au.storetrack.Model.DataModel.UserProfileModel;
import com.au.storetrack.Model.EventModel.PermissionEvent;
import com.au.storetrack.R;
import com.au.storetrack.Utilities.CommonFunction;
import com.au.storetrack.Utilities.Constant;
import com.au.storetrack.Utilities.ControlParser;
import com.au.storetrack.Utilities.Database.DatabaseManager;
import com.au.storetrack.Utilities.Database.DatabaseRunnable;
import com.au.storetrack.Utilities.Device.LocationTracker;
import com.au.storetrack.Utilities.Network.API;
import com.au.storetrack.Utilities.Storage.StorageManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by Joshua on 28/04/2016.
 */
public class TaskCompleteFragment extends FragmentBase {
    public static final String TASK_COMPLETE_TASK_DURATION_VALUE = "TASK_COMPLETE_TASK_DURATION_VALUE";

    private TaskDetailModel taskDetailModel = null;
    private int taskDurationInMinutes = 0;

    private TextView taskScheduledDateValue_textView;
    private Spinner taskScheduledState_spinner;
    private ImageView taskScheduledStateIndicator_imageView;
    private LinearLayout taskCommentContainer_linearLayout;
    private TextView taskCommentValue_textView;
    private EditText taskHourDurationValue_editText;
    private EditText taskMinuteDurationValue_editText;
    private TextView taskBudgetValue_textView;
    private EditText taskClaimableKMSValue_editText;
    private EditText taskPrintedPagesValue_editText;

    private int taskDuration = 0;
    private double taskSubmissionLatitude = 0;
    private double taskSubmissionLongitude = 0;
    private boolean nonMPTTaskPhotosValidated;   //true/false does not matter for initial value
                                                        //mo, 14/8/18 renamed from taskPhotosValidated.

    private int taskTotalFormsCount = 0;
    private boolean taskFormsEdited = false;        //mo, has form blurred by user?
    private boolean taskFormsValidated = false;     //mo, has user finished all the forms mandatory?

    private LinearLayout timeEditBox_linearLayout;
    private UserProfileModel userProfileModel;
    String userOrgsID;
    private LinearLayout budget1_linearLayout;

    String strMinutes;
    String strHour;
    Integer hour;





    //has all budget offset setting
    //private ArrayList<BudgetOffset> totalBudgetOffsetStructure = new ArrayList<>();

//    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        EventBus.getDefault().register(this);

        View v = inflater.inflate(R.layout.task_complete, container, false);

        Bundle receiveBundle = getArguments();

        if (receiveBundle.containsKey(TaskDetailModel.TASK_ID_KEY)) {
            String taskID = receiveBundle.getString(TaskDetailModel.TASK_ID_KEY);
            taskDetailModel = DatabaseManager.getInstance(getContext()).getTaskDetailModelWithTaskID(ActivityBase.mRealm, taskID, true);
        }

        if (receiveBundle.containsKey(TASK_COMPLETE_TASK_DURATION_VALUE)) {
            taskDurationInMinutes = receiveBundle.getInt(TASK_COMPLETE_TASK_DURATION_VALUE);
        }

        if (taskDetailModel == null) {

            //mo, 11/10/16, when the submitted task does not show because of cycle expiry, you end up coming here. And I don't want to show error.
            //so commented. (used to show alert dialog)

        }
        else {
            populateData();


            prepareInterfaces(v);
        }

        return v;
    }

    private void populateData() {
        userProfileModel = DatabaseManager.getInstance(getContext()).getUserProfileModelWithUserID(ActivityBase.mRealm, StorageManager.getActiveUserIDFromSharedPreference(getContext()), true);
        if(userProfileModel.getOrgIDs() != null){
            userOrgsID = userProfileModel.getOrgIDs();
        }

    }

    @Override
    public void onDestroyView() {
        try {
            CommonFunction.hideSoftKeyboard(getActivity());
            EventBus.getDefault().unregister(this);
        }
        catch (Exception e) {

        }
        super.onDestroyView();
    }

    @Subscribe
    public void onPermissionEventReceived(PermissionEvent event) {
        switch (event.getSenderActivityTag()) {
            case Constant.CHECK_LOCATION_PERMISSION_WHEN_SUBMIT_TASK_ACTIVITY_TAG:
                if (event.getPermissionResult() == PackageManager.PERMISSION_GRANTED) {
                    performTaskSubmissionWrapper();
                }

                break;

            default:
                break;
        }
    }

    @Override
    public void setNavigationBarContent() {
        navigationBar.clearAllControls();

        ImageView leftControl = new ImageView(rootActivity);
        leftControl.setImageResource(R.drawable.btn_back);
        leftControl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                popFragment();
            }
        });

        navigationBar.addLeftNavigationBarControl(leftControl);

        navigationBar.setNavigationBarTitleTextMenuPage("Complete Task");

        ImageView rightControl = new ImageView(rootActivity);
        rightControl.setImageResource(R.drawable.icon_tick);

        //save(), save clicked, tick clicked (search again! to find real tick clicked)
        rightControl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonFunction.disableUserInteraction(getActivity());

                getView().requestFocus();
                CommonFunction.hideSoftKeyboard(getActivity());

                taskDuration = 0;
                taskSubmissionLatitude = 0;
                taskSubmissionLongitude = 0;
                nonMPTTaskPhotosValidated = false;
                taskFormsValidated = false;


                CommonFunction.getInstance(getContext()).showProgressIndicator(getString(R.string.ProgressDialogCheckingTitleText));
                rootActivity.checkLocationPermission(Constant.CHECK_LOCATION_PERMISSION_WHEN_SUBMIT_TASK_ACTIVITY_TAG);




            }
        });

        navigationBar.addRightNavigationBarControl(rightControl);
    }

    //nirvik, alert if the task is sent to payroll
    public void showTaskSentToPayrollAlert() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("Cannot Edit Task!");
        builder.setMessage("This task has been completed and sent to payroll, you cannot edit task duration details. Please contact your manager if extra time is required.");


        builder.setNegativeButton(R.string.Cancel, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
            }
        });

        builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
            }
        });

        AlertDialog alertDialog = builder.create();
        alertDialog.show();

        CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
    }

    private void prepareInterfaces(View v) {
        taskScheduledDateValue_textView = ControlParser.parseControl(v, R.id.taskScheduledDateValue_textView);
        taskScheduledState_spinner = ControlParser.parseControl(v, R.id.taskScheduledState_spinner);
        taskScheduledStateIndicator_imageView = ControlParser.parseControl(v, R.id.taskScheduledStateIndicator_imageView);
        taskCommentContainer_linearLayout = ControlParser.parseControl(v, R.id.taskCommentContainer_linearLayout);
        taskCommentValue_textView = ControlParser.parseControl(v, R.id.taskCommentValue_textView);
       // taskHourDurationValue_editText = ControlParser.parseControl(v, R.id.taskHourDurationValue_editText);
        taskMinuteDurationValue_editText = ControlParser.parseControl(v, R.id.taskMinuteDurationValue_editText);
        timeEditBox_linearLayout = ControlParser.parseControl(v,R.id.timeEditBox_linearLayout);
        budget1_linearLayout = ControlParser.parseControl(v,R.id.budget1_linearLayout);

        taskBudgetValue_textView = ControlParser.parseControl(v, R.id.taskBudgetValue_textView);

        taskClaimableKMSValue_editText = ControlParser.parseControl(v, R.id.taskClaimableKMSValue_editText);
        taskPrintedPagesValue_editText = ControlParser.parseControl(v, R.id.taskPrintedPagesValue_editText);

        DateFormat dateFormat = new SimpleDateFormat(Constant.MEDIUM_DATEFORMAT_STRING, Constant.DEFAULT_LOCALE_INSTANCE);
        dateFormat.setTimeZone(Constant.DEFAULT_TIMEZONE_INSTANCE);

        if (taskDetailModel.isIs_open()) {
            //set today's date as DateSchedule
            taskScheduledDateValue_textView.setText(dateFormat.format(new Date()));
        } else {
            taskScheduledDateValue_textView.setText(dateFormat.format(taskDetailModel.getTaskScheduledDate()));
        }

        String[] taskScheduledStateSpinnerValues = new String[] {getString(R.string.SingleSelectionDropDownHintText), TaskDetailModel.TASK_UNSUCCESSFUL_STATUS_VALUE, TaskDetailModel.TASK_SUCCESSFUL_STATUS_VALUE};

        SpinnerAdapter taskScheduledState_spinnerAdapter = new SpinnerAdapter(rootActivity, android.R.layout.simple_spinner_item, 13, true, taskScheduledStateSpinnerValues);
        taskScheduledState_spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        taskScheduledState_spinner.setAdapter(taskScheduledState_spinnerAdapter);
        taskScheduledState_spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) {
                    String taskScheduledStateValue = (String) parent.getItemAtPosition(position);

                    if (taskScheduledStateValue.equalsIgnoreCase(TaskDetailModel.TASK_SUCCESSFUL_STATUS_VALUE)) {
                        taskScheduledStateIndicator_imageView.setImageResource(R.drawable.icon_successful);
                    }
                    else if (taskScheduledStateValue.equalsIgnoreCase(TaskDetailModel.TASK_UNSUCCESSFUL_STATUS_VALUE)) {
                        taskScheduledStateIndicator_imageView.setImageResource(R.drawable.icon_unsuccessful);
                    }
                }
                else {
                    taskScheduledStateIndicator_imageView.setImageDrawable(null);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        taskScheduledState_spinner.setVisibility(View.GONE);

        taskTotalFormsCount = 0;

        if (taskDetailModel.getTaskForms() != null) {
            for (FormModel formModel : taskDetailModel.getTaskForms()) {
                if (!formModel.isFormVision()) {
                    taskTotalFormsCount++;
                }
            }
        }

        if ((taskDetailModel.getTaskForms() == null) || (taskTotalFormsCount == 0)) {
            taskScheduledState_spinner.setVisibility(View.VISIBLE);
        }

        if (taskDetailModel.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_UNSCHEDULED_STATUS_VALUE)) {
            taskScheduledStateIndicator_imageView.setImageResource(R.drawable.icon_tentative);
        }
        else if (taskDetailModel.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_SCHEDULED_STATUS_VALUE)) {
            taskScheduledStateIndicator_imageView.setImageResource(R.drawable.icon_confirmed);
        }
        else if (taskDetailModel.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_SUCCESSFUL_STATUS_VALUE)) {
            taskScheduledState_spinner.setSelection(2);
            taskScheduledStateIndicator_imageView.setImageResource(R.drawable.icon_successful);
        }
        else if (taskDetailModel.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_UNSUCCESSFUL_STATUS_VALUE)) {
            taskScheduledState_spinner.setSelection(1);
            taskScheduledStateIndicator_imageView.setImageResource(R.drawable.icon_unsuccessful);
        }

        taskCommentContainer_linearLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String commentValue = taskCommentValue_textView.getText().toString();
                commentValue = commentValue.replace(getString(R.string.GeneralEditTextHintText), "");

                Bundle fragmentBundle = new Bundle();
                fragmentBundle.putString(TaskTextEditorFragment.TASK_TEXT_EDITOR_TITLE_KEY, "Schedule Comment");
                fragmentBundle.putString(TaskTextEditorFragment.TASK_TEXT_EDITOR_INITIAL_VALUE_KEY, commentValue);
                fragmentBundle.putString(TaskDetailModel.TASK_ID_KEY, taskDetailModel.getTaskID());

                replaceFragment(new TaskTextEditorFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_RIGHT_ANIMATION_KEY);
            }
        });

        taskCommentValue_textView.setText(taskDetailModel.getTaskComment());
        taskCommentValue_textView.setTextColor(Color.BLACK);

        if (CommonFunction.isEmptyStringField(taskCommentValue_textView.getText().toString())) {
            taskCommentValue_textView.setText(getString(R.string.GeneralEditTextHintText));
            taskCommentValue_textView.setTextColor(Color.GRAY);
        }

        int generatedTaskDuration = 0;
        int totalSeconds = 0;

        if ((taskDetailModel.getTaskCommencementDate() != null) && (taskDetailModel.getTaskStoppedDate() != null)) {
            totalSeconds = TimeCalculation.calculateTimeIntervalInSec(taskDetailModel);
            generatedTaskDuration = totalSeconds / 60;
        }
        else {
            generatedTaskDuration = taskDetailModel.getTaskDuration();
        }

        int hours = generatedTaskDuration / 60;
        int minutes = generatedTaskDuration % 60;
        int seconds = totalSeconds % 60;

        if (seconds>30) {
            minutes = minutes + 1;
        }

        //mo, 31/5/17, Below two values, hour and minutes seemed to be controlled by below rules.
        //(1) if timer value is there, show it.
        //(2) if not, show minutes from result (user).
      //  taskHourDurationValue_editText.setText(String.valueOf(hours));
        final int finalTime = hours*60+minutes;
        taskMinuteDurationValue_editText.setText(String.valueOf(finalTime));


        //Budget offset
        //taskBudgetValue_textView.setText(String.valueOf(taskDetailModel.getTaskBudget()));

        int newBudget = taskDetailModel.getTaskBudgetCalculated();
        taskBudgetValue_textView.setText(String.valueOf(newBudget));

        taskClaimableKMSValue_editText.setText(String.valueOf(taskDetailModel.getTaskClaimableKMS()));
        taskPrintedPagesValue_editText.setText(String.valueOf(taskDetailModel.getTaskPrintedPages()));


        if(taskDetailModel.isTaskSentToPayroll()){
            timeEditBox_linearLayout.setBackgroundResource(R.drawable.disable_cell);

            timeEditBox_linearLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showTaskSentToPayrollAlert();
                }
            });

            taskClaimableKMSValue_editText.setEnabled(false);
            taskBudgetValue_textView.setTextColor(Color.GRAY);;
            taskPrintedPagesValue_editText.setEnabled(false);
           // taskHourDurationValue_editText.setEnabled(false);
            taskMinuteDurationValue_editText.setEnabled(false);
        }else{
            timeEditBox_linearLayout.setClickable(false);
        }
    }

    //When tick button clicked, if location is available, proceed form/photos validation >> then proceed to set Successful/Unsuccessful
    //mo created, 14/8/18, used to be a code chunk inside Location permission check.
    private void performTaskSubmissionWrapper() {
        boolean isSuccessful = false;

        //all permission there, continue completing task
        taskSubmissionLatitude = 0;
        taskSubmissionLongitude = 0;

        LocationTracker locationTracker = new LocationTracker(getContext());

        if (locationTracker.canGetLocation()) {
            taskSubmissionLatitude = locationTracker.getLatitude();
            taskSubmissionLongitude = locationTracker.getLongitude();
        }

        locationTracker.stopUsingGPS();

        //-----------------
        // (1/3) Photo Validation (old forms without photo combine)
        //-----------------
        nonMPTTaskPhotosValidated = true;   //mo, 14/8/18, this is the real initialize value. if error in below, it will be changed to false.

        for (PhotoFolderModel photoFolderModel : taskDetailModel.getTaskPhotoFolders()) {
            //check for each folder
            int folderContentAmount = 0;

            for (PhotoModel photoModel : photoFolderModel.getPhotoFolderContents()) {
                if (!photoModel.isPhotoIsDeleted()) {
                    folderContentAmount++;
                }
            }

            if ((photoFolderModel.isPhotoFolderMandatory()) && (folderContentAmount == 0)) {
                //mandatory folder, but no photos uploaded
                nonMPTTaskPhotosValidated = false;
            }
        }

        //-----------------
        // (2/3) Form validation: mo, 25/5/17, taskFormsValidated is used to be set true when user finishes a form.
        // Now, this variable is set to true when all mandatory forms are finished.
        //-----------------
        taskTotalFormsCount = 0;
        taskFormsValidated = false;

        //mo created, 25/5/17, two variables to check whether user finished all mandatory forms.
        int totalMandatoryFormCount = 0;
        int totalMandatoryFormCompleteCount = 0;

        if (taskDetailModel.getTaskForms() != null) {
            for (FormModel formModel : taskDetailModel.getTaskForms()) {
                if (!formModel.isFormVision()) {

                    taskTotalFormsCount++;

                    if (formModel.isFormMandatory()) {
                        totalMandatoryFormCount++;
                    }

                    //user completed a form? For Joshua, isFormEdited() means 'form is edited/saved successfully' so it's treated as completed
                    if ((formModel.isFormEdited() || formModel.isFormCompleted())
                            && (formModel.getFormQuestionAnswers() != null) && (formModel.getFormQuestionAnswers().size() > 0)) {
                        //at least a form is finished.
                        if (formModel.isFormEdited()) {
                            taskFormsEdited = true;
                        }

                        if (formModel.isFormMandatory()) {
                            totalMandatoryFormCompleteCount++;
                        }
                    }
                }
            }
        }

        if ((taskDetailModel.getTaskForms() == null) || (taskTotalFormsCount == 0)) {
            //there is no form in this task
            taskFormsValidated = true;

        } else {
            //now, taskFormsValidated is set with true/false based on the count.
            if (totalMandatoryFormCount == totalMandatoryFormCompleteCount) {
                taskFormsValidated = true;
            }
        }

        //so far, Form/Photo validation is finished

        //-----------------
        // (3/3) Submission Error check
        //-----------------

        if (taskDetailModel.getTaskForms() == null || taskTotalFormsCount == 0) {
            if (taskScheduledState_spinner.getSelectedItemPosition() == 0) {
                //no forms, TaskStatus supposed to be manually selected but not selected yet
                CommonFunction.enableUserInteraction(getActivity());
                CommonFunction.getInstance(getContext()).hideProgressIndicator();

                AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
                builder.setTitle("Error!");
                builder.setCancelable(false);
                builder.setMessage("You have to select a correct Schedule State.");
                builder.setPositiveButton(R.string.OK, new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                    }
                });

                builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
                    }
                });

                AlertDialog alertDialog = builder.create();
                alertDialog.show();

                CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
            } else {
                //no forms, TaskStaus properly selected
                if (photoChecking()) {
                    String str = (String)taskScheduledState_spinner.getSelectedItem();
                    if (str.equals(TaskDetailModel.TASK_SUCCESSFUL_STATUS_VALUE)) {
                        isSuccessful = true;
                    } else {
                        isSuccessful = false;
                    }

                    timeChecking(isSuccessful);
                }
            }
        } else {
            //there is forms
            if (taskFormsValidated) {
                if (photoChecking()) {
                    timeChecking(true);
                }
            } else {
                if (CommonFunction.isEmptyStringField(taskDetailModel.getTaskComment())) {
                    //has forms, form not validated, no comment
                    CommonFunction.enableUserInteraction(getActivity());
                    CommonFunction.getInstance(getContext()).hideProgressIndicator();

                    AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
                    builder.setTitle("Error!");
                    builder.setCancelable(false);
                    //builder.setMessage("You have to complete at least one form or write comment to save this page.");
                    builder.setMessage("You have to complete all mandatory forms or write comment to save this page.");
                    builder.setPositiveButton(R.string.OK, new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int id) {
                        }
                    });

                    builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
                        }
                    });

                    AlertDialog alertDialog = builder.create();
                    alertDialog.show();

                    CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
                } else {
                    //this is forms, but forms not finished, comments there
                    if (photoChecking()) {
                        timeChecking(false);
                    }
                }
            }
        }
    }

    //mo created, 14/8/18
    private boolean photoChecking() {
        // photo check
        if (!nonMPTTaskPhotosValidated && !taskDetailModel.hasMPT()) {
            //old forms (photos are separate), photos not uploaded
            //mo, 12/2/18 photo checking only should be done in non-mpt form tasks (tasks that have mpt forms do not need to check photos because they are checked already in form save considering conditoning etc)
            CommonFunction.enableUserInteraction(getActivity());
            CommonFunction.getInstance(getContext()).hideProgressIndicator();

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("Error!");
            builder.setCancelable(false);
            builder.setMessage("You have to upload all expected photos in order to save this page.");
            builder.setPositiveButton(R.string.OK, new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int id) {
                }
            });

            builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
                }
            });

            AlertDialog alertDialog = builder.create();
            alertDialog.show();

            CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
            return false;
        } else {
            return true;
        }
    }

    //mo created, 14/8/18
    private void timeChecking( boolean isSuccessful) {
        int minutes;
        strMinutes = taskMinuteDurationValue_editText.getText().toString();

        final boolean[] commentRequired = {false};
        final boolean[] isHoopEmployee = {false};


//        if (!CommonFunction.isEmptyStringField(strHour)) {
//            hour = Integer.parseInt(strHour);
//        } else {
//            hour = 0;
//        }

        if (!CommonFunction.isEmptyStringField(strMinutes)) {
            minutes = Integer.parseInt(strMinutes);
        } else {
            minutes = 0;
        }

        taskDuration = minutes;
        String warningMessage = "";

        if (taskDuration == 0 && taskDetailModel.getTaskBudgetCalculated()>0) {
            warningMessage = "You entered 0 working hours/mins so you will not be paid for this task.";
        }
        else if (taskDuration > taskDetailModel.getTaskBudgetCalculated()) {
            if(userOrgsID != null){
                boolean userFallsUnder6or1 = userOrgsID.contains("1") || userOrgsID.contains("6");
                if( userFallsUnder6or1 && taskCommentValue_textView.getText().toString().equalsIgnoreCase("Enter a value...")){
                    warningMessage = "Please mention the reason for going over budget in schedule comment section.";
                    commentRequired[0] = true;
                }
                else
                    if(userOrgsID.contains("8")){
                    warningMessage = "Reporting over budget is forbidden.";
                    isHoopEmployee[0] = true;
                }
            }
          else{
                warningMessage = "Your claimed time exceeds the budgeted time for this visit.";
            }

        }

        if ((!taskFormsValidated) && (!CommonFunction.isEmptyStringField(taskDetailModel.getTaskComment()))) {

            if (CommonFunction.isEmptyStringField(warningMessage)) {
                warningMessage = "If you press OK, the schedule will be marked as unsuccessful. Press cancel if you wish to continue working on this schedule.";
            }
            else {
                warningMessage = "1. " + warningMessage + "\n\n";
                warningMessage += "2. If you press OK, the schedule will be marked as unsuccessful. Press cancel if you wish to continue working on this schedule.";
            }
        }

            if (!CommonFunction.isEmptyStringField(warningMessage)) {
                //show some over budget warning >> then perform sync
                CommonFunction.enableUserInteraction(getActivity());
                CommonFunction.getInstance(getContext()).hideProgressIndicator();

                AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
                builder.setTitle("Warning!");
                builder.setCancelable(false);
                builder.setMessage(warningMessage);

                final boolean[] finalCommentRequired = {commentRequired[0]};
                final boolean[] finalIsHoopEmployee = {isHoopEmployee[0]};
                builder.setPositiveButton(R.string.OK, new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                        //some warning, but clicked ok >> perform sync
                    if(!finalCommentRequired[0] && !finalIsHoopEmployee[0]){
                        performTaskSubmission(isSuccessful);
                    }else{
                        commentRequired[0] = false;
                        isHoopEmployee[0] = false;
                        CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
                    }

                    }
                });

                builder.setNegativeButton(R.string.Cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {

                    }
                });

                builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
                    }
                });

                AlertDialog alertDialog = builder.create();
                alertDialog.show();

                CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
            }
            else {
                //all good, no warning >> perform sync
                performTaskSubmission(isSuccessful);
            }


    }



    //mo, tick clicked
    //mo, 13/8/18, Joshua is wrong.
    // - The process is not as complete as iOS. You have to change the whole process according to PhotoMandatory diagram.
    // - How do I check MPT Task (no need photo validation) or Non-MPT Task (photo validation is necessary)?
    //mo, 7/5/19, you come here when forms validated >> photos validated >> timeChecking()

    private void performTaskSubmission(final boolean isSuccessful) {
        DatabaseManager.getInstance(getContext()).processRealmDataUpdate(ActivityBase.mRealm,
                new DatabaseRunnable(null) {
                    @Override
                    public void run() {

                        // TaskStatus
                        if (isSuccessful) {
                            taskDetailModel.setTaskStatus(TaskDetailModel.TASK_SUCCESSFUL_STATUS_VALUE);
                        } else {
                            taskDetailModel.setTaskStatus(TaskDetailModel.TASK_UNSUCCESSFUL_STATUS_VALUE);
                        }

                        // Wapi3 Submission Status
                        if (taskDetailModel.isTaskCompleted()) {
                            if (taskFormsEdited) {
                                taskDetailModel.setTaskSubmissionState(TaskDetailModel.TASK_SUBMISSION_STATE_RESUBMIT_CHANGE);
                            }
                            else {
                                taskDetailModel.setTaskSubmissionState(TaskDetailModel.TASK_SUBMISSION_STATE_RESUBMIT_NO_CHANGE);
                            }
                        }
                        else {
                            taskDetailModel.setTaskSubmissionState(TaskDetailModel.TASK_SUBMISSION_STATE_FIRST_SUBMIT);
                        }

                        taskDetailModel.setTaskDuration(taskDuration);
                        taskDetailModel.setTaskClaimableKMS(Integer.parseInt(taskClaimableKMSValue_editText.getText().toString()));
                        taskDetailModel.setTaskPrintedPages(Integer.parseInt(taskPrintedPagesValue_editText.getText().toString()));
                        taskDetailModel.setTaskSubmissionLatitude(taskSubmissionLatitude);
                        taskDetailModel.setTaskSubmissionLongitude(taskSubmissionLongitude);

                        taskDetailModel.setTaskSubmissionDate(Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime());

                        if (taskDetailModel.isIs_open()) {
                            //set dateschedule
                            Date today = CommonFunction.getTodayDate();
                            taskDetailModel.setTaskScheduledDate(today);
                        }

                        //mo, this task is checked as sync pending and will be filtered to be submitted later into 'taskDetailModelsToBeUploaded'.
                        taskDetailModel.setTaskSyncPending(true);
                    }
                });

        CommonFunction.enableUserInteraction(getActivity());
        CommonFunction.getInstance(getContext()).hideProgressIndicator();

        if (API.getInstance(getContext()).hasNetworkConnection()) {
            //sync fired! >> where do you go when successful? >> you go to MainScheduleFragemnt.java, onCreateView()
            rootActivity.mainContainerFragment.performSynchronisation();
        }
        else {
            // Pop twice to go back to the task list
//            rootActivity.currentFragment.popFragment();
//            rootActivity.currentFragment.popFragment();
            replaceFragment(new MainTodayFragment(), FragmentBase.SLIDE_IN_FROM_RIGHT_ANIMATION_KEY);
//            replaceFragment(new MainTodayFragment(), null);

            // Showing no connection error
            showTaskLastUpdateInformation(false);
            API.getInstance(rootActivity.currentFragment.getContext()).showNoConnectionErrorMessage();
        }
    }
}
