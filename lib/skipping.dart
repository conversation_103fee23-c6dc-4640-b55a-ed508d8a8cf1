import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart'
    show SubmitReportRequestEntity;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

skipping() {
  final List<String> skippedQuestionsConditions = [];
  final List<String> allQuestionAnswerIDs = [];
  final List<SkippedQuestionIDs> skippedQPMeasurementIDs = [];

  var task = TaskDetail();
  var formModel = task.forms![0];

  final formAnswers = formModel.questionAnswers;
  if (formAnswers != null && formAnswers.isNotEmpty) {
    for (final answer in formAnswers) {
      final questionModel = formModel.questions
          ?.firstWhere((q) => q.questionId == answer.questionId);
      allQuestionAnswerIDs.add(answer.questionId?.toString() ?? '');

      if (questionModel != null) {
        if (questionModel.isComment == false) {
          if (questionModel.measurements != null) {
            for (final measurement in questionModel.measurements!) {
              if (answer.measurementId == measurement.measurementId) {
                if (answer.measurementOptionId != null) {
                  final isInvisible = answer.measurementOptionId == -2 &&
                      answer.measurementTextResult == '-' &&
                      answer.measurementOptionIds != null;

                  if (isInvisible) {
                    final skippedID = SkippedQuestionIDs(
                      answer.questionId?.toString() ?? '',
                      answer.questionpartId?.toString() ?? '',
                      answer.measurementId?.toString() ?? '',
                    );

                    if (!skippedQPMeasurementIDs.contains(skippedID)) {
                      skippedQPMeasurementIDs.add(skippedID);
                    }
                  }
                }
              }
            }
          }
        } else {
          final isCommentInvisible = answer.commentTypeId == -2 &&
              answer.measurementTextResult == '-' &&
              answer.measurementOptionIds != null;

          if (isCommentInvisible &&
              !skippedQuestionsConditions
                  .contains(questionModel.questionId?.toString() ?? '')) {
            skippedQuestionsConditions
                .add(questionModel.questionId?.toString() ?? '');
          }
        }
      }
    }
  }

  // Post-processing skipped measurement-based questions
  for (final qModel in formModel.questions!) {
    int count = 0;

    if (qModel.isComment == false) {
      for (final skippedID in skippedQPMeasurementIDs) {
        if (skippedID.questionID == qModel.questionId?.toString()) {
          count++;
        }
      }

      final totalExpected = (qModel.measurements?.length ?? 0) *
          (qModel.questionParts?.length ?? 0);

      if (count == totalExpected &&
          !skippedQuestionsConditions
              .contains(qModel.questionId?.toString() ?? '')) {
        skippedQuestionsConditions.add(qModel.questionId?.toString() ?? '');
      }
    }

    // Evaluate question conditions (e.g., disappear logic)
    if (qModel.questionConditions != null) {
      for (final condition in qModel.questionConditions!) {
        final targetID = condition.actionQuestionId?.toString() ?? '';
        final targetAction = condition.action;

        if (targetAction == 'disappear' &&
            !skippedQuestionsConditions.contains(targetID) &&
            !allQuestionAnswerIDs.contains(targetID)) {
          skippedQuestionsConditions.add(targetID);
        }
      }
    }
  }

  for (final s in skippedQuestionsConditions) {
    logger("Skipped Question ID: $s");
  }

  // Persist skipped question IDs
  var skippingList = skippedQuestionsConditions;
  return skippingList;
}

class SkippedQuestionIDs {
  String questionID;
  String questionPartID;
  String measurementID;

  SkippedQuestionIDs(this.questionID, this.questionPartID, this.measurementID);
}

bool isQuestionSkipped(String questionID, List<String> skippingList) {
  if (skippingList.contains(questionID)) {
    return true;
  } else {
    bool isSupplementaryMulti = false;

    isSupplementaryMulti = isSupplementaryMultiQuestion(Question());
    if (isSupplementaryMulti) {
      return !isSupplementaryMultiQuestionPositiveAnswered(Question());
    }
    return false;
  }
}

bool isSupplementaryMultiQuestion(Question questionModel) {
  return questionModel.multiMeasurementId != null &&
      questionModel.multiMeasurementId.toString() != "0" &&
      questionModel.multiMeasurementId.toString().isNotEmpty;
}

isSupplementaryMultiQuestionPositiveAnswered(Question questionModel) {
  var isSupplementMultiAnswered = false;

  // Check if the question answer exists and has a positive result
  final questionAnswer = Form().questionAnswers?.firstWhere(
        (qa) => qa.measurementId == questionModel.multiMeasurementId,
        orElse: () => QuestionAnswer(),
      );

  if (questionAnswer?.measurementTextResult != null &&
      questionAnswer?.measurementTextResult != '-' &&
      int.tryParse(questionAnswer!.measurementTextResult ?? '') != null) {
    final supplementaryAnswer =
        int.parse(questionAnswer.measurementTextResult!);
    if (supplementaryAnswer > 0) {
      isSupplementMultiAnswered = true;
    }
  }

  return isSupplementMultiAnswered;
}

getSupplementaryMultiAnswer(Question questionModel) {
  var supplementaryAnswer = 0;

  // Check if the question answer exists and has a positive result
  final questionAnswer = Form().questionAnswers?.firstWhere(
        (qa) => qa.measurementId == questionModel.multiMeasurementId,
        orElse: () => QuestionAnswer(),
      );

  if (questionAnswer?.measurementTextResult != null &&
      questionAnswer?.measurementTextResult != '-' &&
      int.tryParse(questionAnswer!.measurementTextResult ?? '') != null) {
    supplementaryAnswer = int.parse(questionAnswer.measurementTextResult!);
  }

  return supplementaryAnswer;
}

isQuestionAnswered(String questionID, List<String> skippingList) {
  var count = 0;
  var questionModel = Question();
  var questionParts = questionModel.questionParts ?? [];
  for (var part in questionParts) {
    if (questionModel.isMulti == true) {
    } else {
      if (!isQuestionPartMultiAnswered()) {
        count++;
      }
    }
  }
}

isQuestionPartMultiAnswered() {}

isQuestionMandatory() {
  var isMandatory = false;
  var questionModel = Question();
  if (questionModel.isMulti == false && questionModel.isComment == false) {
    for (var measurement in questionModel.measurements!) {
      if (measurement.measurementValidations != null) {
        for (var validation in measurement.measurementValidations!) {
          if (validation.required == true) {
            isMandatory = true;
            break;
          }
        }
      }
    }
  } else {
    if (questionModel.isMulti == true) {
      bool isSupplementaryQuestion =
          isSupplementaryMultiQuestion(questionModel);
    }
  }
}

checkQuestionPartMultipartExpanded() {
  int oneOption = 0;
  var questionModel = Question();
  for (var questionPart in questionModel.questionParts!) {
    // if (questionPart) {
    //   oneOption++;
    // }
  }
}

//  private void uploadTasksData() {
//         DateFormat dateFormat = new SimpleDateFormat(Constant.SERVER_TASK_DATEFORMAT_STRING, Constant.DEFAULT_LOCALE_INSTANCE);
//         dateFormat.setTimeZone(Constant.DEFAULT_TIMEZONE_INSTANCE);

//         final int[] numberOfUploads = {taskDetailModelsToBeUploaded.size()};

//         String requestURL = API.PRODUCTION_URL + "submit_report_v4_11";

//         for (final TaskDetailModel taskDetailModel : taskDetailModelsToBeUploaded) {
//             String str = String.format("taskID: %s, taskStatus: %s, Completed: %s", taskDetailModel.getTaskID(),
//                     taskDetailModel.getTaskStatus(), taskDetailModel.isTaskCompleted());
//             CommonFunction.print(str);

//             HashMap<String, Object> requestBodyMap = new HashMap<>();

//             requestBodyMap.put("token", StorageManager.getFCMdeviceTokenFromSharedPreference(mContext));
//             requestBodyMap.put("user_id", StorageManager.getActiveUserIDFromSharedPreference(mContext));
//             requestBodyMap.put("device_uid", deviceUID);

//             try {
//                 PackageInfo pInfo = mContext.getPackageManager().getPackageInfo(mContext.getPackageName(), 0);
//                 System.out.println("APP VERSION GUS : " + pInfo.versionName);
//                 requestBodyMap.put("appversion", pInfo.versionName);
//             } catch (Exception e) {
//             }

//             requestBodyMap.put("task_id", taskDetailModel.getTaskID());
//             requestBodyMap.put("comment", taskDetailModel.getTaskComment());
//             requestBodyMap.put("minutes", taskDetailModel.getTaskDuration());
//             requestBodyMap.put("timer_minutes", TimeCalculation.calculateTimeIntervalInMin(taskDetailModel));

//             requestBodyMap.put("claimable_kms", taskDetailModel.getTaskClaimableKMS());
//             requestBodyMap.put("pages", taskDetailModel.getTaskPrintedPages());
//             requestBodyMap.put("task_status", taskDetailModel.getTaskStatus());
//             requestBodyMap.put("submission_state", taskDetailModel.getTaskSubmissionState());

//             requestBodyMap.put("task_commencement_time_stamp", (taskDetailModel.getTaskCommencementDate() != null ? dateFormat.format(taskDetailModel.getTaskCommencementDate()) : Constant.SERVER_DEFAULT_DATETIME_VALUE));
//             requestBodyMap.put("task_stopped_time_stamp", (taskDetailModel.getTaskStoppedDate() != null ? dateFormat.format(taskDetailModel.getTaskStoppedDate()) : Constant.SERVER_DEFAULT_DATETIME_VALUE));

//             requestBodyMap.put("scheduled_time_stamp", dateFormat.format(taskDetailModel.getTaskScheduledDate()));
//             requestBodyMap.put("submission_time_stamp", dateFormat.format(taskDetailModel.getTaskSubmissionDate()));

//             double startLatitude = 0;
//             double startLongitude = 0;

//             double submissionLatitude = 0;
//             double submissionLongitude = 0;

//             if ((taskDetailModel.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_SUCCESSFUL_STATUS_VALUE)) ||
//                     (taskDetailModel.getTaskStatus().equalsIgnoreCase(TaskDetailModel.TASK_UNSUCCESSFUL_STATUS_VALUE))) {
//                 startLatitude = (taskDetailModel.getTaskStartLatitude() != 0 ? taskDetailModel.getTaskStartLatitude() : taskSubmissionLatitude);
//                 startLongitude = (taskDetailModel.getTaskStartLongitude() != 0 ? taskDetailModel.getTaskStartLongitude() : taskSubmissionLongitude);

//                 submissionLatitude = (taskDetailModel.getTaskSubmissionLatitude() != 0 ? taskDetailModel.getTaskSubmissionLatitude() : taskSubmissionLatitude);
//                 submissionLongitude = (taskDetailModel.getTaskSubmissionLongitude() != 0 ? taskDetailModel.getTaskSubmissionLongitude() : taskSubmissionLongitude);
//             }

//             requestBodyMap.put("start_task_latitude", startLatitude);
//             requestBodyMap.put("start_task_longitude", startLongitude);

//             requestBodyMap.put("task_latitude", submissionLatitude);
//             requestBodyMap.put("task_longitude", submissionLongitude);

//             //form start --
//             ArrayList<Object> formsList = new ArrayList<>();

//             for (FormModel formModel : taskDetailModel.getTaskForms()) {
//                 if (formModel.isFormEdited()) {
//                     HashMap<String, Object> formDict = new HashMap<>();
//                     ArrayList<Object> formAnswersList = new ArrayList<>();

//                     formDict.put("form_id", formModel.getFormID());

//                     for (QuestionAnswerModel questionAnswerModel : formModel.getFormQuestionAnswers()) {
//                         if ((!questionAnswerModel.isQuestionAnswerIsComment()) ||
//                                 ((questionAnswerModel.isQuestionAnswerIsComment()) && (!CommonFunction.isEmptyStringField(questionAnswerModel.getQuestionAnswerMeasurementTextResult())))) {
//                             HashMap<String, Object> formAnswerDict = new HashMap<>();

//                             formAnswerDict.put("question_id", (questionAnswerModel.getQuestionAnswerQuestionID() != null ? questionAnswerModel.getQuestionAnswerQuestionID() : ""));
//                             formAnswerDict.put("questionpart_id", (questionAnswerModel.getQuestionAnswerQuestionPartID() != null ? questionAnswerModel.getQuestionAnswerQuestionPartID() : ""));
//                             formAnswerDict.put("question_part_multi_id", (questionAnswerModel.getQuestionAnswerQuestionPartMultiID() != null ? questionAnswerModel.getQuestionAnswerQuestionPartMultiID() : questionAnswerModel.getQuestionAnswerQuestionPartID()));

//                             formAnswerDict.put("measurement_id", (questionAnswerModel.getQuestionAnswerMeasurementID() != null ? questionAnswerModel.getQuestionAnswerMeasurementID() : ""));
//                             formAnswerDict.put("measurement_type_id", (questionAnswerModel.getQuestionAnswerMeasurementTypeID() != null ? questionAnswerModel.getQuestionAnswerMeasurementTypeID() : ""));
//                             formAnswerDict.put("measurement_option_id", (questionAnswerModel.getQuestionAnswerMeasurementOptionID() != null ? questionAnswerModel.getQuestionAnswerMeasurementOptionID() : ""));
//                             formAnswerDict.put("measurement_option_ids", (questionAnswerModel.getQuestionAnswerMeasurementOptionIDs() != null ? questionAnswerModel.getQuestionAnswerMeasurementOptionIDs() : ""));
//                             formAnswerDict.put("measurement_text_result", (questionAnswerModel.getQuestionAnswerMeasurementTextResult() != null ? questionAnswerModel.getQuestionAnswerMeasurementTextResult() : ""));
//                             formAnswerDict.put("is_comment", String.valueOf(questionAnswerModel.isQuestionAnswerIsComment()));
//                             formAnswerDict.put("comment_type_id", (questionAnswerModel.getQuestionAnswerCommentTypeID() != null ? questionAnswerModel.getQuestionAnswerCommentTypeID() : ""));

//                             formAnswersList.add(new JSONObject(formAnswerDict));
//                         }
//                     }

//                     formDict.put("question_answers", new JSONArray(formAnswersList));

//                     formsList.add(new JSONObject(formDict));
//                 }
//             }

//             requestBodyMap.put("forms", new JSONArray(formsList));
//             //form end

//             //mo, 28/11/16, followup tasks start ------------
//             ArrayList<Object> ftkList = new ArrayList<>();
//             for (FollowupTaskModel ftkModel : taskDetailModel.getFollowupTasks()) {
//                 HashMap<String, Object> ftkDict = new HashMap<>();

//                 ftkDict.put("task_id", taskDetailModel.getTaskID());
//                 ftkDict.put("visit_date", dateFormat.format(ftkModel.getSelected_visit_date()));
//                 ftkDict.put("followup_type_id", (ftkModel.getSelected_followup_type_id() != 0 ? ftkModel.getSelected_followup_type_id() : 0));
//                 ftkDict.put("followup_item_id", (ftkModel.getSelected_followup_item_id() != 0 ? ftkModel.getSelected_followup_item_id() : 0));
//                 ftkDict.put("budget", (ftkModel.getSelected_budget() != 0 ? ftkModel.getSelected_budget() : 0));
//                 ftkDict.put("schedule_note", (ftkModel.getSelected_schedule_note() != null ? ftkModel.getSelected_schedule_note() : ""));
//                 ftkDict.put("followup_number", (ftkModel.getFollowup_number() != 0 ? ftkModel.getFollowup_number() : 0));

//                 ftkList.add(new JSONObject(ftkDict));
//             }
//             requestBodyMap.put("followup_tasks", new JSONArray(ftkList));
//             //----------------followup task end

//             //mo, 24/5/17, resume pause start ------------
//             ArrayList<Object> resumePauseList = new ArrayList<>();
//             for (ResumePauseItemModel resumePauseItemModel : taskDetailModel.getResumePauseItems()) {

//                 HashMap<String, Object> resumePauseDict = new HashMap<>();
//                 resumePauseDict.put("resume_order_id", resumePauseItemModel.getResumeOrderID());
//                 //mo, note that resume or pause time can be null depending on first item or last item.
//                 resumePauseDict.put("resume_timestamp", (resumePauseItemModel.getResumeDate() != null ? dateFormat.format(resumePauseItemModel.getResumeDate()) : Constant.SERVER_DEFAULT_DATETIME_VALUE));
//                 resumePauseDict.put("pause_timestamp", (resumePauseItemModel.getPauseDate() != null ? dateFormat.format(resumePauseItemModel.getPauseDate()) : Constant.SERVER_DEFAULT_DATETIME_VALUE));

//                 resumePauseList.add(new JSONObject(resumePauseDict));
//             }
//             requestBodyMap.put("resume_pause_items", new JSONArray(resumePauseList));
//             //----------------resume pause end

//             requestBodyMap.put("budget_calculated", taskDetailModel.getTaskBudgetCalculated());

//             String jsonString = requestBodyMap.toString();
//             CommonFunction.print(jsonString);

//             HeaderJsonObjectRequest requestObject = new HeaderJsonObjectRequest(Request.Method.POST, requestURL, new JSONObject(requestBodyMap),
//                     new Response.Listener<JSONObject>() {
//                         @Override
//                         public void onResponse(JSONObject responseObject) {
//                             CommonFunction.print("Inner API success response: " + responseObject.toString());

//                             //----------------------------------------
//                             //mo, submit_report api ran successfully. >> show sync success message
//                             //----------------------------------------
//                             numberOfUploads[0]--;

//                             JSONObject dataObject = responseObject.optJSONObject("data");
//                             if (dataObject != null) {
//                                 //-----------------
//                                 //mo, mark success.
//                                 //-----------------
//                             //    CommonFunction.print("SYNCERROR 5 :C " + taskIdFail + " : " + responseObject.toString());
//                                 DatabaseManager.getInstance(mContext).updateTaskFailToSyncStatus(mRealm, taskDetailModel.getTaskID(), false, "");

//                                 //mo created, check if task is unaccepted. (not an error but needs to increase taskIDsFailedToBeAccepted)
//                                 try {
//                                     String unaccepted_reason_id = dataObject.optString("unaccepted_reason_id");
//                                     if (!unaccepted_reason_id.equals("0")) {
//                                         if ((taskIDsFailedToBeAccepted != null) && (taskDetailModel != null) && (!CommonFunction.isEmptyStringField(taskDetailModel.getTaskID()))) {
//                                             taskIDsFailedToBeAccepted.add(taskDetailModel.getTaskID());
//                                         }
//                                     }
//                                 } catch (Exception e) {
//                                     e.printStackTrace();
//                                 }
//                             }

//                             if (numberOfUploads[0] == 0) {
//                                 //all tasks' submit report is finished.
//                                 if (CommonFunction.isActiveUserExist(mContext)) {
//                                     //mo, this part needs to be copied to tasks_optimise ending part.

//                                     // The last task successfully submitted. Is all Sync Write good? >> Then continue Sync Read, otherwise stop.
//                                     if ((taskIDsFailedToBeSynced != null) && (taskIDsFailedToBeSynced.size() > 0)) {
//                                         String responseValue = API_DOWNLOAD_TASK_GENERAL_ERROR_RESPONSE_VALUE;

//                                         responseValue = composeTaskFailedToBeScheduledMessageString(responseValue);
//                                         responseValue = composeTaskFailedToBeSyncedMessageString(responseValue);
//                                         responseValue = composeTaskFailedToBeAcceptedMessageString(responseValue);

//                                         clearTaskUploadRelatedData();
//                                         DatabaseManager.getInstance(mContext).clearTaskFailedSchedules(ActivityBase.mRealm, taskDetailModel);
//                                         CommonFunction.print("SYNC ERROR : 12 :C " + taskIdFail + " : " + responseValue);
//                                         sendAPIResponseEvent(API_DOWNLOAD_TASKS_REQUEST_TAG, API_FAILED_RESPONSE_TYPE, responseValue);

//                                     } else {
//                                         //mo, even though you have taskIDsFailedToBeAccepted>0, you continue to Sync Read.
//                                         //clearTaskUploadRelatedData();
//                                         continueSyncRead();
//                                     }
//                                 }

//                                 //mo, this should now be called in finishSyncRead()
//                                 //clearTaskUploadRelatedData();
//                             }
//                         }
//                     },
//                     new Response.ErrorListener() {
//                         @Override
//                         public void onErrorResponse(VolleyError error) {
//                             CommonFunction.print("Inner API error response: " + error.toString());

//                             numberOfUploads[0]--;

//                             if (error.networkResponse != null) {

//                                 if (((taskIDsFailedToBeSynced != null) && (taskDetailModel != null) && (!CommonFunction.isEmptyStringField(taskDetailModel.getTaskID())))) {
//                                     taskIDsFailedToBeSynced.add(taskDetailModel.getTaskID());
//                                 }

//                                 String errorMessage = getServerErrorResponse(error);
//                                 //-----------------
//                                 //mo, mark fail (unsuccessful)
//                                 //-----------------
//                                 CommonFunction.print("SYNCERROR 6 :C " + taskDetailModel.getTaskID() + " : " + taskDetailModel.getTaskScheduledDate() + " : " + " : " + errorMessage);

//                                 DatabaseManager.getInstance(mContext).updateTaskFailToSyncStatus(mRealm, taskDetailModel.getTaskID(), true, errorMessage);

//                                 if (numberOfUploads[0] == 0) {
//                                     //mo, The last task finished submit report but failed. >> show sync finish error message. have to consider unaccepted as well.

//                                     String responseValue = getServerErrorResponse(error);
//                                     if (CommonFunction.isEmptyStringField(responseValue)) {
//                                         responseValue = API_DOWNLOAD_TASK_GENERAL_ERROR_RESPONSE_VALUE;
//                                     }

//                                     responseValue = composeTaskFailedToBeScheduledMessageString(responseValue);
//                                     responseValue = composeTaskFailedToBeSyncedMessageString(responseValue);
//                                     responseValue = composeTaskFailedToBeAcceptedMessageString(responseValue);

//                                     if (taskDetailModel.getTaskID() != null) {
//                                         CommonFunction.print("SYNCERROR 6 :C2 " + taskDetailModel.getTaskID() + " : " + taskDetailModel.getTaskID());
//                                     }

//                                     DatabaseManager.getInstance(mContext).clearTaskFailedSchedules((ActivityBase.mRealm), taskDetailModel);
//                                     clearTaskUploadRelatedData();
//                                     CommonFunction.print("SYNC ERROR : 13 :C " + taskIdFail + " : " + responseValue);
//                                     sendAPIResponseEvent(API_DOWNLOAD_TASKS_REQUEST_TAG, API_FAILED_RESPONSE_TYPE, responseValue);
//                                 } else {
//                                     //mo, even though you have taskIDsFailedToBeAccepted>0, you continue to Sync Read.
//                                     //clearTaskUploadRelatedData();
//                                     continueSyncRead();
//                                 }
//                             }
//                         }
//                     });

//             requestObject.setTag(API_UPLOAD_TASK_REQUEST_TAG);  //mo what is this for?

//             //add submit report api request into the queue.
//             if ((hasNetworkConnection()) && (CommonFunction.isActiveUserExist(mContext))) {
//                 addToRequestQueue(requestObject);
//             } else {
//                 //mo, on the very moment of submission, network was down >> add to sync fail.
//                 numberOfUploads[0]--;
//                 CommonFunction.print("SYNCERROR 11 :C " + taskDetailModel.getTaskID() + " : " + taskDetailModel.getTaskScheduledDate() + " : " + " : " + API_DOWNLOAD_TASK_NETWORK_ERROR_RESPONSE_VALUE);
//                 DatabaseManager.getInstance(mContext).updateTaskFailToSyncStatus(mRealm, taskDetailModel.getTaskID(), true, API_DOWNLOAD_TASK_NETWORK_ERROR_RESPONSE_VALUE);

//                 if ((taskIDsFailedToBeSynced != null) && (taskDetailModel != null) && (!CommonFunction.isEmptyStringField(taskDetailModel.getTaskID()))) {
//                     taskIDsFailedToBeSynced.add(taskDetailModel.getTaskID());
//                 }
//             }
//         }//for loop end

//         //mo, Nothing to upload to server. It seems to be just an error check code.
//         //If there was nothing to upload, you shouldn't come to this entire func. That's why Joshua returns API FAIL message below.

//         if (numberOfUploads[0] == 0) {

//             String responseValue = API_DOWNLOAD_TASK_GENERAL_ERROR_RESPONSE_VALUE;
//             CommonFunction.print("SYNC ERROR : 7 :C " + taskIdFail + " : " + responseValue);

//             responseValue = composeTaskFailedToBeScheduledMessageString(responseValue);
//             responseValue = composeTaskFailedToBeSyncedMessageString(responseValue);
//             responseValue = composeTaskFailedToBeAcceptedMessageString(responseValue);

//             clearTaskUploadRelatedData();

//             sendAPIResponseEvent(API_DOWNLOAD_TASKS_REQUEST_TAG, API_FAILED_RESPONSE_TYPE, responseValue);

//         }

//     }

uploadTasksData() async {
  const String actualAppVersion = AppConstants.appVersion;
  var dataManager = sl<DataManager>();
  final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
  List<TaskDetail> taskDetailModelsToBeUploaded = [];
  for (var taskDetailModel in taskDetailModelsToBeUploaded) {
    SubmitReportRequestEntity request = SubmitReportRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = await dataManager.getUserId();
    request.deviceUid = actualDeviceUid;
    request.appversion = actualAppVersion;
    request.taskId = taskDetailModel.taskId.toString();
    request.comment = taskDetailModel.comment;
    request.minutes = taskDetailModel.minutes?.toInt();
    request.timerMinutes = 0;
    request.claimableKms = taskDetailModel.claimableKms?.toInt();
    request.pages = taskDetailModel.pages?.toInt();
    request.taskStatus = taskDetailModel.taskStatus;
    request.submissionState = -1;
    request.taskCommencementTimeStamp =
        taskDetailModel.taskCommencementTimeStamp;
    request.taskStoppedTimeStamp = taskDetailModel.taskStoppedTimeStamp;
    request.scheduledTimeStamp = taskDetailModel.scheduledTimeStamp;
    request.submissionTimeStamp = DateTime.now();
    request.startTaskLatitude = taskDetailModel.taskLatitude?.toInt();
    request.startTaskLongitude = taskDetailModel.taskLongitude?.toInt();
    request.taskLatitude = taskDetailModel.latitude?.toInt();
    request.taskLongitude = taskDetailModel.longitude?.toInt();
    for (var form in taskDetailModel.forms!) {
      var form1 = Form();
      form1.formId = form.formId;
      for (var questionAnswer in form.questionAnswers!) {
        if ((questionAnswer.isComment == false) ||
            (questionAnswer.isComment == true &&
                questionAnswer.measurementTextResult != null)) {
          var questionAnswer1 = QuestionAnswer(
            questionId: questionAnswer.questionId,
            questionpartId: questionAnswer.questionpartId,
            questionPartMultiId: questionAnswer.questionPartMultiId,
            measurementId: questionAnswer.measurementId,
            measurementTypeId: questionAnswer.measurementTypeId,
            measurementOptionId: questionAnswer.measurementOptionId,
            measurementOptionIds: questionAnswer.measurementOptionIds,
            measurementTextResult: questionAnswer.measurementTextResult,
            isComment: questionAnswer.isComment,
            commentTypeId: questionAnswer.commentTypeId,
          );
          form1.questionAnswers?.add(questionAnswer1);
          request.forms?.add(form1);
        }
      }
    }
    // followup tasks
    // resume pause items
    request.followupTasks = [];
    request.resumePauseItems = [];
    request.budgetCalculated = 0;
  }
}
