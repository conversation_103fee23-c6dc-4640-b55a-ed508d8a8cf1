// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i55;
import 'package:flutter/material.dart' as _i56;
import 'package:storetrack_app/features/auth/presentation/pages/login_page.dart'
    as _i23;
import 'package:storetrack_app/features/auth/presentation/pages/reset_password_page.dart'
    as _i37;
import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart'
    as _i58;
import 'package:storetrack_app/features/home/<USER>/models/store_contact_model.dart'
    as _i57;
import 'package:storetrack_app/features/home/<USER>/entities/profile_response_entity.dart'
    as _i59;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as _i60;
import 'package:storetrack_app/features/home/<USER>/pages/add_contact_page.dart'
    as _i1;
import 'package:storetrack_app/features/home/<USER>/pages/add_leave_page.dart'
    as _i2;
import 'package:storetrack_app/features/home/<USER>/pages/add_store_comment_page.dart'
    as _i3;
import 'package:storetrack_app/features/home/<USER>/pages/assistant_page.dart'
    as _i4;
import 'package:storetrack_app/features/home/<USER>/pages/auto_schedule_page.dart'
    as _i5;
import 'package:storetrack_app/features/home/<USER>/pages/availability_page.dart'
    as _i6;
import 'package:storetrack_app/features/home/<USER>/pages/barcode_scanner_page.dart'
    as _i7;
import 'package:storetrack_app/features/home/<USER>/pages/completed_tasks_page.dart'
    as _i8;
import 'package:storetrack_app/features/home/<USER>/pages/create_task_page.dart'
    as _i9;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_history_page.dart'
    as _i10;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_holder_page.dart'
    as _i11;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_page.dart'
    as _i12;
import 'package:storetrack_app/features/home/<USER>/pages/edit_profile_page.dart'
    as _i13;
import 'package:storetrack_app/features/home/<USER>/pages/emulate_user_page.dart'
    as _i14;
import 'package:storetrack_app/features/home/<USER>/pages/flip_page.dart'
    as _i16;
import 'package:storetrack_app/features/home/<USER>/pages/form_page.dart'
    as _i17;
import 'package:storetrack_app/features/home/<USER>/pages/fqpd_page.dart'
    as _i15;
import 'package:storetrack_app/features/home/<USER>/pages/history_page.dart'
    as _i18;
import 'package:storetrack_app/features/home/<USER>/pages/home_page.dart'
    as _i19;
import 'package:storetrack_app/features/home/<USER>/pages/induction_page.dart'
    as _i20;
import 'package:storetrack_app/features/home/<USER>/pages/journey_map_page.dart'
    as _i21;
import 'package:storetrack_app/features/home/<USER>/pages/leave_page.dart'
    as _i22;
import 'package:storetrack_app/features/home/<USER>/pages/more_holder_page.dart'
    as _i25;
import 'package:storetrack_app/features/home/<USER>/pages/more_page.dart'
    as _i26;
import 'package:storetrack_app/features/home/<USER>/pages/mpt_page.dart'
    as _i24;
import 'package:storetrack_app/features/home/<USER>/pages/notes_page.dart'
    as _i27;
import 'package:storetrack_app/features/home/<USER>/pages/notification_page.dart'
    as _i28;
import 'package:storetrack_app/features/home/<USER>/pages/open_tasks_page.dart'
    as _i29;
import 'package:storetrack_app/features/home/<USER>/pages/pos_page.dart'
    as _i30;
import 'package:storetrack_app/features/home/<USER>/pages/previous_task_form_page.dart'
    as _i31;
import 'package:storetrack_app/features/home/<USER>/pages/profile_history_page.dart'
    as _i32;
import 'package:storetrack_app/features/home/<USER>/pages/profile_holder_page.dart'
    as _i33;
import 'package:storetrack_app/features/home/<USER>/pages/profile_page.dart'
    as _i34;
import 'package:storetrack_app/features/home/<USER>/pages/qpmd_page.dart'
    as _i35;
import 'package:storetrack_app/features/home/<USER>/pages/question_page.dart'
    as _i36;
import 'package:storetrack_app/features/home/<USER>/pages/scheduled_page.dart'
    as _i38;
import 'package:storetrack_app/features/home/<USER>/pages/signature_page.dart'
    as _i39;
import 'package:storetrack_app/features/home/<USER>/pages/skills_page.dart'
    as _i40;
import 'package:storetrack_app/features/home/<USER>/pages/store_history_items_page.dart'
    as _i42;
import 'package:storetrack_app/features/home/<USER>/pages/store_history_page.dart'
    as _i43;
import 'package:storetrack_app/features/home/<USER>/pages/store_info_page.dart'
    as _i44;
import 'package:storetrack_app/features/home/<USER>/pages/sub_header_page.dart'
    as _i45;
import 'package:storetrack_app/features/home/<USER>/pages/task_details_page.dart'
    as _i46;
import 'package:storetrack_app/features/home/<USER>/pages/task_files_page.dart'
    as _i47;
import 'package:storetrack_app/features/home/<USER>/pages/todays_page.dart'
    as _i48;
import 'package:storetrack_app/features/home/<USER>/pages/unscheduled_page.dart'
    as _i50;
import 'package:storetrack_app/features/home/<USER>/pages/unscheduled_pos_tasks_page.dart'
    as _i51;
import 'package:storetrack_app/features/home/<USER>/pages/useful_links_page.dart'
    as _i52;
import 'package:storetrack_app/features/home/<USER>/pages/vacancies_page.dart'
    as _i53;
import 'package:storetrack_app/features/splash/presentation/pages/splash_page.dart'
    as _i41;
import 'package:storetrack_app/features/tutorial/presentation/pages/tutorial_page.dart'
    as _i49;
import 'package:storetrack_app/features/web_browser/presentation/pages/web_browser_page.dart'
    as _i54;

/// generated route for
/// [_i1.AddContactPage]
class AddContactRoute extends _i55.PageRouteInfo<AddContactRouteArgs> {
  AddContactRoute({
    _i56.Key? key,
    _i57.StoreContactData? contact,
    bool isEditMode = false,
    required String storeId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          AddContactRoute.name,
          args: AddContactRouteArgs(
            key: key,
            contact: contact,
            isEditMode: isEditMode,
            storeId: storeId,
          ),
          initialChildren: children,
        );

  static const String name = 'AddContactRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddContactRouteArgs>();
      return _i1.AddContactPage(
        key: args.key,
        contact: args.contact,
        isEditMode: args.isEditMode,
        storeId: args.storeId,
      );
    },
  );
}

class AddContactRouteArgs {
  const AddContactRouteArgs({
    this.key,
    this.contact,
    this.isEditMode = false,
    required this.storeId,
  });

  final _i56.Key? key;

  final _i57.StoreContactData? contact;

  final bool isEditMode;

  final String storeId;

  @override
  String toString() {
    return 'AddContactRouteArgs{key: $key, contact: $contact, isEditMode: $isEditMode, storeId: $storeId}';
  }
}

/// generated route for
/// [_i2.AddLeavePage]
class AddLeaveRoute extends _i55.PageRouteInfo<void> {
  const AddLeaveRoute({List<_i55.PageRouteInfo>? children})
      : super(
          AddLeaveRoute.name,
          initialChildren: children,
        );

  static const String name = 'AddLeaveRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i2.AddLeavePage();
    },
  );
}

/// generated route for
/// [_i3.AddStoreCommentPage]
class AddStoreCommentRoute
    extends _i55.PageRouteInfo<AddStoreCommentRouteArgs> {
  AddStoreCommentRoute({
    _i56.Key? key,
    required String taskId,
    _i58.StoreCommentData? comment,
    bool isEditMode = false,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          AddStoreCommentRoute.name,
          args: AddStoreCommentRouteArgs(
            key: key,
            taskId: taskId,
            comment: comment,
            isEditMode: isEditMode,
          ),
          initialChildren: children,
        );

  static const String name = 'AddStoreCommentRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AddStoreCommentRouteArgs>();
      return _i3.AddStoreCommentPage(
        key: args.key,
        taskId: args.taskId,
        comment: args.comment,
        isEditMode: args.isEditMode,
      );
    },
  );
}

class AddStoreCommentRouteArgs {
  const AddStoreCommentRouteArgs({
    this.key,
    required this.taskId,
    this.comment,
    this.isEditMode = false,
  });

  final _i56.Key? key;

  final String taskId;

  final _i58.StoreCommentData? comment;

  final bool isEditMode;

  @override
  String toString() {
    return 'AddStoreCommentRouteArgs{key: $key, taskId: $taskId, comment: $comment, isEditMode: $isEditMode}';
  }
}

/// generated route for
/// [_i4.AssistantPage]
class AssistantRoute extends _i55.PageRouteInfo<void> {
  const AssistantRoute({List<_i55.PageRouteInfo>? children})
      : super(
          AssistantRoute.name,
          initialChildren: children,
        );

  static const String name = 'AssistantRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i4.AssistantPage();
    },
  );
}

/// generated route for
/// [_i5.AutoSchedulePage]
class AutoScheduleRoute extends _i55.PageRouteInfo<void> {
  const AutoScheduleRoute({List<_i55.PageRouteInfo>? children})
      : super(
          AutoScheduleRoute.name,
          initialChildren: children,
        );

  static const String name = 'AutoScheduleRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i5.AutoSchedulePage();
    },
  );
}

/// generated route for
/// [_i6.AvailabilityPage]
class AvailabilityRoute extends _i55.PageRouteInfo<void> {
  const AvailabilityRoute({List<_i55.PageRouteInfo>? children})
      : super(
          AvailabilityRoute.name,
          initialChildren: children,
        );

  static const String name = 'AvailabilityRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i6.AvailabilityPage();
    },
  );
}

/// generated route for
/// [_i7.BarcodeScannerPage]
class BarcodeScannerRoute extends _i55.PageRouteInfo<void> {
  const BarcodeScannerRoute({List<_i55.PageRouteInfo>? children})
      : super(
          BarcodeScannerRoute.name,
          initialChildren: children,
        );

  static const String name = 'BarcodeScannerRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i7.BarcodeScannerPage();
    },
  );
}

/// generated route for
/// [_i8.CompletedTasksPage]
class CompletedTasksRoute extends _i55.PageRouteInfo<void> {
  const CompletedTasksRoute({List<_i55.PageRouteInfo>? children})
      : super(
          CompletedTasksRoute.name,
          initialChildren: children,
        );

  static const String name = 'CompletedTasksRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i8.CompletedTasksPage();
    },
  );
}

/// generated route for
/// [_i9.CreateTaskPage]
class CreateTaskRoute extends _i55.PageRouteInfo<void> {
  const CreateTaskRoute({List<_i55.PageRouteInfo>? children})
      : super(
          CreateTaskRoute.name,
          initialChildren: children,
        );

  static const String name = 'CreateTaskRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i9.CreateTaskPage();
    },
  );
}

/// generated route for
/// [_i10.DashboardHistoryPage]
class DashboardHistoryRoute extends _i55.PageRouteInfo<void> {
  const DashboardHistoryRoute({List<_i55.PageRouteInfo>? children})
      : super(
          DashboardHistoryRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardHistoryRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i10.DashboardHistoryPage();
    },
  );
}

/// generated route for
/// [_i11.DashboardHolderPage]
class DashboardHolderRoute extends _i55.PageRouteInfo<void> {
  const DashboardHolderRoute({List<_i55.PageRouteInfo>? children})
      : super(
          DashboardHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardHolderRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i11.DashboardHolderPage();
    },
  );
}

/// generated route for
/// [_i12.DashboardPage]
class DashboardRoute extends _i55.PageRouteInfo<void> {
  const DashboardRoute({List<_i55.PageRouteInfo>? children})
      : super(
          DashboardRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i12.DashboardPage();
    },
  );
}

/// generated route for
/// [_i13.EditProfilePage]
class EditProfileRoute extends _i55.PageRouteInfo<EditProfileRouteArgs> {
  EditProfileRoute({
    _i56.Key? key,
    _i59.ProfileResponseEntity? profileData,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          EditProfileRoute.name,
          args: EditProfileRouteArgs(
            key: key,
            profileData: profileData,
          ),
          initialChildren: children,
        );

  static const String name = 'EditProfileRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<EditProfileRouteArgs>(
          orElse: () => const EditProfileRouteArgs());
      return _i13.EditProfilePage(
        key: args.key,
        profileData: args.profileData,
      );
    },
  );
}

class EditProfileRouteArgs {
  const EditProfileRouteArgs({
    this.key,
    this.profileData,
  });

  final _i56.Key? key;

  final _i59.ProfileResponseEntity? profileData;

  @override
  String toString() {
    return 'EditProfileRouteArgs{key: $key, profileData: $profileData}';
  }
}

/// generated route for
/// [_i14.EmulateUserPage]
class EmulateUserRoute extends _i55.PageRouteInfo<void> {
  const EmulateUserRoute({List<_i55.PageRouteInfo>? children})
      : super(
          EmulateUserRoute.name,
          initialChildren: children,
        );

  static const String name = 'EmulateUserRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i14.EmulateUserPage();
    },
  );
}

/// generated route for
/// [_i15.FQPDPage]
class FQPDRoute extends _i55.PageRouteInfo<FQPDRouteArgs> {
  FQPDRoute({
    _i56.Key? key,
    num? questionId,
    num? taskId,
    num? formId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          FQPDRoute.name,
          args: FQPDRouteArgs(
            key: key,
            questionId: questionId,
            taskId: taskId,
            formId: formId,
          ),
          initialChildren: children,
        );

  static const String name = 'FQPDRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<FQPDRouteArgs>(orElse: () => const FQPDRouteArgs());
      return _i15.FQPDPage(
        key: args.key,
        questionId: args.questionId,
        taskId: args.taskId,
        formId: args.formId,
      );
    },
  );
}

class FQPDRouteArgs {
  const FQPDRouteArgs({
    this.key,
    this.questionId,
    this.taskId,
    this.formId,
  });

  final _i56.Key? key;

  final num? questionId;

  final num? taskId;

  final num? formId;

  @override
  String toString() {
    return 'FQPDRouteArgs{key: $key, questionId: $questionId, taskId: $taskId, formId: $formId}';
  }
}

/// generated route for
/// [_i16.FlipPage]
class FlipRoute extends _i55.PageRouteInfo<FlipRouteArgs> {
  FlipRoute({
    _i56.Key? key,
    required num? questionId,
    num? taskId,
    num? formId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          FlipRoute.name,
          args: FlipRouteArgs(
            key: key,
            questionId: questionId,
            taskId: taskId,
            formId: formId,
          ),
          initialChildren: children,
        );

  static const String name = 'FlipRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<FlipRouteArgs>();
      return _i16.FlipPage(
        key: args.key,
        questionId: args.questionId,
        taskId: args.taskId,
        formId: args.formId,
      );
    },
  );
}

class FlipRouteArgs {
  const FlipRouteArgs({
    this.key,
    required this.questionId,
    this.taskId,
    this.formId,
  });

  final _i56.Key? key;

  final num? questionId;

  final num? taskId;

  final num? formId;

  @override
  String toString() {
    return 'FlipRouteArgs{key: $key, questionId: $questionId, taskId: $taskId, formId: $formId}';
  }
}

/// generated route for
/// [_i17.FormPage]
class FormRoute extends _i55.PageRouteInfo<FormRouteArgs> {
  FormRoute({
    _i56.Key? key,
    required int taskId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          FormRoute.name,
          args: FormRouteArgs(
            key: key,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'FormRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<FormRouteArgs>();
      return _i17.FormPage(
        key: args.key,
        taskId: args.taskId,
      );
    },
  );
}

class FormRouteArgs {
  const FormRouteArgs({
    this.key,
    required this.taskId,
  });

  final _i56.Key? key;

  final int taskId;

  @override
  String toString() {
    return 'FormRouteArgs{key: $key, taskId: $taskId}';
  }
}

/// generated route for
/// [_i18.HistoryPage]
class HistoryRoute extends _i55.PageRouteInfo<void> {
  const HistoryRoute({List<_i55.PageRouteInfo>? children})
      : super(
          HistoryRoute.name,
          initialChildren: children,
        );

  static const String name = 'HistoryRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i18.HistoryPage();
    },
  );
}

/// generated route for
/// [_i19.HomePage]
class HomeRoute extends _i55.PageRouteInfo<void> {
  const HomeRoute({List<_i55.PageRouteInfo>? children})
      : super(
          HomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i19.HomePage();
    },
  );
}

/// generated route for
/// [_i20.InductionPage]
class InductionRoute extends _i55.PageRouteInfo<void> {
  const InductionRoute({List<_i55.PageRouteInfo>? children})
      : super(
          InductionRoute.name,
          initialChildren: children,
        );

  static const String name = 'InductionRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i20.InductionPage();
    },
  );
}

/// generated route for
/// [_i21.JourneyMapPage]
class JourneyMapRoute extends _i55.PageRouteInfo<void> {
  const JourneyMapRoute({List<_i55.PageRouteInfo>? children})
      : super(
          JourneyMapRoute.name,
          initialChildren: children,
        );

  static const String name = 'JourneyMapRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i21.JourneyMapPage();
    },
  );
}

/// generated route for
/// [_i22.LeavePage]
class LeaveRoute extends _i55.PageRouteInfo<void> {
  const LeaveRoute({List<_i55.PageRouteInfo>? children})
      : super(
          LeaveRoute.name,
          initialChildren: children,
        );

  static const String name = 'LeaveRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i22.LeavePage();
    },
  );
}

/// generated route for
/// [_i23.LoginPage]
class LoginRoute extends _i55.PageRouteInfo<void> {
  const LoginRoute({List<_i55.PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i23.LoginPage();
    },
  );
}

/// generated route for
/// [_i24.MPTPage]
class MPTRoute extends _i55.PageRouteInfo<MPTRouteArgs> {
  MPTRoute({
    _i56.Key? key,
    String? taskId,
    String? formId,
    String? questionId,
    String? questionPartId,
    String? measurementId,
    String? combineTypeId,
    String? questionPartMultiId,
    List<String>? images,
    _i60.Question? question,
    int level = 2,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          MPTRoute.name,
          args: MPTRouteArgs(
            key: key,
            taskId: taskId,
            formId: formId,
            questionId: questionId,
            questionPartId: questionPartId,
            measurementId: measurementId,
            combineTypeId: combineTypeId,
            questionPartMultiId: questionPartMultiId,
            images: images,
            question: question,
            level: level,
          ),
          initialChildren: children,
        );

  static const String name = 'MPTRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<MPTRouteArgs>(orElse: () => const MPTRouteArgs());
      return _i24.MPTPage(
        key: args.key,
        taskId: args.taskId,
        formId: args.formId,
        questionId: args.questionId,
        questionPartId: args.questionPartId,
        measurementId: args.measurementId,
        combineTypeId: args.combineTypeId,
        questionPartMultiId: args.questionPartMultiId,
        images: args.images,
        question: args.question,
        level: args.level,
      );
    },
  );
}

class MPTRouteArgs {
  const MPTRouteArgs({
    this.key,
    this.taskId,
    this.formId,
    this.questionId,
    this.questionPartId,
    this.measurementId,
    this.combineTypeId,
    this.questionPartMultiId,
    this.images,
    this.question,
    this.level = 2,
  });

  final _i56.Key? key;

  final String? taskId;

  final String? formId;

  final String? questionId;

  final String? questionPartId;

  final String? measurementId;

  final String? combineTypeId;

  final String? questionPartMultiId;

  final List<String>? images;

  final _i60.Question? question;

  final int level;

  @override
  String toString() {
    return 'MPTRouteArgs{key: $key, taskId: $taskId, formId: $formId, questionId: $questionId, questionPartId: $questionPartId, measurementId: $measurementId, combineTypeId: $combineTypeId, questionPartMultiId: $questionPartMultiId, images: $images, question: $question, level: $level}';
  }
}

/// generated route for
/// [_i25.MoreHolderPage]
class MoreHolderRoute extends _i55.PageRouteInfo<void> {
  const MoreHolderRoute({List<_i55.PageRouteInfo>? children})
      : super(
          MoreHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreHolderRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i25.MoreHolderPage();
    },
  );
}

/// generated route for
/// [_i26.MorePage]
class MoreRoute extends _i55.PageRouteInfo<void> {
  const MoreRoute({List<_i55.PageRouteInfo>? children})
      : super(
          MoreRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i26.MorePage();
    },
  );
}

/// generated route for
/// [_i27.NotesPage]
class NotesRoute extends _i55.PageRouteInfo<NotesRouteArgs> {
  NotesRoute({
    _i56.Key? key,
    required _i60.TaskDetail task,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          NotesRoute.name,
          args: NotesRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'NotesRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<NotesRouteArgs>();
      return _i27.NotesPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class NotesRouteArgs {
  const NotesRouteArgs({
    this.key,
    required this.task,
  });

  final _i56.Key? key;

  final _i60.TaskDetail task;

  @override
  String toString() {
    return 'NotesRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i28.NotificationsPage]
class NotificationsRoute extends _i55.PageRouteInfo<void> {
  const NotificationsRoute({List<_i55.PageRouteInfo>? children})
      : super(
          NotificationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationsRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i28.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i29.OpenTasksPage]
class OpenTasksRoute extends _i55.PageRouteInfo<void> {
  const OpenTasksRoute({List<_i55.PageRouteInfo>? children})
      : super(
          OpenTasksRoute.name,
          initialChildren: children,
        );

  static const String name = 'OpenTasksRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i29.OpenTasksPage();
    },
  );
}

/// generated route for
/// [_i30.PosPage]
class PosRoute extends _i55.PageRouteInfo<PosRouteArgs> {
  PosRoute({
    _i56.Key? key,
    _i60.TaskDetail? task,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          PosRoute.name,
          args: PosRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'PosRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<PosRouteArgs>(orElse: () => const PosRouteArgs());
      return _i30.PosPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class PosRouteArgs {
  const PosRouteArgs({
    this.key,
    this.task,
  });

  final _i56.Key? key;

  final _i60.TaskDetail? task;

  @override
  String toString() {
    return 'PosRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i31.PreviousTaskFormPage]
class PreviousTaskFormRoute
    extends _i55.PageRouteInfo<PreviousTaskFormRouteArgs> {
  PreviousTaskFormRoute({
    _i56.Key? key,
    required dynamic form,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          PreviousTaskFormRoute.name,
          args: PreviousTaskFormRouteArgs(
            key: key,
            form: form,
          ),
          initialChildren: children,
        );

  static const String name = 'PreviousTaskFormRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PreviousTaskFormRouteArgs>();
      return _i31.PreviousTaskFormPage(
        key: args.key,
        form: args.form,
      );
    },
  );
}

class PreviousTaskFormRouteArgs {
  const PreviousTaskFormRouteArgs({
    this.key,
    required this.form,
  });

  final _i56.Key? key;

  final dynamic form;

  @override
  String toString() {
    return 'PreviousTaskFormRouteArgs{key: $key, form: $form}';
  }
}

/// generated route for
/// [_i32.ProfileHistoryPage]
class ProfileHistoryRoute extends _i55.PageRouteInfo<void> {
  const ProfileHistoryRoute({List<_i55.PageRouteInfo>? children})
      : super(
          ProfileHistoryRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileHistoryRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i32.ProfileHistoryPage();
    },
  );
}

/// generated route for
/// [_i33.ProfileHolderPage]
class ProfileHolderRoute extends _i55.PageRouteInfo<void> {
  const ProfileHolderRoute({List<_i55.PageRouteInfo>? children})
      : super(
          ProfileHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileHolderRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i33.ProfileHolderPage();
    },
  );
}

/// generated route for
/// [_i34.ProfilePage]
class ProfileRoute extends _i55.PageRouteInfo<void> {
  const ProfileRoute({List<_i55.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i34.ProfilePage();
    },
  );
}

/// generated route for
/// [_i35.QPMDPage]
class QPMDRoute extends _i55.PageRouteInfo<QPMDRouteArgs> {
  QPMDRoute({
    _i56.Key? key,
    num? questionId,
    num? questionpartId,
    num? taskId,
    num? formId,
    String? questionpartMultiId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          QPMDRoute.name,
          args: QPMDRouteArgs(
            key: key,
            questionId: questionId,
            questionpartId: questionpartId,
            taskId: taskId,
            formId: formId,
            questionpartMultiId: questionpartMultiId,
          ),
          initialChildren: children,
        );

  static const String name = 'QPMDRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<QPMDRouteArgs>(orElse: () => const QPMDRouteArgs());
      return _i35.QPMDPage(
        key: args.key,
        questionId: args.questionId,
        questionpartId: args.questionpartId,
        taskId: args.taskId,
        formId: args.formId,
        questionpartMultiId: args.questionpartMultiId,
      );
    },
  );
}

class QPMDRouteArgs {
  const QPMDRouteArgs({
    this.key,
    this.questionId,
    this.questionpartId,
    this.taskId,
    this.formId,
    this.questionpartMultiId,
  });

  final _i56.Key? key;

  final num? questionId;

  final num? questionpartId;

  final num? taskId;

  final num? formId;

  final String? questionpartMultiId;

  @override
  String toString() {
    return 'QPMDRouteArgs{key: $key, questionId: $questionId, questionpartId: $questionpartId, taskId: $taskId, formId: $formId, questionpartMultiId: $questionpartMultiId}';
  }
}

/// generated route for
/// [_i36.QuestionPage]
class QuestionRoute extends _i55.PageRouteInfo<QuestionRouteArgs> {
  QuestionRoute({
    _i56.Key? key,
    required num formId,
    num? taskId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          QuestionRoute.name,
          args: QuestionRouteArgs(
            key: key,
            formId: formId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'QuestionRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<QuestionRouteArgs>();
      return _i36.QuestionPage(
        key: args.key,
        formId: args.formId,
        taskId: args.taskId,
      );
    },
  );
}

class QuestionRouteArgs {
  const QuestionRouteArgs({
    this.key,
    required this.formId,
    this.taskId,
  });

  final _i56.Key? key;

  final num formId;

  final num? taskId;

  @override
  String toString() {
    return 'QuestionRouteArgs{key: $key, formId: $formId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i37.ResetPasswordPage]
class ResetPasswordRoute extends _i55.PageRouteInfo<ResetPasswordRouteArgs> {
  ResetPasswordRoute({
    _i56.Key? key,
    required String email,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          ResetPasswordRoute.name,
          args: ResetPasswordRouteArgs(
            key: key,
            email: email,
          ),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ResetPasswordRouteArgs>();
      return _i37.ResetPasswordPage(
        key: args.key,
        email: args.email,
      );
    },
  );
}

class ResetPasswordRouteArgs {
  const ResetPasswordRouteArgs({
    this.key,
    required this.email,
  });

  final _i56.Key? key;

  final String email;

  @override
  String toString() {
    return 'ResetPasswordRouteArgs{key: $key, email: $email}';
  }
}

/// generated route for
/// [_i38.SchedulePage]
class ScheduleRoute extends _i55.PageRouteInfo<void> {
  const ScheduleRoute({List<_i55.PageRouteInfo>? children})
      : super(
          ScheduleRoute.name,
          initialChildren: children,
        );

  static const String name = 'ScheduleRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i38.SchedulePage();
    },
  );
}

/// generated route for
/// [_i39.SignaturePage]
class SignatureRoute extends _i55.PageRouteInfo<SignatureRouteArgs> {
  SignatureRoute({
    _i56.Key? key,
    num? questionId,
    num? taskId,
    num? formId,
    String? title,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          SignatureRoute.name,
          args: SignatureRouteArgs(
            key: key,
            questionId: questionId,
            taskId: taskId,
            formId: formId,
            title: title,
          ),
          initialChildren: children,
        );

  static const String name = 'SignatureRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SignatureRouteArgs>(
          orElse: () => const SignatureRouteArgs());
      return _i39.SignaturePage(
        key: args.key,
        questionId: args.questionId,
        taskId: args.taskId,
        formId: args.formId,
        title: args.title,
      );
    },
  );
}

class SignatureRouteArgs {
  const SignatureRouteArgs({
    this.key,
    this.questionId,
    this.taskId,
    this.formId,
    this.title,
  });

  final _i56.Key? key;

  final num? questionId;

  final num? taskId;

  final num? formId;

  final String? title;

  @override
  String toString() {
    return 'SignatureRouteArgs{key: $key, questionId: $questionId, taskId: $taskId, formId: $formId, title: $title}';
  }
}

/// generated route for
/// [_i40.SkillsPage]
class SkillsRoute extends _i55.PageRouteInfo<void> {
  const SkillsRoute({List<_i55.PageRouteInfo>? children})
      : super(
          SkillsRoute.name,
          initialChildren: children,
        );

  static const String name = 'SkillsRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i40.SkillsPage();
    },
  );
}

/// generated route for
/// [_i41.SplashPage]
class SplashRoute extends _i55.PageRouteInfo<void> {
  const SplashRoute({List<_i55.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i41.SplashPage();
    },
  );
}

/// generated route for
/// [_i42.StoreHistoryItemsPage]
class StoreHistoryItemsRoute
    extends _i55.PageRouteInfo<StoreHistoryItemsRouteArgs> {
  StoreHistoryItemsRoute({
    _i56.Key? key,
    required int storeId,
    required int taskId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          StoreHistoryItemsRoute.name,
          args: StoreHistoryItemsRouteArgs(
            key: key,
            storeId: storeId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'StoreHistoryItemsRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoreHistoryItemsRouteArgs>();
      return _i42.StoreHistoryItemsPage(
        key: args.key,
        storeId: args.storeId,
        taskId: args.taskId,
      );
    },
  );
}

class StoreHistoryItemsRouteArgs {
  const StoreHistoryItemsRouteArgs({
    this.key,
    required this.storeId,
    required this.taskId,
  });

  final _i56.Key? key;

  final int storeId;

  final int taskId;

  @override
  String toString() {
    return 'StoreHistoryItemsRouteArgs{key: $key, storeId: $storeId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i43.StoreHistoryPage]
class StoreHistoryRoute extends _i55.PageRouteInfo<StoreHistoryRouteArgs> {
  StoreHistoryRoute({
    _i56.Key? key,
    required int storeId,
    required int taskId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          StoreHistoryRoute.name,
          args: StoreHistoryRouteArgs(
            key: key,
            storeId: storeId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'StoreHistoryRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoreHistoryRouteArgs>();
      return _i43.StoreHistoryPage(
        key: args.key,
        storeId: args.storeId,
        taskId: args.taskId,
      );
    },
  );
}

class StoreHistoryRouteArgs {
  const StoreHistoryRouteArgs({
    this.key,
    required this.storeId,
    required this.taskId,
  });

  final _i56.Key? key;

  final int storeId;

  final int taskId;

  @override
  String toString() {
    return 'StoreHistoryRouteArgs{key: $key, storeId: $storeId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i44.StoreInfoPage]
class StoreInfoRoute extends _i55.PageRouteInfo<StoreInfoRouteArgs> {
  StoreInfoRoute({
    _i56.Key? key,
    required String storeId,
    required String taskId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          StoreInfoRoute.name,
          args: StoreInfoRouteArgs(
            key: key,
            storeId: storeId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'StoreInfoRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoreInfoRouteArgs>();
      return _i44.StoreInfoPage(
        key: args.key,
        storeId: args.storeId,
        taskId: args.taskId,
      );
    },
  );
}

class StoreInfoRouteArgs {
  const StoreInfoRouteArgs({
    this.key,
    required this.storeId,
    required this.taskId,
  });

  final _i56.Key? key;

  final String storeId;

  final String taskId;

  @override
  String toString() {
    return 'StoreInfoRouteArgs{key: $key, storeId: $storeId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i45.SubHeaderPage]
class SubHeaderRoute extends _i55.PageRouteInfo<SubHeaderRouteArgs> {
  SubHeaderRoute({
    _i56.Key? key,
    required String title,
    num? questionId,
    num? taskId,
    num? formId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          SubHeaderRoute.name,
          args: SubHeaderRouteArgs(
            key: key,
            title: title,
            questionId: questionId,
            taskId: taskId,
            formId: formId,
          ),
          initialChildren: children,
        );

  static const String name = 'SubHeaderRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SubHeaderRouteArgs>();
      return _i45.SubHeaderPage(
        key: args.key,
        title: args.title,
        questionId: args.questionId,
        taskId: args.taskId,
        formId: args.formId,
      );
    },
  );
}

class SubHeaderRouteArgs {
  const SubHeaderRouteArgs({
    this.key,
    required this.title,
    this.questionId,
    this.taskId,
    this.formId,
  });

  final _i56.Key? key;

  final String title;

  final num? questionId;

  final num? taskId;

  final num? formId;

  @override
  String toString() {
    return 'SubHeaderRouteArgs{key: $key, title: $title, questionId: $questionId, taskId: $taskId, formId: $formId}';
  }
}

/// generated route for
/// [_i46.TaskDetailsPage]
class TaskDetailsRoute extends _i55.PageRouteInfo<TaskDetailsRouteArgs> {
  TaskDetailsRoute({
    _i56.Key? key,
    required int taskId,
    bool openBrief = false,
    bool openDocuments = false,
    required num storeId,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          TaskDetailsRoute.name,
          args: TaskDetailsRouteArgs(
            key: key,
            taskId: taskId,
            openBrief: openBrief,
            openDocuments: openDocuments,
            storeId: storeId,
          ),
          initialChildren: children,
        );

  static const String name = 'TaskDetailsRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TaskDetailsRouteArgs>();
      return _i46.TaskDetailsPage(
        key: args.key,
        taskId: args.taskId,
        openBrief: args.openBrief,
        openDocuments: args.openDocuments,
        storeId: args.storeId,
      );
    },
  );
}

class TaskDetailsRouteArgs {
  const TaskDetailsRouteArgs({
    this.key,
    required this.taskId,
    this.openBrief = false,
    this.openDocuments = false,
    required this.storeId,
  });

  final _i56.Key? key;

  final int taskId;

  final bool openBrief;

  final bool openDocuments;

  final num storeId;

  @override
  String toString() {
    return 'TaskDetailsRouteArgs{key: $key, taskId: $taskId, openBrief: $openBrief, openDocuments: $openDocuments, storeId: $storeId}';
  }
}

/// generated route for
/// [_i47.TaskFilesPage]
class TaskFilesRoute extends _i55.PageRouteInfo<TaskFilesRouteArgs> {
  TaskFilesRoute({
    _i56.Key? key,
    required int taskId,
    required num storeId,
    required String sectionType,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          TaskFilesRoute.name,
          args: TaskFilesRouteArgs(
            key: key,
            taskId: taskId,
            storeId: storeId,
            sectionType: sectionType,
          ),
          initialChildren: children,
        );

  static const String name = 'TaskFilesRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TaskFilesRouteArgs>();
      return _i47.TaskFilesPage(
        key: args.key,
        taskId: args.taskId,
        storeId: args.storeId,
        sectionType: args.sectionType,
      );
    },
  );
}

class TaskFilesRouteArgs {
  const TaskFilesRouteArgs({
    this.key,
    required this.taskId,
    required this.storeId,
    required this.sectionType,
  });

  final _i56.Key? key;

  final int taskId;

  final num storeId;

  final String sectionType;

  @override
  String toString() {
    return 'TaskFilesRouteArgs{key: $key, taskId: $taskId, storeId: $storeId, sectionType: $sectionType}';
  }
}

/// generated route for
/// [_i48.TodayPage]
class TodayRoute extends _i55.PageRouteInfo<void> {
  const TodayRoute({List<_i55.PageRouteInfo>? children})
      : super(
          TodayRoute.name,
          initialChildren: children,
        );

  static const String name = 'TodayRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i48.TodayPage();
    },
  );
}

/// generated route for
/// [_i49.TutorialPage]
class TutorialRoute extends _i55.PageRouteInfo<void> {
  const TutorialRoute({List<_i55.PageRouteInfo>? children})
      : super(
          TutorialRoute.name,
          initialChildren: children,
        );

  static const String name = 'TutorialRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i49.TutorialPage();
    },
  );
}

/// generated route for
/// [_i50.UnscheduledPage]
class UnscheduledRoute extends _i55.PageRouteInfo<void> {
  const UnscheduledRoute({List<_i55.PageRouteInfo>? children})
      : super(
          UnscheduledRoute.name,
          initialChildren: children,
        );

  static const String name = 'UnscheduledRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i50.UnscheduledPage();
    },
  );
}

/// generated route for
/// [_i51.UnscheduledPosTasksPage]
class UnscheduledPosTasksRoute extends _i55.PageRouteInfo<void> {
  const UnscheduledPosTasksRoute({List<_i55.PageRouteInfo>? children})
      : super(
          UnscheduledPosTasksRoute.name,
          initialChildren: children,
        );

  static const String name = 'UnscheduledPosTasksRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i51.UnscheduledPosTasksPage();
    },
  );
}

/// generated route for
/// [_i52.UsefulLinksPage]
class UsefulLinksRoute extends _i55.PageRouteInfo<void> {
  const UsefulLinksRoute({List<_i55.PageRouteInfo>? children})
      : super(
          UsefulLinksRoute.name,
          initialChildren: children,
        );

  static const String name = 'UsefulLinksRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i52.UsefulLinksPage();
    },
  );
}

/// generated route for
/// [_i53.VacanciesPage]
class VacanciesRoute extends _i55.PageRouteInfo<void> {
  const VacanciesRoute({List<_i55.PageRouteInfo>? children})
      : super(
          VacanciesRoute.name,
          initialChildren: children,
        );

  static const String name = 'VacanciesRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      return const _i53.VacanciesPage();
    },
  );
}

/// generated route for
/// [_i54.WebBrowserPage]
class WebBrowserRoute extends _i55.PageRouteInfo<WebBrowserRouteArgs> {
  WebBrowserRoute({
    _i56.Key? key,
    required String url,
    String? title,
    List<_i55.PageRouteInfo>? children,
  }) : super(
          WebBrowserRoute.name,
          args: WebBrowserRouteArgs(
            key: key,
            url: url,
            title: title,
          ),
          initialChildren: children,
        );

  static const String name = 'WebBrowserRoute';

  static _i55.PageInfo page = _i55.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<WebBrowserRouteArgs>();
      return _i54.WebBrowserPage(
        key: args.key,
        url: args.url,
        title: args.title,
      );
    },
  );
}

class WebBrowserRouteArgs {
  const WebBrowserRouteArgs({
    this.key,
    required this.url,
    this.title,
  });

  final _i56.Key? key;

  final String url;

  final String? title;

  @override
  String toString() {
    return 'WebBrowserRouteArgs{key: $key, url: $url, title: $title}';
  }
}
