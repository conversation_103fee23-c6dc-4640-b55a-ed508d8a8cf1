# CLAUDE.md

This file provides guidance to <PERSON> when working with the StoreTrack Flutter application.

## App Overview

**StoreTrack** is an enterprise Flutter application for retail field workforce management. Field workers complete store audits, manage tasks, capture photos/signatures, and sync data offline-first.

**Primary Workflow**: Authentication → Task Download → Task Completion → Data Sync → Report Submission

## Quick Reference

### Most Common Commands
- `flutter pub get` - Install dependencies
- `flutter run` - Start app with connected device
- `flutter analyze` - Check for issues (run after every change)
- `dart run build_runner build` - Generate code after model changes

### Critical Paths
- Dependencies: `lib/di/service_locator.dart`
- Routes: `lib/config/routes/app_router.dart`
- Database: `lib/core/database/realm_database.dart`
- API Client: `lib/core/network/api_client.dart`

### Common Patterns
- All new services → register in `service_locator.dart`
- All states → use `Result<T>` pattern for errors
- All models → run build_runner after Realm changes
- All forms → follow QPMD system patterns

## Quick Start

- **Setup**: `flutter pub get` → `dart run build_runner build`
- **Run**: `flutter run` (with device connected)
- **Test Changes**: `flutter analyze` → fix warnings
- **Generate Code**: Run build_runner after model changes

## Development Commands

```bash
flutter pub get                         # Install dependencies
flutter run                            # Run app
flutter analyze                        # Static analysis
flutter test                           # Run tests
dart run build_runner build            # Generate code (routes, realm models)
dart run build_runner build --delete-conflicting-outputs  # Force regenerate
```

## Architecture

### Clean Architecture with BLoC Pattern
```
lib/
├── core/              # Database, network, services, utilities
├── config/            # Routes, themes, app configuration
├── features/          # Feature modules (auth, home, etc.)
│   └── [feature]/
│       ├── data/         # Models, repositories, datasources
│       ├── domain/       # Entities, use cases, validators
│       └── presentation/ # Pages, widgets, cubits/blocs
├── shared/            # Shared widgets, models, utilities
├── di/               # Dependency injection (GetIt)
└── main.dart         # App entry point
```

### Critical Files
- `lib/di/service_locator.dart` - All dependency registration
- `lib/config/routes/app_router.dart` - Navigation configuration
- `lib/core/database/realm_database.dart` - Database schema
- `lib/core/network/api_client.dart` - HTTP client with interceptors
- `lib/main.dart` - App entry with 18 BlocProviders

## API Integration & Caching

**Base URL**: `https://webservice2.storetrack.com.au/api`

**Key Endpoints**:
- `POST /auth_storetrackNZ` - Authentication
- `POST /tasks_optimize` - Get task list
- `POST /submit_report_v4_11` - Submit reports
- `POST /send_task_pic_v4_11` - Upload photos
- `POST /send_task_sig_v4_11` - Upload signatures
- `POST /sync_pic_info_mpt` - Sync photo metadata
- `POST /sync_sig_info` - Sync signature metadata

### Local Caching Pattern

For APIs that need local caching:

- **Create Entity class** with fromJson/toJson methods in `domain/entities/`
- **Create Model class** with Realm annotations in `data/models/`
- **Run build_runner** to generate Realm models: `dart run build_runner build`
- **Create Mapper class** with toEntity/toModel methods in `data/mappers/`

```dart
// Example Entity
class UserEntity {
  final String id;
  final String name;
  
  const UserEntity({required this.id, required this.name});
  
  factory UserEntity.fromJson(Map<String, dynamic> json) => UserEntity(
    id: json['id'],
    name: json['name'],
  );
  
  Map<String, dynamic> toJson() => {'id': id, 'name': name};
}

// Example Model
@RealmModel()
class _UserModel {
  @PrimaryKey()
  late String id;
  late String name;
}

// Example Mapper
class UserMapper {
  static UserEntity toEntity(UserModel model) => UserEntity(
    id: model.id,
    name: model.name,
  );
  
  static UserModel toModel(UserEntity entity) => UserModel()
    ..id = entity.id
    ..name = entity.name;
}
```

## Data Architecture

### Database (Realm) - Schema Version 8
```
TaskDetailModel (Primary Entity)
├── PhotoFolderModel (1:N) → PhotoModel (1:N)
├── SignatureFolderModel (1:N) → SignatureModel (1:N)
├── FormModel (1:N) → QuestionModel (1:N) → MeasurementModel (1:N)
├── PosItemModel (1:N)
├── DocumentModel (1:N)
└── TaskmemberModel (1:N)
```

## Form Processing (QPMD System)

**QPMD = Question Part Measurement Data**

### Form Widget Types
- **Text Fields** (Types 1-2): Basic text input
- **Checkboxes** (Type 3): Boolean selections
- **Dropdowns** (Types 4-5): Single selection
- **Multi-select** (Type 6): Multiple selections
- **Counter** (Type 7): Numeric input
- **Radio Buttons** (Type 8): Single choice
- **Date Picker** (Type 9): Date selection

### Key Features
- **Auto-save**: Continuous form progress saving
- **Real-time Validation**: Field validation with custom messages
- **Quiz Mode**: Answer validation for quiz forms
- **Conditional Logic**: Show/hide fields based on selections

## Sync System

### 4-Phase Sync Process
- **Photo Upload & Sync**: Individual photo upload → metadata sync → local cleanup
- **Signature Upload & Sync**: Similar to photos with duplicate detection
- **Report Submission**: Batch task data submission
- **Task & Calendar Sync**: Download latest tasks and calendar updates

### Sync Service
```dart
class SyncService {
  Future<void> syncPhotos() // Phase 1
  Future<void> syncSignatures() // Phase 2
  Future<void> submitReports() // Phase 3
  Future<void> syncTasks() // Phase 4
}
```

## State Management

### BLoC Pattern Implementation
```dart
// Service registration in service_locator.dart
sl.registerLazySingleton<CubitName>(() => CubitImpl(...));

// Usage in main.dart
MultiBlocProvider(
  providers: [
    BlocProvider(create: (_) => sl<AuthCubit>()),
    BlocProvider(create: (_) => sl<DashboardCubit>()),
    // ... 18 total providers
  ],
  child: MaterialApp.router(...)
)
```

### State Pattern
- **States**: Initial, Loading, Success, Error with Equatable
- **Cubits**: Business logic with emit() for state changes
- **Use Cases**: Single responsibility business operations

## Core Services

### Essential Services (Registered in service_locator.dart)
```dart
PhotoService         # Photo management with local/remote sync
SyncService          # Background synchronization
LocationService      # GPS and location tracking
CameraService        # Camera integration
BarcodeScannerService # QR/barcode scanning
RealmDatabase        # Local database instance
ApiClient            # HTTP client with interceptors
```

## Error Handling

### Result Pattern
```dart
abstract class Result<T> {
  const Result();
}

class Success<T> extends Result<T> {
  final T data;
  const Success(this.data);
}

class Failure<T> extends Result<T> {
  final String message;
  const Failure(this.message);
}
```

## Development Guidelines

### Code Organization
- **Reuse First**: Check existing Cubits, Use Cases, widgets before creating new
- **Follow DI**: Register all dependencies in `service_locator.dart`
- **Result Pattern**: Use `Result<T>` for error handling
- **Immutable States**: Keep state classes immutable with Equatable
- **Code Generation**: Run build_runner after model changes
- **DRY Principle**: Avoid code duplication - extract common functionality into reusable components
- **KISS Principle**: Keep solutions simple and straightforward - prefer clarity over cleverness

### Naming Conventions
- Pages: `*_page.dart`
- Widgets: `*_widget.dart`
- Models: `*_model.dart`, `*_response.dart`, `*_request.dart`
- Entities: `*_entity.dart`
- Use Cases: `*_usecase.dart`
- Cubits: `*_cubit.dart` + `*_state.dart`

### Key Development Patterns
```dart
// Repository pattern
abstract class Repository {
  Future<Result<T>> getData();
}

// Use case pattern
class GetDataUseCase {
  final Repository repository;
  GetDataUseCase(this.repository);
  
  Future<Result<T>> call() => repository.getData();
}

// Cubit pattern
class DataCubit extends Cubit<DataState> {
  final GetDataUseCase useCase;
  DataCubit(this.useCase) : super(DataInitial());
  
  Future<void> loadData() async {
    emit(DataLoading());
    final result = await useCase();
    result.fold(
      (failure) => emit(DataError(failure.message)),
      (data) => emit(DataLoaded(data)),
    );
  }
}
```

## Navigation

### Auto Route Configuration
```dart
@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  // 60+ routes defined
}
```

### Navigation Patterns
```dart
// Push route
context.router.push(RouteNameRoute());

// Replace route
context.router.pushAndClearStack(RouteNameRoute());

// Pop route
context.router.pop();
```

## UI Guidelines

- **Balance**: Not too minimalistic, not too modern
- **Consistency**: Follow app's established look and feel
- **Reuse**: Check existing widgets before creating new ones

## Critical Implementation Notes

### Realm Database
- **Schema Version**: Currently 8, increment on changes
- **Migration**: Automatic schema migrations supported
- **Relationships**: Complex nested object relationships

### Photo Management
- **Local Storage**: Photos stored in device filesystem
- **Metadata**: Photo models stored in Realm
- **Sync Status**: Track local, uploaded, and synced states
- **Cleanup**: Remove local files after successful sync

### Form System
- **Dynamic Forms**: Forms generated from server configuration
- **Validation**: Real-time validation with custom messages
- **Auto-save**: Continuous form progress saving
- **Conditional Logic**: Complex field dependencies

### Sync System
- **Sequential Sync**: Photos → Signatures → Reports → Tasks
- **Retry Logic**: Automatic retry on failures
- **Status Tracking**: Visual sync indicators

## Quality Assurance & Common Pitfalls

### Quality Checklist
- Run `flutter analyze` after every change and fix all warnings/errors
- Test changes on device before committing
- Verify build_runner generates correctly after model changes

### Common Pitfalls
- **Realm Schema Changes**: Always increment schema version and run `dart run build_runner build --delete-conflicting-outputs`
- **Missing Dependencies**: Register new services in `service_locator.dart` before using
- **Sync Conflicts**: Check task status before modifying - synced tasks are read-only
- **Photo/Signature Upload**: Ensure proper file paths and sync status tracking
- **Form Validation**: Use existing validation patterns, don't bypass QPMD system

## Debug Commands

- `flutter logs` - View device logs
- `flutter analyze` - Static analysis  
- `dart run build_runner build --delete-conflicting-outputs` - Fix build issues
- `flutter clean && flutter pub get` - Reset build cache

This guide provides essential information for effective development in the StoreTrack codebase.