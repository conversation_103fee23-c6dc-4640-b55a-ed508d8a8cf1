name: storetrack_app
description: "Flutter application for Storetrack"

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.4.1 <4.0.0'

dependencies:
  auto_route: ^9.0.0
  bloc: ^8.1.4
  cached_network_image: ^3.4.1
  cupertino_icons: ^1.0.6
  dart_jsonwebtoken: ^2.17.0
  device_info_plus: ^10.1.2
  dio: ^5.7.0
  dio_smart_retry: ^6.0.0
  easy_debounce: ^2.0.3
  equatable: ^2.0.5
  firebase_core: ^3.15.2
  firebase_messaging: ^15.2.10
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  flutter_html: ^3.0.0
  flutter_local_notifications: ^19.3.1
  flutter_native_splash: ^2.4.1
  flutter_polyline_points: ^2.1.0
  flutter_svg: ^2.0.9
  gap: ^3.0.1
  get_it: ^8.0.0
  google_maps_flutter: ^2.10.1
  icons_launcher: ^3.0.0
  image_picker: ^1.1.2
  internet_connection_checker_plus: ^2.5.2
  intl: ^0.19.0
  location: ^6.0.2
  mobile_scanner: ^7.0.1
  msal_auth: ^3.1.5
  package_info_plus: ^8.3.0
  path: ^1.9.0
  path_provider: ^2.1.5
  permission_handler: ^12.0.0+1
  pretty_dio_logger: ^1.4.0
  realm: ^20.0.1
  rename: ^3.0.2
  shared_preferences: ^2.2.2
  signature: ^6.3.0
  table_calendar: ^3.0.9
  timezone: ^0.10.1
  url_launcher: ^6.3.1
  webview_flutter: ^4.10.0

dev_dependencies:
  auto_route_generator: ^9.0.0
  build_runner: null
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter
  realm_common: ^20.1.1

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/fonts/
    - assets/jsons/
    - assets/icons/

  fonts:
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat-Bold.ttf
          weight: 700
        - asset: assets/fonts/Montserrat-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Montserrat-Medium.ttf
          weight: 500
        - asset: assets/fonts/Montserrat-Regular.ttf
          weight: 400
        - asset: assets/fonts/Montserrat-Light.ttf
          weight: 300

# flutter_native_splash:
#   color: "#FF584F"
#   image: assets/images/g4l_icon.png
#   android_12:
#     color: "#FF584F"
#     image: assets/images/g4l_icon.png
# icons_launcher:
#   image_path: "assets/images/icon.png"
#   platforms:
#     android:
#       enable: true
#     ios:
#       enable: true
